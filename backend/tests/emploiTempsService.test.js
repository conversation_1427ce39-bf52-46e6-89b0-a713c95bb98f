/**
 * Tests unitaires pour EmploiTempsService
 * NS School Manager - Emploi du Temps Tests
 */

const EmploiTempsService = require('../services/EmploiTempsService');
const { query } = require('../models/db');

// Mock de la base de données
jest.mock('../models/db');

describe('EmploiTempsService', () => {
  let service;

  beforeEach(() => {
    service = new EmploiTempsService();
    jest.clearAllMocks();
  });

  describe('obtenirClasses', () => {
    it('devrait retourner toutes les classes', async () => {
      const mockClasses = [
        { nom_classe: '6eme A', niveau: '6eme', annee_scolaire: '2024-2025' },
        { nom_classe: '5eme A', niveau: '5eme', annee_scolaire: '2024-2025' }
      ];

      query.mockResolvedValue(mockClasses);

      const result = await service.obtenirClasses();

      expect(query).toHaveBeenCalledWith(
        'SELECT nom_classe, niveau, annee_scolaire FROM classe ORDER BY niveau, nom_classe'
      );
      expect(result).toEqual(mockClasses);
    });

    it('devrait gérer les erreurs de base de données', async () => {
      query.mockRejectedValue(new Error('Erreur DB'));

      await expect(service.obtenirClasses()).rejects.toThrow('Erreur lors de la récupération des classes');
    });
  });

  describe('obtenirMatieres', () => {
    it('devrait retourner toutes les matières sans filtre', async () => {
      const mockMatieres = [
        { matiere_id: 1, nom: 'Mathématiques', nom_classe: '6eme A', professeur: 'Jean Dupont' }
      ];

      query.mockResolvedValue(mockMatieres);

      const result = await service.obtenirMatieres();

      expect(query).toHaveBeenCalledWith(expect.stringContaining('SELECT'));
      expect(result).toEqual(mockMatieres);
    });

    it('devrait filtrer par classe', async () => {
      const mockMatieres = [
        { matiere_id: 1, nom: 'Mathématiques', nom_classe: '6eme A', professeur: 'Jean Dupont' }
      ];

      query.mockResolvedValue(mockMatieres);

      const result = await service.obtenirMatieres('6eme A');

      expect(query).toHaveBeenCalledWith(expect.stringContaining('WHERE m.nom_classe = ?'), ['6eme A']);
      expect(result).toEqual(mockMatieres);
    });
  });

  describe('obtenirEmploiTempsClasse', () => {
    it('devrait retourner l\'emploi du temps d\'une classe', async () => {
      const mockEmploiTemps = [
        {
          id: 1,
          jour_semaine: 'Lundi',
          heure_debut: '08:00:00',
          heure_fin: '09:00:00',
          matiere: 'Mathématiques',
          salle: 'Salle A1',
          professeur: 'Jean Dupont'
        }
      ];

      query.mockResolvedValue(mockEmploiTemps);

      const result = await service.obtenirEmploiTempsClasse('6eme A');

      expect(query).toHaveBeenCalledWith(expect.stringContaining('WHERE et.nom_classe = ?'), ['6eme A']);
      expect(result).toEqual(mockEmploiTemps);
    });

    it('devrait retourner un tableau vide pour une classe sans emploi du temps', async () => {
      query.mockResolvedValue([]);

      const result = await service.obtenirEmploiTempsClasse('Classe Inexistante');

      expect(result).toEqual([]);
    });
  });

  describe('verifierConflits', () => {
    const donneesCreneau = {
      nom_classe: '6eme A',
      matiere_id: 1,
      jour_semaine: 'Lundi',
      heure_debut: '08:00',
      heure_fin: '09:00',
      salle: 'Salle A1'
    };

    it('devrait détecter un conflit de classe', async () => {
      const mockConflits = [
        { type: 'classe', details: 'Conflit détecté' }
      ];

      query.mockResolvedValue(mockConflits);

      const result = await service.verifierConflits(donneesCreneau);

      expect(result.aConflits).toBe(true);
      expect(result.conflits).toEqual(mockConflits);
    });

    it('devrait détecter un conflit de professeur', async () => {
      query.mockResolvedValueOnce([]); // Pas de conflit de classe
      query.mockResolvedValueOnce([{ type: 'professeur' }]); // Conflit de professeur

      const result = await service.verifierConflits(donneesCreneau);

      expect(result.aConflits).toBe(true);
      expect(result.conflits).toHaveLength(1);
    });

    it('devrait détecter un conflit de salle', async () => {
      query.mockResolvedValueOnce([]); // Pas de conflit de classe
      query.mockResolvedValueOnce([]); // Pas de conflit de professeur
      query.mockResolvedValueOnce([{ type: 'salle' }]); // Conflit de salle

      const result = await service.verifierConflits(donneesCreneau);

      expect(result.aConflits).toBe(true);
      expect(result.conflits).toHaveLength(1);
    });

    it('ne devrait pas détecter de conflit', async () => {
      query.mockResolvedValue([]); // Aucun conflit

      const result = await service.verifierConflits(donneesCreneau);

      expect(result.aConflits).toBe(false);
      expect(result.conflits).toEqual([]);
    });
  });

  describe('ajouterCreneau', () => {
    const donneesCreneau = {
      nom_classe: '6eme A',
      matiere_id: 1,
      jour_semaine: 'Lundi',
      heure_debut: '08:00',
      heure_fin: '09:00',
      salle: 'Salle A1'
    };

    it('devrait ajouter un créneau sans conflit', async () => {
      // Mock pour vérification des conflits (aucun conflit)
      query.mockResolvedValue([]);
      
      // Mock pour l'insertion
      query.mockResolvedValueOnce({ insertId: 1 });

      const result = await service.ajouterCreneau(donneesCreneau);

      expect(result.success).toBe(true);
      expect(result.id).toBe(1);
      expect(result.message).toContain('ajouté avec succès');
    });

    it('devrait rejeter l\'ajout en cas de conflit', async () => {
      // Mock pour vérification des conflits (conflit détecté)
      query.mockResolvedValue([{ type: 'classe' }]);

      await expect(service.ajouterCreneau(donneesCreneau)).rejects.toThrow('Conflit détecté');
    });

    it('devrait valider les données requises', async () => {
      const donneesIncompletes = {
        nom_classe: '6eme A'
        // Données manquantes
      };

      await expect(service.ajouterCreneau(donneesIncompletes)).rejects.toThrow('Données manquantes');
    });
  });

  describe('modifierCreneau', () => {
    const donneesCreneau = {
      nom_classe: '6eme A',
      matiere_id: 1,
      jour_semaine: 'Lundi',
      heure_debut: '08:00',
      heure_fin: '09:00',
      salle: 'Salle A1'
    };

    it('devrait modifier un créneau existant', async () => {
      // Mock pour vérification d'existence
      query.mockResolvedValueOnce([{ id: 1 }]);
      
      // Mock pour vérification des conflits (aucun conflit)
      query.mockResolvedValue([]);
      
      // Mock pour la mise à jour
      query.mockResolvedValueOnce({ affectedRows: 1 });

      const result = await service.modifierCreneau(1, donneesCreneau);

      expect(result.success).toBe(true);
      expect(result.message).toContain('modifié avec succès');
    });

    it('devrait rejeter la modification d\'un créneau inexistant', async () => {
      query.mockResolvedValue([]);

      await expect(service.modifierCreneau(999, donneesCreneau)).rejects.toThrow('Créneau non trouvé');
    });
  });

  describe('supprimerCreneau', () => {
    it('devrait supprimer un créneau existant', async () => {
      // Mock pour vérification d'existence
      query.mockResolvedValueOnce([{ id: 1 }]);
      
      // Mock pour la suppression
      query.mockResolvedValueOnce({ affectedRows: 1 });

      const result = await service.supprimerCreneau(1);

      expect(result.success).toBe(true);
      expect(result.message).toContain('supprimé avec succès');
    });

    it('devrait rejeter la suppression d\'un créneau inexistant', async () => {
      query.mockResolvedValue([]);

      await expect(service.supprimerCreneau(999)).rejects.toThrow('Créneau non trouvé');
    });
  });

  describe('obtenirStatistiques', () => {
    it('devrait retourner les statistiques complètes', async () => {
      // Mock pour différentes requêtes de statistiques
      query.mockResolvedValueOnce([{ total: 50 }]); // Total créneaux
      query.mockResolvedValueOnce([{ total: 5 }]);  // Total classes
      query.mockResolvedValueOnce([{ total: 8 }]);  // Total matières
      query.mockResolvedValueOnce([{ total: 4 }]);  // Total professeurs
      query.mockResolvedValueOnce([
        { jour_semaine: 'Lundi', nombre: 12 },
        { jour_semaine: 'Mardi', nombre: 10 }
      ]); // Répartition par jour

      const result = await service.obtenirStatistiques();

      expect(result).toHaveProperty('totalCreneaux', 50);
      expect(result).toHaveProperty('totalClasses', 5);
      expect(result).toHaveProperty('totalMatieres', 8);
      expect(result).toHaveProperty('totalProfesseurs', 4);
      expect(result).toHaveProperty('repartitionParJour');
      expect(result.repartitionParJour).toHaveLength(2);
    });
  });

  describe('validerDonneesCreneau', () => {
    it('devrait valider des données correctes', () => {
      const donneesValides = {
        nom_classe: '6eme A',
        matiere_id: 1,
        jour_semaine: 'Lundi',
        heure_debut: '08:00',
        heure_fin: '09:00',
        salle: 'Salle A1'
      };

      const result = service.validerDonneesCreneau(donneesValides);

      expect(result.valide).toBe(true);
      expect(result.erreurs).toEqual([]);
    });

    it('devrait détecter les champs manquants', () => {
      const donneesInvalides = {
        nom_classe: '6eme A'
        // Champs manquants
      };

      const result = service.validerDonneesCreneau(donneesInvalides);

      expect(result.valide).toBe(false);
      expect(result.erreurs.length).toBeGreaterThan(0);
    });

    it('devrait valider le format des heures', () => {
      const donneesInvalides = {
        nom_classe: '6eme A',
        matiere_id: 1,
        jour_semaine: 'Lundi',
        heure_debut: '25:00', // Heure invalide
        heure_fin: '09:00',
        salle: 'Salle A1'
      };

      const result = service.validerDonneesCreneau(donneesInvalides);

      expect(result.valide).toBe(false);
      expect(result.erreurs).toContain('Format d\'heure de début invalide');
    });

    it('devrait vérifier que l\'heure de fin est après l\'heure de début', () => {
      const donneesInvalides = {
        nom_classe: '6eme A',
        matiere_id: 1,
        jour_semaine: 'Lundi',
        heure_debut: '10:00',
        heure_fin: '09:00', // Heure de fin avant heure de début
        salle: 'Salle A1'
      };

      const result = service.validerDonneesCreneau(donneesInvalides);

      expect(result.valide).toBe(false);
      expect(result.erreurs).toContain('L\'heure de fin doit être après l\'heure de début');
    });
  });
});
