/**
 * JWT Utility Module for NS School Manager
 * Handles JWT token generation, validation, and management
 */

const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('./logger');

/**
 * Generate JWT token for user
 * @param {Object} user - User object with matricule and role
 * @param {boolean} rememberMe - Whether to extend token expiry
 * @returns {string} JWT token
 */
function generateToken(user, rememberMe = false) {
  try {
    const payload = {
      matricule: user.matricule,
      role: user.role,
      type: 'access'
    };

    const options = {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
      expiresIn: rememberMe ? '30d' : config.jwt.expiresIn
    };

    return jwt.sign(payload, config.jwt.secret, options);
  } catch (error) {
    logger.error('Error generating JWT token', error);
    throw new Error('Token generation failed');
  }
}

/**
 * Generate refresh token for user
 * @param {Object} user - User object with matricule and role
 * @returns {string} Refresh token
 */
function generateRefreshToken(user) {
  try {
    const payload = {
      matricule: user.matricule,
      role: user.role,
      type: 'refresh'
    };

    const options = {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
      expiresIn: '7d' // Refresh tokens last longer
    };

    return jwt.sign(payload, config.jwt.secret, options);
  } catch (error) {
    logger.error('Error generating refresh token', error);
    throw new Error('Refresh token generation failed');
  }
}

/**
 * Verify JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload
 */
function verifyToken(token) {
  try {
    const options = {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    };

    return jwt.verify(token, config.jwt.secret, options);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    } else {
      logger.error('Error verifying JWT token', error);
      throw new Error('Token verification failed');
    }
  }
}

/**
 * Extract token from Authorization header
 * @param {string} authHeader - Authorization header value
 * @returns {string|null} Extracted token or null
 */
function extractTokenFromHeader(authHeader) {
  if (!authHeader) return null;
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
}

/**
 * Decode token without verification (for debugging)
 * @param {string} token - JWT token to decode
 * @returns {Object} Decoded token payload
 */
function decodeToken(token) {
  try {
    return jwt.decode(token, { complete: true });
  } catch (error) {
    logger.error('Error decoding JWT token', error);
    return null;
  }
}

/**
 * Check if token is expired
 * @param {string} token - JWT token to check
 * @returns {boolean} True if expired, false otherwise
 */
function isTokenExpired(token) {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
}

/**
 * Get token expiry time
 * @param {string} token - JWT token
 * @returns {Date|null} Expiry date or null
 */
function getTokenExpiry(token) {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return null;
    
    return new Date(decoded.exp * 1000);
  } catch (error) {
    return null;
  }
}

module.exports = {
  generateToken,
  generateRefreshToken,
  verifyToken,
  extractTokenFromHeader,
  decodeToken,
  isTokenExpired,
  getTokenExpiry
};
