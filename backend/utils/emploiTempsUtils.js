/**
 * Utilitaires avancés pour la gestion des emplois du temps
 * NS School Manager - Advanced Timetable Utilities
 */

const { db } = require('../models/db');
const logger = require('./logger');

class EmploiTempsUtils {
  
  /**
   * Exporte l'emploi du temps d'une classe au format CSV
   */
  static async exportToCSV(nomClasse) {
    try {
      const requete = `
        SELECT 
          et.jour_semaine,
          TIME_FORMAT(et.heure_debut, '%H:%i') as heure_debut,
          TIME_FORMAT(et.heure_fin, '%H:%i') as heure_fin,
          m.nom as matiere,
          et.salle,
          CONCAT(p.prenom, ' ', p.nom) as professeur
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        WHERE et.nom_classe = ?
        ORDER BY 
          FIELD(et.jour_semaine, '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'),
          et.heure_debut
      `;

      const creneaux = await db.query(requete, [nomClasse]);
      
      // En-têtes CSV
      let csv = 'Jour,Heure Début,Heure Fin,Matière,Salle,Professeur\n';
      
      // Données
      creneaux.forEach(creneau => {
        csv += `"${creneau.jour_semaine}","${creneau.heure_debut}","${creneau.heure_fin}","${creneau.matiere}","${creneau.salle}","${creneau.professeur}"\n`;
      });

      return csv;
    } catch (error) {
      logger.error('Erreur lors de l\'export CSV:', error);
      throw new Error('Échec de l\'export CSV');
    }
  }

  /**
   * Exporte l'emploi du temps au format JSON
   */
  static async exportToJSON(nomClasse) {
    try {
      const requete = `
        SELECT 
          et.id,
          et.jour_semaine,
          TIME_FORMAT(et.heure_debut, '%H:%i') as heure_debut,
          TIME_FORMAT(et.heure_fin, '%H:%i') as heure_fin,
          m.nom as matiere,
          m.matiere_id,
          et.salle,
          p.nom as nom_professeur,
          p.prenom as prenom_professeur,
          p.matricule as matricule_professeur
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        WHERE et.nom_classe = ?
        ORDER BY 
          FIELD(et.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'),
          et.heure_debut
      `;

      const creneaux = await db.query(requete, [nomClasse]);
      
      return {
        classe: nomClasse,
        dateExport: new Date().toISOString(),
        nombreCreneaux: creneaux.length,
        creneaux: creneaux
      };
    } catch (error) {
      logger.error('Erreur lors de l\'export JSON:', error);
      throw new Error('Échec de l\'export JSON');
    }
  }

  /**
   * Importe des créneaux depuis des données JSON
   */
  static async importFromJSON(data, nomClasse) {
    try {
      const { creneaux } = data;
      let importedCount = 0;
      let errors = [];

      for (const creneau of creneaux) {
        try {
          // Vérifier si la matière existe pour cette classe
          const matiereExiste = await db.query(
            'SELECT matiere_id FROM matiere WHERE nom = ? AND nom_classe = ?',
            [creneau.matiere, nomClasse]
          );

          if (matiereExiste.length === 0) {
            errors.push(`Matière "${creneau.matiere}" non trouvée pour la classe ${nomClasse}`);
            continue;
          }

          // Insérer le créneau
          await db.query(
            'INSERT INTO emploi_temps (nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle) VALUES (?, ?, ?, ?, ?, ?)',
            [nomClasse, matiereExiste[0].matiere_id, creneau.jour_semaine, creneau.heure_debut, creneau.heure_fin, creneau.salle]
          );
          
          importedCount++;
        } catch (error) {
          errors.push(`Erreur pour le créneau ${creneau.matiere} ${creneau.jour_semaine}: ${error.message}`);
        }
      }

      return {
        success: true,
        importedCount,
        errors,
        message: `${importedCount} créneaux importés avec succès${errors.length > 0 ? `, ${errors.length} erreurs` : ''}`
      };
    } catch (error) {
      logger.error('Erreur lors de l\'import JSON:', error);
      throw new Error('Échec de l\'import JSON');
    }
  }

  /**
   * Vérifie la disponibilité d'un professeur
   */
  static async verifierDisponibiliteProfesseur(matriculeProfesseur, jourSemaine, heureDebut, heureFin, excludeId = null) {
    try {
      let requete = `
        SELECT et.*, m.nom as matiere, et.nom_classe
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        WHERE m.professeur_matricule = ? 
        AND et.jour_semaine = ?
        AND (
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut >= ? AND et.heure_fin <= ?)
        )
      `;

      const params = [matriculeProfesseur, jourSemaine, heureFin, heureDebut, heureDebut, heureFin, heureDebut, heureFin];

      if (excludeId) {
        requete += ' AND et.id != ?';
        params.push(excludeId);
      }

      const conflits = await db.query(requete, params);
      
      return {
        disponible: conflits.length === 0,
        conflits: conflits
      };
    } catch (error) {
      logger.error('Erreur lors de la vérification de disponibilité professeur:', error);
      throw new Error('Échec de la vérification de disponibilité');
    }
  }

  /**
   * Vérifie la disponibilité d'une salle
   */
  static async verifierDisponibiliteSalle(salle, jourSemaine, heureDebut, heureFin, excludeId = null) {
    try {
      let requete = `
        SELECT et.*, m.nom as matiere, et.nom_classe
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        WHERE et.salle = ? 
        AND et.jour_semaine = ?
        AND (
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut >= ? AND et.heure_fin <= ?)
        )
      `;

      const params = [salle, jourSemaine, heureFin, heureDebut, heureDebut, heureFin, heureDebut, heureFin];

      if (excludeId) {
        requete += ' AND et.id != ?';
        params.push(excludeId);
      }

      const conflits = await db.query(requete, params);
      
      return {
        disponible: conflits.length === 0,
        conflits: conflits
      };
    } catch (error) {
      logger.error('Erreur lors de la vérification de disponibilité salle:', error);
      throw new Error('Échec de la vérification de disponibilité');
    }
  }

  /**
   * Génère des suggestions d'optimisation pour l'emploi du temps
   */
  static async genererSuggestionsOptimisation(nomClasse) {
    try {
      const suggestions = [];

      // 1. Vérifier les trous dans l'emploi du temps
      const creneaux = await db.query(`
        SELECT jour_semaine, heure_debut, heure_fin
        FROM emploi_temps 
        WHERE nom_classe = ?
        ORDER BY jour_semaine, heure_debut
      `, [nomClasse]);

      // Grouper par jour
      const creneauxParJour = {};
      creneaux.forEach(creneau => {
        if (!creneauxParJour[creneau.jour_semaine]) {
          creneauxParJour[creneau.jour_semaine] = [];
        }
        creneauxParJour[creneau.jour_semaine].push(creneau);
      });

      // Analyser les trous
      Object.keys(creneauxParJour).forEach(jour => {
        const creneauxJour = creneauxParJour[jour];
        for (let i = 0; i < creneauxJour.length - 1; i++) {
          const finActuel = creneauxJour[i].heure_fin;
          const debutSuivant = creneauxJour[i + 1].heure_debut;
          
          if (finActuel !== debutSuivant) {
            const dureeEcart = this.calculerDureeEcart(finActuel, debutSuivant);
            if (dureeEcart > 30) { // Plus de 30 minutes d'écart
              suggestions.push({
                type: 'trou_horaire',
                jour: jour,
                message: `Trou de ${dureeEcart} minutes entre ${finActuel} et ${debutSuivant}`,
                priorite: 'moyenne'
              });
            }
          }
        }
      });

      // 2. Vérifier la répartition des matières
      const repartitionMatieres = await db.query(`
        SELECT m.nom, COUNT(*) as nombre_creneaux
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        WHERE et.nom_classe = ?
        GROUP BY m.nom
      `, [nomClasse]);

      repartitionMatieres.forEach(matiere => {
        if (matiere.nombre_creneaux < 2) {
          suggestions.push({
            type: 'repartition_matiere',
            message: `La matière "${matiere.nom}" n'a que ${matiere.nombre_creneaux} créneau(x) par semaine`,
            priorite: 'faible'
          });
        } else if (matiere.nombre_creneaux > 6) {
          suggestions.push({
            type: 'repartition_matiere',
            message: `La matière "${matiere.nom}" a ${matiere.nombre_creneaux} créneaux par semaine (peut-être trop)`,
            priorite: 'moyenne'
          });
        }
      });

      // 3. Vérifier les créneaux en fin de journée
      const creneauxTardifs = await db.query(`
        SELECT et.*, m.nom as matiere
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        WHERE et.nom_classe = ? AND et.heure_debut >= '16:00'
      `, [nomClasse]);

      if (creneauxTardifs.length > 0) {
        suggestions.push({
          type: 'horaire_tardif',
          message: `${creneauxTardifs.length} créneau(x) après 16h00 (peut affecter la concentration)`,
          priorite: 'faible'
        });
      }

      return suggestions;
    } catch (error) {
      logger.error('Erreur lors de la génération de suggestions:', error);
      throw new Error('Échec de la génération de suggestions');
    }
  }

  /**
   * Calcule la durée d'écart entre deux heures
   */
  static calculerDureeEcart(heure1, heure2) {
    const [h1, m1] = heure1.split(':').map(Number);
    const [h2, m2] = heure2.split(':').map(Number);
    
    const minutes1 = h1 * 60 + m1;
    const minutes2 = h2 * 60 + m2;
    
    return Math.abs(minutes2 - minutes1);
  }

  /**
   * Copie l'emploi du temps d'une classe vers une autre
   */
  static async copierEmploiTemps(classeSource, classeDestination) {
    try {
      // Vérifier que les classes existent
      const [sourceExiste, destExiste] = await Promise.all([
        db.query('SELECT nom_classe FROM classe WHERE nom_classe = ?', [classeSource]),
        db.query('SELECT nom_classe FROM classe WHERE nom_classe = ?', [classeDestination])
      ]);

      if (sourceExiste.length === 0) {
        throw new Error(`Classe source "${classeSource}" non trouvée`);
      }
      if (destExiste.length === 0) {
        throw new Error(`Classe destination "${classeDestination}" non trouvée`);
      }

      // Récupérer l'emploi du temps source
      const creneauxSource = await db.query(`
        SELECT et.*, m.nom as nom_matiere
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        WHERE et.nom_classe = ?
      `, [classeSource]);

      let copiedCount = 0;
      let errors = [];

      for (const creneau of creneauxSource) {
        try {
          // Chercher une matière équivalente pour la classe destination
          const matiereDestination = await db.query(
            'SELECT matiere_id FROM matiere WHERE nom = ? AND nom_classe = ?',
            [creneau.nom_matiere, classeDestination]
          );

          if (matiereDestination.length > 0) {
            await db.query(
              'INSERT INTO emploi_temps (nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle) VALUES (?, ?, ?, ?, ?, ?)',
              [classeDestination, matiereDestination[0].matiere_id, creneau.jour_semaine, creneau.heure_debut, creneau.heure_fin, creneau.salle]
            );
            copiedCount++;
          } else {
            errors.push(`Matière "${creneau.nom_matiere}" non trouvée pour la classe ${classeDestination}`);
          }
        } catch (error) {
          errors.push(`Erreur pour le créneau ${creneau.nom_matiere}: ${error.message}`);
        }
      }

      return {
        success: true,
        copiedCount,
        errors,
        message: `${copiedCount} créneaux copiés avec succès${errors.length > 0 ? `, ${errors.length} erreurs` : ''}`
      };
    } catch (error) {
      logger.error('Erreur lors de la copie d\'emploi du temps:', error);
      throw error;
    }
  }
}

module.exports = EmploiTempsUtils;
