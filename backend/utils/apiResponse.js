/**
 * API Response Utility for NS School Manager
 * Provides standardized response formats for both web and mobile clients
 */

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {*} data - Response data
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code (default: 200)
 */
function sendSuccess(res, data = null, message = 'Success', statusCode = 200) {
  const response = {
    success: true,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  return res.status(statusCode).json(response);
}

/**
 * Send error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code (default: 400)
 * @param {string} errorCode - Application-specific error code
 * @param {*} details - Additional error details
 */
function sendError(res, message = 'An error occurred', statusCode = 400, errorCode = null, details = null) {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  if (errorCode) {
    response.errorCode = errorCode;
  }

  if (details) {
    response.details = details;
  }

  return res.status(statusCode).json(response);
}

/**
 * Send validation error response
 * @param {Object} res - Express response object
 * @param {Array|Object} errors - Validation errors
 * @param {string} message - Error message
 */
function sendValidationError(res, errors, message = 'Validation failed') {
  return sendError(res, message, 422, 'VALIDATION_ERROR', errors);
}

/**
 * Send authentication error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
function sendAuthError(res, message = 'Authentication required') {
  return sendError(res, message, 401, 'AUTHENTICATION_ERROR');
}

/**
 * Send authorization error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
function sendAuthorizationError(res, message = 'Insufficient permissions') {
  return sendError(res, message, 403, 'AUTHORIZATION_ERROR');
}

/**
 * Send not found error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 */
function sendNotFoundError(res, message = 'Resource not found') {
  return sendError(res, message, 404, 'NOT_FOUND');
}

/**
 * Send server error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {*} details - Error details (only in development)
 */
function sendServerError(res, message = 'Internal server error', details = null) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return sendError(res, message, 500, 'SERVER_ERROR', isDevelopment ? details : null);
}

/**
 * Send paginated response
 * @param {Object} res - Express response object
 * @param {Array} data - Response data
 * @param {Object} pagination - Pagination info
 * @param {string} message - Success message
 */
function sendPaginatedResponse(res, data, pagination, message = 'Success') {
  const response = {
    success: true,
    message,
    data,
    pagination: {
      page: pagination.page || 1,
      limit: pagination.limit || 10,
      total: pagination.total || 0,
      totalPages: Math.ceil((pagination.total || 0) / (pagination.limit || 10))
    },
    timestamp: new Date().toISOString()
  };

  return res.status(200).json(response);
}

/**
 * Handle database errors and send appropriate response
 * @param {Object} res - Express response object
 * @param {Error} error - Database error
 * @param {string} operation - Operation being performed
 */
function handleDatabaseError(res, error, operation = 'database operation') {
  console.error(`Database error during ${operation}:`, error);
  
  // Check for specific database errors
  if (error.code === 'ER_DUP_ENTRY') {
    return sendError(res, 'Duplicate entry found', 409, 'DUPLICATE_ENTRY');
  } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
    return sendError(res, 'Referenced record not found', 400, 'REFERENCE_ERROR');
  } else if (error.code === 'ER_ROW_IS_REFERENCED_2') {
    return sendError(res, 'Cannot delete record - it is referenced by other records', 400, 'REFERENCE_CONSTRAINT');
  }
  
  return sendServerError(res, `Error during ${operation}`, error.message);
}

/**
 * Middleware to catch async errors
 * @param {Function} fn - Async function to wrap
 * @returns {Function} Express middleware function
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

module.exports = {
  sendSuccess,
  sendError,
  sendValidationError,
  sendAuthError,
  sendAuthorizationError,
  sendNotFoundError,
  sendServerError,
  sendPaginatedResponse,
  handleDatabaseError,
  asyncHandler
};
