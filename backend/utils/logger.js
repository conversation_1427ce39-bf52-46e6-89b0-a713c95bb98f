/**
 * Professional Logging System for NS School Manager
 * Provides structured logging with different levels and outputs
 */

const fs = require('fs');
const path = require('path');
const config = require('../config/config');

class Logger {
  constructor() {
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };
    
    this.colors = {
      error: '\x1b[31m', // Red
      warn: '\x1b[33m',  // Yellow
      info: '\x1b[36m',  // Cyan
      debug: '\x1b[37m', // White
      reset: '\x1b[0m'
    };

    // Ensure logs directory exists
    this.logsDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
    }

    this.logFile = path.join(this.logsDir, 'app.log');
    this.errorFile = path.join(this.logsDir, 'error.log');
  }

  /**
   * Format log message with timestamp and level
   */
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
  }

  /**
   * Write log to file
   */
  writeToFile(filename, message) {
    try {
      fs.appendFileSync(filename, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  /**
   * Log message with specified level
   */
  log(level, message, meta = {}) {
    const currentLevel = this.levels[config.logging?.level] || this.levels.info;
    const messageLevel = this.levels[level];

    if (messageLevel <= currentLevel) {
      const formattedMessage = this.formatMessage(level, message, meta);
      
      // Console output with colors
      const color = this.colors[level] || this.colors.reset;
      console.log(`${color}${formattedMessage}${this.colors.reset}`);
      
      // File output
      this.writeToFile(this.logFile, formattedMessage);
      
      // Error file for errors and warnings
      if (level === 'error' || level === 'warn') {
        this.writeToFile(this.errorFile, formattedMessage);
      }
    }
  }

  /**
   * Log error with stack trace
   */
  error(message, error = null, meta = {}) {
    if (error instanceof Error) {
      meta.stack = error.stack;
      meta.name = error.name;
      message = `${message}: ${error.message}`;
    }
    this.log('error', message, meta);
  }

  /**
   * Log warning
   */
  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  /**
   * Log info
   */
  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  /**
   * Log debug
   */
  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  /**
   * Log database operations
   */
  database(operation, query, params = [], duration = null, error = null) {
    const meta = {
      operation,
      query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
      params: params.length > 0 ? params : undefined,
      duration: duration ? `${duration}ms` : undefined
    };

    if (error) {
      this.error(`Database ${operation} failed`, error, meta);
    } else {
      this.debug(`Database ${operation} completed`, meta);
    }
  }

  /**
   * Log authentication events
   */
  auth(event, matricule, success = true, meta = {}) {
    const message = `Authentication ${event} for ${matricule}`;
    const logMeta = {
      matricule,
      event,
      success,
      ...meta
    };

    if (success) {
      this.info(message, logMeta);
    } else {
      this.warn(`${message} failed`, logMeta);
    }
  }

  /**
   * Log HTTP requests
   */
  request(req, res, duration) {
    const meta = {
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent'),
      matricule: req.session?.matricule,
      role: req.session?.role
    };

    const message = `${req.method} ${req.url} ${res.statusCode}`;
    
    if (res.statusCode >= 400) {
      this.warn(message, meta);
    } else {
      this.info(message, meta);
    }
  }

  /**
   * Log file operations
   */
  file(operation, filename, success = true, meta = {}) {
    const message = `File ${operation}: ${filename}`;
    const logMeta = {
      operation,
      filename,
      success,
      ...meta
    };

    if (success) {
      this.info(message, logMeta);
    } else {
      this.error(`${message} failed`, null, logMeta);
    }
  }

  /**
   * Clean old log files
   */
  cleanup(daysToKeep = 7) {
    try {
      const files = fs.readdirSync(this.logsDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      files.forEach(file => {
        const filePath = path.join(this.logsDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          this.info(`Cleaned up old log file: ${file}`);
        }
      });
    } catch (error) {
      this.error('Failed to cleanup log files', error);
    }
  }
}

// Create singleton instance
const logger = new Logger();

// Cleanup old logs on startup
logger.cleanup();

module.exports = logger;
