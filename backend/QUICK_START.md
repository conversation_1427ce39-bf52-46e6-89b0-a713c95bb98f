# 🚀 NS School Manager - Quick Start Guide

Get up and running with NS School Manager in 5 minutes!

## ⚡ Super Quick Setup

```bash
# 1. Navigate to backend directory
cd backend

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your MySQL credentials

# 4. Setup database with sample data (complete reset)
npm run db:reset

# 5. Start the application
npm run dev
```

## 🔑 Ready-to-Use Test Accounts

After running `npm run db:reset`, you can immediately login with these accounts:

### 👨‍💼 Admin Access
- **URL**: http://localhost:8080/ad
- **Matricule**: `ADM001`
- **Password**: `admin123`

### 👨‍🏫 Teacher Access
- **URL**: http://localhost:8080/
- **Matricule**: `PROF001`
- **Password**: `prof123`
- **Role**: Select "Professeur"

### 👨‍🎓 Student Access
- **URL**: http://localhost:8080/
- **Matricule**: `ELV001`
- **Password**: `eleve123`
- **Role**: Select "Élève"

## 🧪 Quick Test Scenarios

### Test 1: Admin <PERSON> (30 seconds)
1. Go to http://localhost:8080/ad
2. Login with `ADM001` / `admin123`
3. Navigate to "Utilisateurs" to see all users
4. Check "Classes" to see available classes

### Test 2: Teacher Course Upload (2 minutes)
1. Go to http://localhost:8080/
2. Login with `PROF001` / `prof123` (Role: Professeur)
3. Navigate to "Cours"
4. Try uploading a sample PDF file
5. Verify the course appears in the list

### Test 3: Student Course Access (1 minute)
1. Go to http://localhost:8080/
2. Login with `ELV001` / `eleve123` (Role: Élève)
3. Navigate to "Cours"
4. See courses available for your class (6eme A)
5. Try downloading a course file

## 📊 What's Included in Sample Data

- **2 Admin accounts** with full system access
- **4 Teacher accounts** (Math, French, Physics, History)
- **6 Student accounts** across 5 different classes
- **5 Classes** from 6eme to 3eme
- **8 Subject assignments** linking teachers to classes
- **4 Sample courses** ready for download
- **Complete system configuration**

## 🔧 Useful Commands

```bash
# Complete reset: Drop database, recreate tables, and seed data
npm run db:reset

# Clear existing data and add fresh seed data
npm run db:clear

# Add sample data only (keep existing data)
npm run db:seed-only

# Start development server
npm run dev

# Start production server
npm start

# View application logs
tail -f logs/app.log
```

## 🌐 Application URLs

- **Student/Teacher Login**: http://localhost:8080/
- **Admin Login**: http://localhost:8080/ad
- **API Documentation**: See [README.md](./README.md)

## 🆘 Quick Troubleshooting

### Port 8080 already in use?
```bash
# Kill existing process
lsof -ti:8080 | xargs kill -9

# Or change port in .env
echo "PORT=3000" >> .env
```

### Database connection error?
```bash
# Check MySQL is running
sudo service mysql start

# Verify credentials in .env file
cat .env | grep DB_
```

### Login not working?
- Make sure you're using the correct URL for your role
- Admin login: `/ad` | Student/Teacher login: `/`
- Clear browser cache and cookies
- Verify you ran `npm run db:reset`

## 📖 Next Steps

1. **Explore Features**: Try all the different user roles
2. **Read Full Guide**: Check [TESTING_GUIDE.md](./TESTING_GUIDE.md)
3. **Customize**: Modify the seed data in `seeds/seedData.js`
4. **Deploy**: Follow production deployment guide in [README.md](./README.md)

## 🎯 Key Features to Test

- **File Upload/Download**: Teachers can upload courses, students can download
- **User Management**: Admins can create/edit users
- **Role-based Access**: Different features for different user types
- **Professional Logging**: Check `logs/` directory for detailed logs
- **Session Management**: Logout/login functionality
- **Responsive Design**: Works on mobile and desktop

---

**Need help?** Check the [TESTING_GUIDE.md](./TESTING_GUIDE.md) for detailed testing scenarios or [README.md](./README.md) for complete documentation.
