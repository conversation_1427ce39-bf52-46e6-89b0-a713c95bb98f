#!/usr/bin/env node

/**
 * Test script to verify database connection and data
 */

const { query, initialize } = require('../models/db');

async function testDatabase() {
  try {
    console.log('🔍 Testing database connection and data...\n');

    // Initialize database
    await initialize();

    // Test professor count
    const professors = await query('SELECT COUNT(*) as count FROM professeur');
    console.log(`👨‍🏫 Professors in database: ${professors[0].count}`);

    if (professors[0].count > 0) {
      const profList = await query('SELECT matricule, nom, prenom, specialiste FROM professeur LIMIT 5');
      console.log('\n📋 Professor list:');
      profList.forEach(prof => {
        console.log(`  - ${prof.matricule}: ${prof.nom} ${prof.prenom} (${prof.specialiste})`);
      });
    }

    // Test student count
    const students = await query('SELECT COUNT(*) as count FROM eleve');
    console.log(`\n👨‍🎓 Students in database: ${students[0].count}`);

    // Test admin count
    const admins = await query('SELECT COUNT(*) as count FROM admin');
    console.log(`👨‍💼 Admins in database: ${admins[0].count}`);

    // Test classes count
    const classes = await query('SELECT COUNT(*) as count FROM classe');
    console.log(`🏫 Classes in database: ${classes[0].count}`);

    console.log('\n✅ Database test completed successfully!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  } finally {
    process.exit(0);
  }
}

testDatabase();
