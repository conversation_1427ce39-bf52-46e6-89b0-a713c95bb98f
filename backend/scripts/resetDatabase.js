#!/usr/bin/env node

/**
 * Complete Database Reset Script for NS School Manager
 * Drops and recreates database, then seeds with sample data
 */

const mysql = require('mysql2');
const config = require('../config/config');
const { initialize } = require('../models/db');
const DatabaseSeeder = require('../seeds/seedData');

// Validate configuration
config.validate();

class DatabaseReset {
  constructor() {
    this.setupConnection = null;
    this.dbConnection = null;
  }

  /**
   * Create initial connection without database
   */
  async createInitialConnection() {
    this.setupConnection = mysql.createConnection({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password
    });

    return new Promise((resolve, reject) => {
      this.setupConnection.connect((err) => {
        if (err) {
          console.error('❌ Initial connection failed:', err.message);
          reject(err);
        } else {
          console.log('🛠️  MySQL connection established');
          resolve();
        }
      });
    });
  }

  /**
   * Drop and recreate database
   */
  async resetDatabase() {
    console.log(`🗑️  Dropping database "${config.database.name}" if exists...`);
    
    await new Promise((resolve, reject) => {
      this.setupConnection.query(`DROP DATABASE IF EXISTS ${config.database.name}`, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log(`📦 Creating fresh database "${config.database.name}"...`);
    
    await new Promise((resolve, reject) => {
      this.setupConnection.query(`CREATE DATABASE ${config.database.name}`, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    console.log(`✅ Database "${config.database.name}" created successfully`);
  }

  /**
   * Create database connection
   */
  async createDatabaseConnection() {
    this.dbConnection = mysql.createConnection({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password,
      database: config.database.name,
      charset: config.database.charset,
      timezone: config.database.timezone
    });

    return new Promise((resolve, reject) => {
      this.dbConnection.connect((err) => {
        if (err) {
          console.error(`❌ Connection to "${config.database.name}" failed:`, err.message);
          reject(err);
        } else {
          console.log(`✅ Connected to "${config.database.name}"`);
          resolve();
        }
      });
    });
  }

  /**
   * Create all tables
   */
  async createTables() {
    console.log('🏗️  Creating database tables...');

    const tables = [
      {
        name: 'utilisateur',
        sql: `CREATE TABLE utilisateur (
          matricule VARCHAR(100) PRIMARY KEY,
          password VARCHAR(255),
          role ENUM('admin', 'eleve', 'professeur') NOT NULL
        );`
      },
      {
        name: 'classe',
        sql: `CREATE TABLE classe (
          nom_classe VARCHAR(50) PRIMARY KEY,
          niveau VARCHAR(20),
          annee_scolaire VARCHAR(20)
        );`
      },
      {
        name: 'professeur',
        sql: `CREATE TABLE professeur (
          matricule VARCHAR(100) PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          sexe ENUM('homme', 'femme'),
          specialiste VARCHAR(100),
          email VARCHAR(100),
          telephone VARCHAR(50),
          FOREIGN KEY (matricule) REFERENCES utilisateur(matricule)
        );`
      },
      {
        name: 'admin',
        sql: `CREATE TABLE admin (
          matricule VARCHAR(100) PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          email VARCHAR(100),
          sexe ENUM('homme', 'femme'),
          telephone VARCHAR(50),
          FOREIGN KEY (matricule) REFERENCES utilisateur(matricule)
        );`
      },
      {
        name: 'eleve',
        sql: `CREATE TABLE eleve (
          matricule VARCHAR(100) PRIMARY KEY,
          nom VARCHAR(100),
          prenom VARCHAR(100),
          date_naissance DATE,
          sexe ENUM('homme', 'femme'),
          email VARCHAR(100),
          telephone VARCHAR(50),
          nom_classe VARCHAR(50),
          FOREIGN KEY (matricule) REFERENCES utilisateur(matricule),
          FOREIGN KEY (nom_classe) REFERENCES classe(nom_classe)
        );`
      },
      {
        name: 'matiere',
        sql: `CREATE TABLE matiere (
          matiere_id INT AUTO_INCREMENT PRIMARY KEY,
          nom VARCHAR(100),
          professeur_matricule VARCHAR(100),
          nom_classe VARCHAR(50),
          FOREIGN KEY (professeur_matricule) REFERENCES professeur(matricule),
          FOREIGN KEY (nom_classe) REFERENCES classe(nom_classe)
        );`
      },
      {
        name: 'cours',
        sql: `CREATE TABLE cours (
          cours_id INT AUTO_INCREMENT PRIMARY KEY,
          titre VARCHAR(255),
          fichier VARCHAR(255),
          professeur_matricule VARCHAR(100),
          matiere_id INT,
          nom_classe VARCHAR(100),
          date_ajout DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (professeur_matricule) REFERENCES professeur(matricule),
          FOREIGN KEY (matiere_id) REFERENCES matiere(matiere_id),
          FOREIGN KEY (nom_classe) REFERENCES classe(nom_classe)
        );`
      },
      {
        name: 'note',
        sql: `CREATE TABLE note (
          note_id INT AUTO_INCREMENT PRIMARY KEY,
          eleve_matricule VARCHAR(100),
          matiere_id INT,
          note DECIMAL(5,2),
          coefficient INT DEFAULT 1,
          type_evaluation VARCHAR(50),
          date_evaluation DATE,
          FOREIGN KEY (eleve_matricule) REFERENCES eleve(matricule),
          FOREIGN KEY (matiere_id) REFERENCES matiere(matiere_id)
        );`
      },
      {
        name: 'absence',
        sql: `CREATE TABLE absence (
          absence_id INT AUTO_INCREMENT PRIMARY KEY,
          eleve_matricule VARCHAR(100),
          matiere_id INT,
          date_absence DATE,
          justifiee BOOLEAN DEFAULT FALSE,
          motif TEXT,
          UNIQUE KEY unique_absence (eleve_matricule, matiere_id, date_absence),
          FOREIGN KEY (eleve_matricule) REFERENCES eleve(matricule),
          FOREIGN KEY (matiere_id) REFERENCES matiere(matiere_id)
        );`
      },
      {
        name: 'activite',
        sql: `CREATE TABLE activite (
          id INT AUTO_INCREMENT PRIMARY KEY,
          utilisateur_id VARCHAR(100),
          action VARCHAR(255),
          date DATETIME,
          statut VARCHAR(50),
          FOREIGN KEY (utilisateur_id) REFERENCES utilisateur(matricule)
        );`
      },
      {
        name: 'bulletin',
        sql: `CREATE TABLE bulletin (
          id INT AUTO_INCREMENT PRIMARY KEY,
          eleve_matricule VARCHAR(100),
          periode VARCHAR(50),
          moyenne_generale DECIMAL(5,2),
          appreciation TEXT,
          date_creation DATE,
          FOREIGN KEY (eleve_matricule) REFERENCES eleve(matricule)
        );`
      },
      {
        name: 'salle',
        sql: `CREATE TABLE salle (
          salle_id INT AUTO_INCREMENT PRIMARY KEY,
          nom_salle VARCHAR(50) NOT NULL UNIQUE,
          type_salle ENUM('normale', 'laboratoire', 'informatique', 'sport', 'bibliotheque', 'autre') DEFAULT 'normale',
          capacite INT DEFAULT 30,
          equipements TEXT,
          etage VARCHAR(10),
          batiment VARCHAR(50),
          disponible BOOLEAN DEFAULT TRUE,
          date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );`
      },
      {
        name: 'emploi_temps',
        sql: `CREATE TABLE emploi_temps (
          id INT AUTO_INCREMENT PRIMARY KEY,
          nom_classe VARCHAR(50),
          matiere_id INT,
          jour_semaine VARCHAR(20),
          heure_debut TIME,
          heure_fin TIME,
          salle VARCHAR(50),
          salle_id INT NULL,
          FOREIGN KEY (nom_classe) REFERENCES classe(nom_classe),
          FOREIGN KEY (matiere_id) REFERENCES matiere(matiere_id),
          FOREIGN KEY (salle_id) REFERENCES salle(salle_id)
        );`
      },
      {
        name: 'devoir',
        sql: `CREATE TABLE devoir (
          devoir_id INT AUTO_INCREMENT PRIMARY KEY,
          titre VARCHAR(255),
          description TEXT,
          fichier VARCHAR(255),
          matiere_id INT,
          matricule VARCHAR(100),
          date_creation DATE,
          date_limite DATE,
          FOREIGN KEY (matiere_id) REFERENCES matiere(matiere_id),
          FOREIGN KEY (matricule) REFERENCES professeur(matricule)
        );`
      },
      {
        name: 'soumission_devoir',
        sql: `CREATE TABLE soumission_devoir (
          soumission_id INT AUTO_INCREMENT PRIMARY KEY,
          devoir_id INT,
          eleve_matricule VARCHAR(100),
          fichier VARCHAR(255),
          date_soumission DATETIME,
          note DECIMAL(5,2),
          remarque TEXT,
          FOREIGN KEY (devoir_id) REFERENCES devoir(devoir_id),
          FOREIGN KEY (eleve_matricule) REFERENCES eleve(matricule)
        );`
      },
      {
        name: 'enseignant_classe',
        sql: `CREATE TABLE enseignant_classe (
          id INT AUTO_INCREMENT PRIMARY KEY,
          professeur_matricule VARCHAR(100),
          nom_classe VARCHAR(50),
          FOREIGN KEY (professeur_matricule) REFERENCES professeur(matricule),
          FOREIGN KEY (nom_classe) REFERENCES classe(nom_classe)
        );`
      },
      {
        name: 'message',
        sql: `CREATE TABLE message (
          id INT AUTO_INCREMENT PRIMARY KEY,
          sender_matricule VARCHAR(100) NOT NULL,
          receiver_matricule VARCHAR(100) NULL,
          nom_classe VARCHAR(50) NULL,
          contenu TEXT NOT NULL,
          subject VARCHAR(255) NULL,
          priority ENUM('normal', 'high', 'urgent') DEFAULT 'normal',
          date_envoi DATETIME DEFAULT CURRENT_TIMESTAMP,
          type ENUM('privé', 'annonce') NOT NULL,
          lu BOOLEAN DEFAULT FALSE,
          archived BOOLEAN DEFAULT FALSE,
          FOREIGN KEY (sender_matricule) REFERENCES utilisateur(matricule) ON DELETE CASCADE ON UPDATE CASCADE,
          FOREIGN KEY (receiver_matricule) REFERENCES utilisateur(matricule) ON DELETE SET NULL ON UPDATE CASCADE,
          FOREIGN KEY (nom_classe) REFERENCES classe(nom_classe) ON DELETE SET NULL ON UPDATE CASCADE
        );`
      },
      {
        name: 'parametre',
        sql: `CREATE TABLE parametre (
          id INT PRIMARY KEY,
          annee_scolaire VARCHAR(20),
          nb_trimestres INT,
          theme VARCHAR(20),
          messagerie_active BOOLEAN,
          nom_ecole VARCHAR(100),
          email_ecole VARCHAR(100),
          telephone_ecole VARCHAR(50),
          adresse_ecole VARCHAR(255),
          logo_ecole VARCHAR(255)
        );`
      }
    ];

    for (const table of tables) {
      await new Promise((resolve, reject) => {
        this.dbConnection.query(table.sql, (err) => {
          if (err) {
            console.error(`❌ Error creating table "${table.name}":`, err.message);
            reject(err);
          } else {
            console.log(`📄 Table "${table.name}" created`);
            resolve();
          }
        });
      });
    }

    console.log('✅ All tables created successfully');
  }

  /**
   * Close all connections
   */
  async closeConnections() {
    if (this.dbConnection) {
      await new Promise((resolve) => {
        this.dbConnection.end(() => resolve());
      });
    }
    if (this.setupConnection) {
      await new Promise((resolve) => {
        this.setupConnection.end(() => resolve());
      });
    }
  }

  /**
   * Run complete database reset
   */
  async reset() {
    try {
      console.log('🔄 NS School Manager - Complete Database Reset');
      console.log('===============================================\n');

      await this.createInitialConnection();
      await this.resetDatabase();
      await this.createDatabaseConnection();
      await this.createTables();
      await this.closeConnections();

      console.log('\n✅ Database reset completed successfully!');
      console.log('🌱 Now running database seeding...\n');

      // Initialize the centralized database connection
      await initialize();

      // Run seeding
      const seeder = new DatabaseSeeder();
      await seeder.seedAll(false); // Don't clear since we just created fresh tables

      console.log('\n🎉 Complete database reset and seeding finished!');
      
    } catch (error) {
      console.error('\n❌ Database reset failed:', error.message);
      await this.closeConnections();
      throw error;
    }
  }
}

// Run reset if called directly
if (require.main === module) {
  const reset = new DatabaseReset();
  
  reset.reset()
    .then(() => {
      console.log('✅ Database reset completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database reset failed:', error.message);
      process.exit(1);
    });
}

module.exports = DatabaseReset;
