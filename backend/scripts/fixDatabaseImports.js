#!/usr/bin/env node

/**
 * <PERSON>ript to fix database import patterns across all route files
 * Converts db.query() calls to connection.query() calls
 */

const fs = require('fs');
const path = require('path');

// Files to fix
const filesToFix = [
  'routes/api/cour.js',
  'routes/api/emploi_temps.js',
  'routes/api/bulletin.js',
  'routes/api/parametre.js',
  'routes/prof/devoir.js',
  'routes/prof/cours.js',
  'routes/prof/note.js',
  'routes/prof/absence.js',
  'routes/prof/message.js',
  'routes/eleve/cours.js',
  'routes/eleve/notes.js',
  'routes/eleve/emploi.js',
  'routes/eleve/absence.js',
  'routes/eleve/annonce.js',
  'routes/eleve/devoir.js',
  'routes/eleve/bulletin.js'
];

function fixFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;

    // Fix import statement
    if (content.includes("const db = require('../../models/db')")) {
      content = content.replace(
        "const db = require('../../models/db')",
        "const { connection } = require('../../models/db')"
      );
      modified = true;
    }

    if (content.includes("const db = require('../models/db')")) {
      content = content.replace(
        "const db = require('../models/db')",
        "const { connection } = require('../models/db')"
      );
      modified = true;
    }

    // Fix db.query() calls to connection.query()
    const dbQueryPattern = /\bdb\.query\(/g;
    if (dbQueryPattern.test(content)) {
      content = content.replace(dbQueryPattern, 'connection.query(');
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Fixed: ${filePath}`);
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
    }

  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
  }
}

function main() {
  console.log('🔧 Fixing database import patterns...\n');

  for (const file of filesToFix) {
    fixFile(file);
  }

  console.log('\n🎉 Database import fix completed!');
  console.log('\n📋 Summary:');
  console.log('- Changed: const db = require(...) → const { connection } = require(...)');
  console.log('- Changed: db.query(...) → connection.query(...)');
  console.log('\n✅ All files should now use the correct database connection pattern.');
}

if (require.main === module) {
  main();
}

module.exports = { fixFile };
