# ===========================================
# NS SCHOOL MANAGER - CONFIGURATION EXAMPLE
# ===========================================
# Copy this file to .env and update the values

# Application Configuration
NODE_ENV=development
PORT=8080

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-here
SESSION_MAX_AGE=86400000

# Database Configuration (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=ns_school
DB_USER=root
DB_PASSWORD=your_mysql_password

# JWT Configuration
JWT_SECRET=your-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=pdf,doc,docx,ppt,pptx,xls,xlsx,txt,jpg,png,zip

# Email Configuration (for future use)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# Application URLs
BASE_URL=http://localhost:8080
FRONTEND_URL=http://localhost:3000