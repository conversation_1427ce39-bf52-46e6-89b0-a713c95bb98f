/**
 * Professional Timetable Service for NS School Manager
 * Handles all business logic for schedule management
 */

const { db } = require('../models/db');
const logger = require('../utils/logger');

class TimetableService {
  constructor() {
    this.timeSlots = this.generateTimeSlots();
    this.weekDays = ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'];
  }

  /**
   * Generate standard time slots for the school
   */
  generateTimeSlots() {
    const slots = [];
    const startHour = 7;
    const endHour = 18;
    const slotDuration = 60; // minutes

    for (let hour = startHour; hour < endHour; hour++) {
      slots.push({
        start: `${hour.toString().padStart(2, '0')}:00`,
        end: `${(hour + 1).toString().padStart(2, '0')}:00`,
        label: `${hour.toString().padStart(2, '0')}:00 - ${(hour + 1).toString().padStart(2, '0')}:00`
      });
    }

    return slots;
  }

  /**
   * Get all classes with their details
   */
  async getClasses() {
    try {
      const query = `
        SELECT 
          c.nom_classe,
          c.niveau,
          c.annee_scolaire,
          COUNT(e.matricule) as student_count
        FROM classe c
        LEFT JOIN eleve e ON c.nom_classe = e.nom_classe
        GROUP BY c.nom_classe, c.niveau, c.annee_scolaire
        ORDER BY c.niveau, c.nom_classe
      `;
      
      const classes = await db.query(query);
      logger.info(`Retrieved ${classes.length} classes`);
      return classes;
    } catch (error) {
      logger.error('Error fetching classes:', error);
      throw new Error('Failed to fetch classes');
    }
  }

  /**
   * Get subjects with teacher information
   */
  async getSubjects(className = null) {
    try {
      let query = `
        SELECT 
          m.matiere_id,
          m.nom as subject_name,
          m.nom_classe,
          p.nom as teacher_lastname,
          p.prenom as teacher_firstname,
          p.matricule as teacher_id,
          p.specialiste as teacher_specialty
        FROM matiere m
        JOIN professeur p ON m.professeur_matricule = p.matricule
      `;
      
      const params = [];
      if (className) {
        query += ' WHERE m.nom_classe = ?';
        params.push(className);
      }
      
      query += ' ORDER BY m.nom_classe, m.nom';
      
      const subjects = await db.query(query, params);
      logger.info(`Retrieved ${subjects.length} subjects${className ? ` for class ${className}` : ''}`);
      return subjects;
    } catch (error) {
      logger.error('Error fetching subjects:', error);
      throw new Error('Failed to fetch subjects');
    }
  }

  /**
   * Get complete timetable for a class
   */
  async getClassTimetable(className) {
    try {
      if (!className) {
        throw new Error('Class name is required');
      }

      const query = `
        SELECT 
          et.id,
          et.nom_classe,
          et.jour_semaine,
          TIME_FORMAT(et.heure_debut, '%H:%i') as heure_debut,
          TIME_FORMAT(et.heure_fin, '%H:%i') as heure_fin,
          et.salle,
          m.nom as subject_name,
          m.matiere_id,
          p.nom as teacher_lastname,
          p.prenom as teacher_firstname,
          p.matricule as teacher_id
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        WHERE et.nom_classe = ?
        ORDER BY 
          FIELD(et.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'),
          et.heure_debut
      `;

      const timetable = await db.query(query, [className]);
      
      // Structure the data for easy frontend consumption
      const structuredTimetable = this.structureTimetableData(timetable);
      
      logger.info(`Retrieved timetable for class ${className} with ${timetable.length} time slots`);
      return {
        className,
        timeSlots: timetable,
        structured: structuredTimetable,
        weekDays: this.weekDays,
        availableSlots: this.timeSlots
      };
    } catch (error) {
      logger.error(`Error fetching timetable for class ${className}:`, error);
      throw new Error(`Failed to fetch timetable for class ${className}`);
    }
  }

  /**
   * Structure timetable data into a grid format
   */
  structureTimetableData(timetable) {
    const structured = {};
    
    // Initialize structure
    this.weekDays.forEach(day => {
      structured[day] = {};
      this.timeSlots.forEach(slot => {
        structured[day][slot.start] = null;
      });
    });

    // Fill with actual data
    timetable.forEach(slot => {
      if (structured[slot.jour_semaine] && structured[slot.jour_semaine][slot.heure_debut] !== undefined) {
        structured[slot.jour_semaine][slot.heure_debut] = {
          id: slot.id,
          subject: slot.subject_name,
          teacher: `${slot.teacher_firstname} ${slot.teacher_lastname}`,
          room: slot.salle,
          startTime: slot.heure_debut,
          endTime: slot.heure_fin,
          teacherId: slot.teacher_id,
          subjectId: slot.matiere_id
        };
      }
    });

    return structured;
  }
