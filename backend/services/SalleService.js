/**
 * Service de gestion des salles
 * NS School Manager - Professional Room Management Service
 */

const { query } = require('../models/db');
const logger = require('../utils/logger');

class SalleService {
  constructor() {
    this.typesSalle = ['normale', 'laboratoire', 'informatique', 'sport', 'bibliotheque', 'autre'];
  }

  /**
   * Récupère toutes les salles disponibles
   */
  async obtenirToutesSalles() {
    try {
      const requete = `
        SELECT 
          salle_id,
          nom_salle,
          type_salle,
          capacite,
          equipements,
          etage,
          batiment,
          disponible
        FROM salle
        WHERE disponible = TRUE
        ORDER BY 
          CASE type_salle
            WHEN 'normale' THEN 1
            WHEN 'laboratoire' THEN 2
            WHEN 'informatique' THEN 3
            WHEN 'sport' THEN 4
            WHEN 'bibliotheque' THEN 5
            WHEN 'autre' THEN 6
          END,
          nom_salle
      `;

      const salles = await query(requete);
      logger.info(`Récupération de ${salles.length} salles disponibles`);
      return salles;
    } catch (erreur) {
      logger.error('Erreur lors de la récupération des salles:', erreur);
      throw new Error('Erreur lors de la récupération des salles');
    }
  }

  /**
   * Récupère les salles par type
   */
  async obtenirSallesParType(typeSalle) {
    try {
      if (!this.typesSalle.includes(typeSalle)) {
        throw new Error('Type de salle invalide');
      }

      const requete = `
        SELECT 
          salle_id,
          nom_salle,
          capacite,
          equipements,
          etage,
          batiment
        FROM salle
        WHERE type_salle = ? AND disponible = TRUE
        ORDER BY nom_salle
      `;

      const salles = await query(requete, [typeSalle]);
      logger.info(`Récupération de ${salles.length} salles de type ${typeSalle}`);
      return salles;
    } catch (erreur) {
      logger.error(`Erreur lors de la récupération des salles de type ${typeSalle}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Récupère une salle par son ID
   */
  async obtenirSalleParId(salleId) {
    try {
      const requete = `
        SELECT 
          salle_id,
          nom_salle,
          type_salle,
          capacite,
          equipements,
          etage,
          batiment,
          disponible
        FROM salle
        WHERE salle_id = ?
      `;

      const salles = await query(requete, [salleId]);
      
      if (salles.length === 0) {
        throw new Error('Salle non trouvée');
      }

      return salles[0];
    } catch (erreur) {
      logger.error(`Erreur lors de la récupération de la salle ${salleId}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Récupère une salle par son nom
   */
  async obtenirSalleParNom(nomSalle) {
    try {
      const requete = `
        SELECT 
          salle_id,
          nom_salle,
          type_salle,
          capacite,
          equipements,
          etage,
          batiment,
          disponible
        FROM salle
        WHERE nom_salle = ?
      `;

      const salles = await query(requete, [nomSalle]);
      
      if (salles.length === 0) {
        return null;
      }

      return salles[0];
    } catch (erreur) {
      logger.error(`Erreur lors de la récupération de la salle ${nomSalle}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Vérifie la disponibilité d'une salle à un horaire donné
   */
  async verifierDisponibiliteSalle(salleId, jourSemaine, heureDebut, heureFin, excludeId = null) {
    try {
      let requete = `
        SELECT 
          et.id,
          et.nom_classe,
          TIME_FORMAT(et.heure_debut, '%H:%i') as heure_debut,
          TIME_FORMAT(et.heure_fin, '%H:%i') as heure_fin,
          m.nom as nom_matiere,
          CONCAT(p.prenom, ' ', p.nom) as professeur
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        WHERE et.salle_id = ? AND et.jour_semaine = ?
        AND (
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut >= ? AND et.heure_fin <= ?)
        )
      `;

      const params = [salleId, jourSemaine, heureFin, heureDebut, heureDebut, heureFin, heureDebut, heureFin];

      if (excludeId) {
        requete += ' AND et.id != ?';
        params.push(excludeId);
      }

      const conflits = await query(requete, params);

      return {
        disponible: conflits.length === 0,
        conflits: conflits
      };
    } catch (erreur) {
      logger.error('Erreur lors de la vérification de disponibilité de la salle:', erreur);
      throw erreur;
    }
  }

  /**
   * Ajoute une nouvelle salle
   */
  async ajouterSalle(donneesSalle) {
    try {
      const erreurs = this.validerDonneesSalle(donneesSalle);
      if (erreurs.length > 0) {
        throw new Error(`Données invalides: ${erreurs.join(', ')}`);
      }

      // Vérifier que le nom de salle n'existe pas déjà
      const salleExistante = await this.obtenirSalleParNom(donneesSalle.nom_salle);
      if (salleExistante) {
        throw new Error('Une salle avec ce nom existe déjà');
      }

      const requete = `
        INSERT INTO salle (nom_salle, type_salle, capacite, equipements, etage, batiment)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      const { nom_salle, type_salle, capacite, equipements, etage, batiment } = donneesSalle;
      const resultat = await query(requete, [nom_salle, type_salle, capacite, equipements, etage, batiment]);

      logger.info(`Nouvelle salle ajoutée avec l'ID ${resultat.insertId}: ${nom_salle}`);
      return {
        id: resultat.insertId,
        success: true,
        message: `Salle ${nom_salle} ajoutée avec succès`
      };
    } catch (erreur) {
      logger.error('Erreur lors de l\'ajout de la salle:', erreur);
      throw erreur;
    }
  }

  /**
   * Modifie une salle existante
   */
  async modifierSalle(salleId, donneesSalle) {
    try {
      // Vérifier que la salle existe
      await this.obtenirSalleParId(salleId);

      const erreurs = this.validerDonneesSalle(donneesSalle);
      if (erreurs.length > 0) {
        throw new Error(`Données invalides: ${erreurs.join(', ')}`);
      }

      const requete = `
        UPDATE salle
        SET nom_salle = ?, type_salle = ?, capacite = ?, equipements = ?, etage = ?, batiment = ?
        WHERE salle_id = ?
      `;

      const { nom_salle, type_salle, capacite, equipements, etage, batiment } = donneesSalle;
      await query(requete, [nom_salle, type_salle, capacite, equipements, etage, batiment, salleId]);

      logger.info(`Salle ${salleId} modifiée avec succès`);
      return {
        success: true,
        message: `Salle ${nom_salle} modifiée avec succès`
      };
    } catch (erreur) {
      logger.error(`Erreur lors de la modification de la salle ${salleId}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Supprime une salle (désactive)
   */
  async supprimerSalle(salleId) {
    try {
      const salle = await this.obtenirSalleParId(salleId);

      // Vérifier si la salle est utilisée dans l'emploi du temps
      const requeteUtilisation = `
        SELECT COUNT(*) as count
        FROM emploi_temps
        WHERE salle_id = ?
      `;

      const utilisation = await query(requeteUtilisation, [salleId]);
      
      if (utilisation[0].count > 0) {
        throw new Error('Impossible de supprimer une salle utilisée dans l\'emploi du temps');
      }

      // Désactiver la salle au lieu de la supprimer
      const requete = `
        UPDATE salle
        SET disponible = FALSE
        WHERE salle_id = ?
      `;

      await query(requete, [salleId]);

      logger.info(`Salle ${salleId} (${salle.nom_salle}) désactivée avec succès`);
      return {
        success: true,
        message: `Salle ${salle.nom_salle} supprimée avec succès`
      };
    } catch (erreur) {
      logger.error(`Erreur lors de la suppression de la salle ${salleId}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Récupère les options pour les formulaires de création/édition
   */
  async obtenirOptionsFormulaire() {
    try {
      // Récupérer les bâtiments existants depuis la base de données
      const requeteBatiments = `
        SELECT DISTINCT batiment
        FROM salle
        WHERE batiment IS NOT NULL AND batiment != ''
        ORDER BY batiment
      `;
      const batientsExistants = await query(requeteBatiments);

      // Récupérer les étages existants depuis la base de données
      const requeteEtages = `
        SELECT DISTINCT etage
        FROM salle
        WHERE etage IS NOT NULL AND etage != ''
        ORDER BY
          CASE etage
            WHEN 'RDC' THEN 1
            WHEN '1er' THEN 2
            WHEN '2ème' THEN 3
            WHEN '3ème' THEN 4
            ELSE 5
          END
      `;
      const etagesExistants = await query(requeteEtages);

      // Récupérer les capacités les plus utilisées
      const requeteCapacites = `
        SELECT DISTINCT capacite, COUNT(*) as usage_count
        FROM salle
        WHERE capacite IS NOT NULL
        GROUP BY capacite
        ORDER BY usage_count DESC, capacite ASC
        LIMIT 15
      `;
      const capacitesPopulaires = await query(requeteCapacites);

      return {
        roomTypes: [
          { label: 'Salle Normale', value: 'normale' },
          { label: 'Laboratoire', value: 'laboratoire' },
          { label: 'Salle Informatique', value: 'informatique' },
          { label: 'Installation Sportive', value: 'sport' },
          { label: 'Bibliothèque', value: 'bibliotheque' },
          { label: 'Autre', value: 'autre' }
        ],
        floors: [
          { label: 'Rez-de-chaussée', value: 'RDC' },
          { label: '1er étage', value: '1er' },
          { label: '2ème étage', value: '2ème' },
          { label: '3ème étage', value: '3ème' },
          // Ajouter les étages existants qui ne sont pas dans la liste standard
          ...etagesExistants
            .filter(e => !['RDC', '1er', '2ème', '3ème'].includes(e.etage))
            .map(e => ({ label: e.etage, value: e.etage }))
        ],
        buildings: [
          // Bâtiments standards suggérés
          { label: 'Bâtiment A', value: 'Bâtiment A' },
          { label: 'Bâtiment B', value: 'Bâtiment B' },
          { label: 'Bâtiment C', value: 'Bâtiment C' },
          { label: 'Bâtiment Principal', value: 'Bâtiment Principal' },
          { label: 'Bâtiment Administratif', value: 'Bâtiment Administratif' },
          { label: 'Bâtiment Sciences', value: 'Bâtiment Sciences' },
          { label: 'Bâtiment Informatique', value: 'Bâtiment Informatique' },
          { label: 'Gymnase', value: 'Gymnase' },
          // Ajouter les bâtiments existants qui ne sont pas dans la liste standard
          ...batientsExistants
            .filter(b => ![
              'Bâtiment A', 'Bâtiment B', 'Bâtiment C', 'Bâtiment Principal',
              'Bâtiment Administratif', 'Bâtiment Sciences', 'Bâtiment Informatique', 'Gymnase'
            ].includes(b.batiment))
            .map(b => ({ label: b.batiment, value: b.batiment }))
        ],
        capacities: [
          // Capacités standards
          { label: '10 personnes', value: '10' },
          { label: '15 personnes', value: '15' },
          { label: '20 personnes', value: '20' },
          { label: '25 personnes', value: '25' },
          { label: '30 personnes', value: '30' },
          { label: '35 personnes', value: '35' },
          { label: '40 personnes', value: '40' },
          { label: '50 personnes', value: '50' },
          { label: '60 personnes', value: '60' },
          { label: '80 personnes', value: '80' },
          { label: '100 personnes', value: '100' },
          // Ajouter les capacités populaires qui ne sont pas dans la liste standard
          ...capacitesPopulaires
            .filter(c => ![10, 15, 20, 25, 30, 35, 40, 50, 60, 80, 100].includes(c.capacite))
            .map(c => ({ label: `${c.capacite} personnes`, value: String(c.capacite) }))
        ]
      };
    } catch (erreur) {
      logger.error('Erreur lors de la récupération des options de formulaire:', erreur);
      // Retourner des options par défaut en cas d'erreur
      return {
        roomTypes: [
          { label: 'Salle Normale', value: 'normale' },
          { label: 'Laboratoire', value: 'laboratoire' },
          { label: 'Salle Informatique', value: 'informatique' },
          { label: 'Installation Sportive', value: 'sport' },
          { label: 'Bibliothèque', value: 'bibliotheque' },
          { label: 'Autre', value: 'autre' }
        ],
        floors: [
          { label: 'Rez-de-chaussée', value: 'RDC' },
          { label: '1er étage', value: '1er' },
          { label: '2ème étage', value: '2ème' },
          { label: '3ème étage', value: '3ème' }
        ],
        buildings: [
          { label: 'Bâtiment A', value: 'Bâtiment A' },
          { label: 'Bâtiment B', value: 'Bâtiment B' },
          { label: 'Bâtiment C', value: 'Bâtiment C' }
        ],
        capacities: [
          { label: '20 personnes', value: 20 },
          { label: '30 personnes', value: 30 },
          { label: '40 personnes', value: 40 }
        ]
      };
    }
  }

  /**
   * Valide les données d'une salle
   */
  validerDonneesSalle(donneesSalle) {
    const { nom_salle, type_salle, capacite } = donneesSalle;
    const erreurs = [];

    if (!nom_salle || nom_salle.trim().length === 0) {
      erreurs.push('Le nom de la salle est requis');
    }

    if (!type_salle || !this.typesSalle.includes(type_salle)) {
      erreurs.push('Type de salle invalide');
    }

    if (capacite && (isNaN(capacite) || capacite < 1 || capacite > 200)) {
      erreurs.push('La capacité doit être un nombre entre 1 et 200');
    }

    return erreurs;
  }

  /**
   * Obtient les statistiques des salles
   */
  async obtenirStatistiquesSalles() {
    try {
      const requetes = [
        'SELECT COUNT(*) as total FROM salle WHERE disponible = TRUE',
        'SELECT type_salle, COUNT(*) as nombre FROM salle WHERE disponible = TRUE GROUP BY type_salle',
        'SELECT AVG(capacite) as capacite_moyenne FROM salle WHERE disponible = TRUE',
        `SELECT 
           s.nom_salle,
           COUNT(et.id) as utilisation
         FROM salle s
         LEFT JOIN emploi_temps et ON s.salle_id = et.salle_id
         WHERE s.disponible = TRUE
         GROUP BY s.salle_id, s.nom_salle
         ORDER BY utilisation DESC
         LIMIT 5`
      ];

      const [total, repartitionType, capaciteMoyenne, utilisationTop] = await Promise.all(
        requetes.map(req => query(req))
      );

      return {
        totalSalles: total[0].total,
        repartitionParType: repartitionType,
        capaciteMoyenne: Math.round(capaciteMoyenne[0].capacite_moyenne || 0),
        sallesLesPlusUtilisees: utilisationTop
      };
    } catch (erreur) {
      logger.error('Erreur lors de la récupération des statistiques des salles:', erreur);
      throw erreur;
    }
  }
}

module.exports = SalleService;
