/**
 * Service professionnel pour la gestion des emplois du temps
 * NS School Manager - Emploi du Temps Service
 */

const { db } = require('../models/db');
const logger = require('../utils/logger');

class EmploiTempsService {
  constructor() {
    this.creneauxHoraires = this.genererCreneauxHoraires();
    this.joursSemaine = ['<PERSON><PERSON>', '<PERSON><PERSON>', 'Me<PERSON><PERSON>i', '<PERSON><PERSON>', 'Vendredi', '<PERSON><PERSON>'];
  }

  /**
   * Génère les créneaux horaires standards de l'école
   */
  genererCreneauxHoraires() {
    const creneaux = [];
    const heureDebut = 7;
    const heureFin = 18;

    for (let heure = heureDebut; heure < heureFin; heure++) {
      creneaux.push({
        debut: `${heure.toString().padStart(2, '0')}:00`,
        fin: `${(heure + 1).toString().padStart(2, '0')}:00`,
        libelle: `${heure.toString().padStart(2, '0')}:00 - ${(heure + 1).toString().padStart(2, '0')}:00`
      });
    }

    return creneaux;
  }

  /**
   * Récupère toutes les classes avec leurs détails
   */
  async obtenirClasses() {
    try {
      const requete = `
        SELECT 
          c.nom_classe,
          c.niveau,
          c.annee_scolaire,
          COUNT(e.matricule) as nombre_eleves
        FROM classe c
        LEFT JOIN eleve e ON c.nom_classe = e.nom_classe
        GROUP BY c.nom_classe, c.niveau, c.annee_scolaire
        ORDER BY c.niveau, c.nom_classe
      `;
      
      const classes = await db.query(requete);
      logger.info(`Récupération de ${classes.length} classes`);
      return classes;
    } catch (erreur) {
      logger.error('Erreur lors de la récupération des classes:', erreur);
      throw new Error('Échec de la récupération des classes');
    }
  }

  /**
   * Récupère les matières avec les informations des professeurs
   */
  async obtenirMatieres(nomClasse = null) {
    try {
      let requete = `
        SELECT 
          m.matiere_id,
          m.nom as nom_matiere,
          m.nom_classe,
          p.nom as nom_professeur,
          p.prenom as prenom_professeur,
          p.matricule as matricule_professeur,
          p.specialiste as specialite_professeur
        FROM matiere m
        JOIN professeur p ON m.professeur_matricule = p.matricule
      `;
      
      const parametres = [];
      if (nomClasse) {
        requete += ' WHERE m.nom_classe = ?';
        parametres.push(nomClasse);
      }
      
      requete += ' ORDER BY m.nom_classe, m.nom';
      
      const matieres = await db.query(requete, parametres);
      logger.info(`Récupération de ${matieres.length} matières${nomClasse ? ` pour la classe ${nomClasse}` : ''}`);
      return matieres;
    } catch (erreur) {
      logger.error('Erreur lors de la récupération des matières:', erreur);
      throw new Error('Échec de la récupération des matières');
    }
  }

  /**
   * Récupère l'emploi du temps complet d'une classe
   */
  async obtenirEmploiTempsClasse(nomClasse) {
    try {
      if (!nomClasse) {
        throw new Error('Le nom de la classe est requis');
      }

      const requete = `
        SELECT
          et.id,
          et.nom_classe,
          et.jour_semaine,
          TIME_FORMAT(et.heure_debut, '%H:%i') as heure_debut,
          TIME_FORMAT(et.heure_fin, '%H:%i') as heure_fin,
          COALESCE(s.nom_salle, et.salle) as salle,
          et.salle_id,
          s.type_salle,
          s.capacite,
          m.nom as nom_matiere,
          m.matiere_id,
          p.nom as nom_professeur,
          p.prenom as prenom_professeur,
          p.matricule as matricule_professeur
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        LEFT JOIN salle s ON et.salle_id = s.salle_id
        WHERE et.nom_classe = ?
        ORDER BY
          FIELD(et.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'),
          et.heure_debut
      `;

      const emploiTemps = await db.query(requete, [nomClasse]);
      
      // Structure les données pour une consommation facile côté frontend
      const emploiStructure = this.structurerEmploiTemps(emploiTemps);
      
      logger.info(`Récupération de l'emploi du temps pour la classe ${nomClasse} avec ${emploiTemps.length} créneaux`);
      return {
        nomClasse,
        creneaux: emploiTemps,
        structure: emploiStructure,
        joursSemaine: this.joursSemaine,
        creneauxDisponibles: this.creneauxHoraires
      };
    } catch (erreur) {
      logger.error(`Erreur lors de la récupération de l'emploi du temps pour la classe ${nomClasse}:`, erreur);
      throw new Error(`Échec de la récupération de l'emploi du temps pour la classe ${nomClasse}`);
    }
  }

  /**
   * Récupère tous les créneaux d'un jour spécifique pour toutes les classes
   */
  async obtenirEmploiTempsParJour(jourSemaine) {
    try {
      if (!jourSemaine) {
        throw new Error('Le jour de la semaine est requis');
      }

      const requete = `
        SELECT
          et.id,
          et.nom_classe,
          et.jour_semaine,
          TIME_FORMAT(et.heure_debut, '%H:%i') as heure_debut,
          TIME_FORMAT(et.heure_fin, '%H:%i') as heure_fin,
          COALESCE(s.nom_salle, et.salle) as salle,
          et.salle_id,
          s.type_salle,
          s.capacite,
          m.nom as nom_matiere,
          m.matiere_id,
          p.nom as nom_professeur,
          p.prenom as prenom_professeur,
          p.matricule as matricule_professeur
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        LEFT JOIN salle s ON et.salle_id = s.salle_id
        WHERE et.jour_semaine = ?
        ORDER BY
          et.nom_classe,
          et.heure_debut
      `;

      const emploiTemps = await db.query(requete, [jourSemaine]);

      logger.info(`Récupération de l'emploi du temps pour ${jourSemaine} avec ${emploiTemps.length} créneaux`);
      return {
        jourSemaine,
        creneaux: emploiTemps,
        joursSemaine: this.joursSemaine,
        creneauxDisponibles: this.creneauxHoraires
      };
    } catch (erreur) {
      logger.error(`Erreur lors de la récupération de l'emploi du temps pour ${jourSemaine}:`, erreur);
      throw new Error(`Échec de la récupération de l'emploi du temps pour ${jourSemaine}`);
    }
  }

  /**
   * Structure les données de l'emploi du temps en format grille
   */
  structurerEmploiTemps(emploiTemps) {
    const structure = {};
    
    // Initialise la structure
    this.joursSemaine.forEach(jour => {
      structure[jour] = {};
      this.creneauxHoraires.forEach(creneau => {
        structure[jour][creneau.debut] = null;
      });
    });

    // Remplit avec les données réelles
    emploiTemps.forEach(creneau => {
      if (structure[creneau.jour_semaine] && structure[creneau.jour_semaine][creneau.heure_debut] !== undefined) {
        structure[creneau.jour_semaine][creneau.heure_debut] = {
          id: creneau.id,
          matiere: creneau.nom_matiere,
          professeur: `${creneau.prenom_professeur} ${creneau.nom_professeur}`,
          salle: creneau.salle,
          heureDebut: creneau.heure_debut,
          heureFin: creneau.heure_fin,
          matriculeProfesseur: creneau.matricule_professeur,
          matiereId: creneau.matiere_id
        };
      }
    });

    return structure;
  }

  /**
   * Valide les données d'un nouveau créneau
   */
  validerCreneau(donneesCreneau) {
    const { nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle, salle_id } = donneesCreneau;

    const erreurs = [];

    if (!nom_classe) erreurs.push('Le nom de la classe est requis');
    if (!matiere_id) erreurs.push('L\'ID de la matière est requis');
    if (!jour_semaine) erreurs.push('Le jour de la semaine est requis');
    if (!heure_debut) erreurs.push('L\'heure de début est requise');
    if (!heure_fin) erreurs.push('L\'heure de fin est requise');

    // Validation de la salle (soit salle_id soit salle requis)
    if (!salle_id && !salle) {
      erreurs.push('La salle est requise');
    }

    // Validation du jour de la semaine
    if (jour_semaine && !this.joursSemaine.includes(jour_semaine)) {
      erreurs.push('Jour de la semaine invalide');
    }

    // Validation des heures
    if (heure_debut && heure_fin) {
      const debut = new Date(`2000-01-01T${heure_debut}:00`);
      const fin = new Date(`2000-01-01T${heure_fin}:00`);
      
      if (debut >= fin) {
        erreurs.push('L\'heure de début doit être antérieure à l\'heure de fin');
      }
    }

    return erreurs;
  }

  /**
   * Vérifie les conflits d'horaires pour un créneau
   */
  async verifierConflits(donneesCreneau, idCreneau = null) {
    try {
      const { nom_classe, jour_semaine, heure_debut, heure_fin, matiere_id } = donneesCreneau;

      // Conflit de classe (même classe, même horaire)
      let requeteConflitClasse = `
        SELECT et.*, m.nom as nom_matiere
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        WHERE et.nom_classe = ? AND et.jour_semaine = ?
        AND (
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut >= ? AND et.heure_fin <= ?)
        )
      `;

      const parametresClasse = [nom_classe, jour_semaine, heure_fin, heure_debut, heure_debut, heure_fin, heure_debut, heure_fin];

      if (idCreneau) {
        requeteConflitClasse += ' AND et.id != ?';
        parametresClasse.push(idCreneau);
      }

      const conflitsClasse = await db.query(requeteConflitClasse, parametresClasse);

      // Conflit de professeur (même professeur, même horaire)
      let requeteConflitProfesseur = `
        SELECT et.*, m.nom as nom_matiere, p.nom as nom_professeur, p.prenom as prenom_professeur
        FROM emploi_temps et
        JOIN matiere m ON et.matiere_id = m.matiere_id
        JOIN professeur p ON m.professeur_matricule = p.matricule
        WHERE m.professeur_matricule = (
          SELECT professeur_matricule FROM matiere WHERE matiere_id = ?
        )
        AND et.jour_semaine = ?
        AND (
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut < ? AND et.heure_fin > ?) OR
          (et.heure_debut >= ? AND et.heure_fin <= ?)
        )
      `;

      const parametresProfesseur = [matiere_id, jour_semaine, heure_fin, heure_debut, heure_debut, heure_fin, heure_debut, heure_fin];

      if (idCreneau) {
        requeteConflitProfesseur += ' AND et.id != ?';
        parametresProfesseur.push(idCreneau);
      }

      const conflitsProfesseur = await db.query(requeteConflitProfesseur, parametresProfesseur);

      return {
        conflitsClasse,
        conflitsProfesseur,
        aConflits: conflitsClasse.length > 0 || conflitsProfesseur.length > 0
      };
    } catch (erreur) {
      logger.error('Erreur lors de la vérification des conflits:', erreur);
      throw new Error('Échec de la vérification des conflits');
    }
  }

  /**
   * Ajoute un nouveau créneau à l'emploi du temps
   */
  async ajouterCreneau(donneesCreneau) {
    try {
      // Validation des données
      const erreurs = this.validerCreneau(donneesCreneau);
      if (erreurs.length > 0) {
        throw new Error(`Données invalides: ${erreurs.join(', ')}`);
      }

      // Vérification des conflits
      const conflits = await this.verifierConflits(donneesCreneau);
      if (conflits.aConflits) {
        const messageConflits = [];
        if (conflits.conflitsClasse.length > 0) {
          messageConflits.push(`Conflit de classe détecté avec ${conflits.conflitsClasse[0].nom_matiere}`);
        }
        if (conflits.conflitsProfesseur.length > 0) {
          messageConflits.push(`Conflit de professeur détecté`);
        }
        throw new Error(messageConflits.join('. '));
      }

      // Insertion du créneau
      const { nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle, salle_id } = donneesCreneau;

      let requete, params;
      if (salle_id) {
        // Utiliser le nouveau système avec salle_id
        requete = `
          INSERT INTO emploi_temps (nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle_id)
          VALUES (?, ?, ?, ?, ?, ?)
        `;
        params = [nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle_id];
      } else {
        // Compatibilité avec l'ancien système
        requete = `
          INSERT INTO emploi_temps (nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle)
          VALUES (?, ?, ?, ?, ?, ?)
        `;
        params = [nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle];
      }

      const resultat = await db.query(requete, params);

      logger.info(`Nouveau créneau ajouté avec l'ID ${resultat.insertId} pour la classe ${nom_classe}`);
      return { id: resultat.insertId, success: true };
    } catch (erreur) {
      logger.error('Erreur lors de l\'ajout du créneau:', erreur);
      throw erreur;
    }
  }

  /**
   * Met à jour un créneau existant
   */
  async modifierCreneau(idCreneau, donneesCreneau) {
    try {
      // Validation des données
      const erreurs = this.validerCreneau(donneesCreneau);
      if (erreurs.length > 0) {
        throw new Error(`Données invalides: ${erreurs.join(', ')}`);
      }

      // Vérification des conflits (en excluant le créneau actuel)
      const conflits = await this.verifierConflits(donneesCreneau, idCreneau);
      if (conflits.aConflits) {
        const messageConflits = [];
        if (conflits.conflitsClasse.length > 0) {
          messageConflits.push(`Conflit de classe détecté avec ${conflits.conflitsClasse[0].nom_matiere}`);
        }
        if (conflits.conflitsProfesseur.length > 0) {
          messageConflits.push(`Conflit de professeur détecté`);
        }
        throw new Error(messageConflits.join('. '));
      }

      // Mise à jour du créneau
      const { nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle, salle_id } = donneesCreneau;

      let requete, params;
      if (salle_id) {
        // Utiliser le nouveau système avec salle_id
        requete = `
          UPDATE emploi_temps
          SET nom_classe = ?, matiere_id = ?, jour_semaine = ?, heure_debut = ?, heure_fin = ?, salle_id = ?, salle = NULL
          WHERE id = ?
        `;
        params = [nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle_id, idCreneau];
      } else {
        // Compatibilité avec l'ancien système
        requete = `
          UPDATE emploi_temps
          SET nom_classe = ?, matiere_id = ?, jour_semaine = ?, heure_debut = ?, heure_fin = ?, salle = ?, salle_id = NULL
          WHERE id = ?
        `;
        params = [nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle, idCreneau];
      }

      await db.query(requete, params);

      logger.info(`Créneau ${idCreneau} modifié avec succès`);
      return { success: true };
    } catch (erreur) {
      logger.error(`Erreur lors de la modification du créneau ${idCreneau}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Supprime un créneau de l'emploi du temps
   */
  async supprimerCreneau(idCreneau) {
    try {
      const requete = 'DELETE FROM emploi_temps WHERE id = ?';
      const resultat = await db.query(requete, [idCreneau]);

      if (resultat.affectedRows === 0) {
        throw new Error('Créneau non trouvé');
      }

      logger.info(`Créneau ${idCreneau} supprimé avec succès`);
      return { success: true };
    } catch (erreur) {
      logger.error(`Erreur lors de la suppression du créneau ${idCreneau}:`, erreur);
      throw erreur;
    }
  }

  /**
   * Obtient les statistiques de l'emploi du temps
   */
  async obtenirStatistiques() {
    try {
      const requetes = [
        'SELECT COUNT(*) as total_creneaux FROM emploi_temps',
        'SELECT COUNT(DISTINCT nom_classe) as classes_avec_emploi FROM emploi_temps',
        'SELECT COUNT(DISTINCT matiere_id) as matieres_programmees FROM emploi_temps',
        `SELECT jour_semaine, COUNT(*) as nombre_creneaux
         FROM emploi_temps
         GROUP BY jour_semaine
         ORDER BY FIELD(jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi')`
      ];

      const [totalCreneaux, classesAvecEmploi, matieresProgrammees, repartitionJours] = await Promise.all(
        requetes.map(requete => db.query(requete))
      );

      return {
        totalCreneaux: totalCreneaux[0].total_creneaux,
        classesAvecEmploi: classesAvecEmploi[0].classes_avec_emploi,
        matieresProgrammees: matieresProgrammees[0].matieres_programmees,
        repartitionJours
      };
    } catch (erreur) {
      logger.error('Erreur lors de la récupération des statistiques:', erreur);
      throw new Error('Échec de la récupération des statistiques');
    }
  }
}

module.exports = EmploiTempsService;
