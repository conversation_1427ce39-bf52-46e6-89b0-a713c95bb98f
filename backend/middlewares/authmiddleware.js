exports.isAuthenticated = (req, res, next) => {
  if (req.session && req.session.user) return next();
  return res.redirect('/');
};

// exports.isAdmin = (req, res, next) => {
//   if (req.session.user && req.session.user.role === 'admin') return next();
//   return res.status(403).send('Accès refusé');
// };

exports.isEleve = (req, res, next) => {
  if (req.session.user && req.session.user.role === 'eleve') return next();
  return res.status(403).send('Accès refusé');
};

// exports.isParent = (req, res, next) => {
//   if (req.session.user && req.session.user.role === 'parent') return next();
//   return res.status(403).send('Accès refusé');
// };

exports.isProf = (req, res, next) => {
  if (req.session.user && req.session.user.role === 'professeur') return next();
  return res.status(403).send('Accès refusé');
};
