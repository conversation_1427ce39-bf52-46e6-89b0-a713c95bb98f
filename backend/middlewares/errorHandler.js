/**
 * Professional Error Handling Middleware for NS School Manager
 * Provides comprehensive error handling and logging
 */

const logger = require('../utils/logger');
const config = require('../config/config');

/**
 * Custom Application Error class
 */
class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Async error wrapper to catch async errors
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Database error handler
 */
const handleDatabaseError = (error) => {
  let message = 'Database operation failed';
  let statusCode = 500;

  // MySQL specific errors
  if (error.code) {
    switch (error.code) {
      case 'ER_DUP_ENTRY':
        message = 'Duplicate entry. This record already exists.';
        statusCode = 409;
        break;
      case 'ER_NO_REFERENCED_ROW_2':
        message = 'Referenced record does not exist.';
        statusCode = 400;
        break;
      case 'ER_ROW_IS_REFERENCED_2':
        message = 'Cannot delete record. It is referenced by other records.';
        statusCode = 400;
        break;
      case 'ER_BAD_FIELD_ERROR':
        message = 'Invalid field in database query.';
        statusCode = 400;
        break;
      case 'ER_PARSE_ERROR':
        message = 'Database query syntax error.';
        statusCode = 500;
        break;
      case 'ECONNREFUSED':
        message = 'Database connection refused.';
        statusCode = 503;
        break;
      case 'ETIMEDOUT':
        message = 'Database connection timeout.';
        statusCode = 503;
        break;
      default:
        message = `Database error: ${error.message}`;
    }
  }

  return new AppError(message, statusCode);
};

/**
 * Validation error handler
 */
const handleValidationError = (error) => {
  const message = `Validation failed: ${error.message}`;
  return new AppError(message, 400);
};

/**
 * JWT error handler
 */
const handleJWTError = (error) => {
  let message = 'Authentication failed';
  
  if (error.name === 'JsonWebTokenError') {
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    message = 'Token expired';
  }
  
  return new AppError(message, 401);
};

/**
 * File upload error handler
 */
const handleMulterError = (error) => {
  let message = 'File upload failed';
  let statusCode = 400;

  if (error.code === 'LIMIT_FILE_SIZE') {
    message = 'File too large';
    statusCode = 413;
  } else if (error.code === 'LIMIT_FILE_COUNT') {
    message = 'Too many files';
    statusCode = 400;
  } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    message = 'Unexpected file field';
    statusCode = 400;
  }

  return new AppError(message, statusCode);
};

/**
 * Send error response in development
 */
const sendErrorDev = (err, res) => {
  res.status(err.statusCode).json({
    success: false,
    error: {
      message: err.message,
      stack: err.stack,
      timestamp: err.timestamp,
      statusCode: err.statusCode
    }
  });
};

/**
 * Send error response in production
 */
const sendErrorProd = (err, res) => {
  // Operational errors: send message to client
  if (err.isOperational) {
    res.status(err.statusCode).json({
      success: false,
      message: err.message,
      timestamp: err.timestamp
    });
  } else {
    // Programming errors: don't leak error details
    logger.error('Programming error', err);
    
    res.status(500).json({
      success: false,
      message: 'Something went wrong on our end',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Global error handling middleware
 */
const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.timestamp = err.timestamp || new Date().toISOString();

  // Log the error
  logger.error('Application error', err, {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    matricule: req.session?.matricule,
    body: req.method === 'POST' ? req.body : undefined
  });

  let error = { ...err };
  error.message = err.message;

  // Handle specific error types
  if (err.code && err.code.startsWith('ER_')) {
    error = handleDatabaseError(err);
  } else if (err.name === 'ValidationError') {
    error = handleValidationError(err);
  } else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    error = handleJWTError(err);
  } else if (err.name === 'MulterError') {
    error = handleMulterError(err);
  }

  // Send error response
  if (config.app.env === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
};

/**
 * 404 Not Found handler
 */
const notFoundHandler = (req, res, next) => {
  // Filter out Chrome DevTools requests to reduce noise in logs
  if (req.originalUrl.includes('.well-known/appspecific/com.chrome.devtools')) {
    return res.status(404).json({ error: 'Not found' });
  }

  const message = `Route ${req.originalUrl} not found`;
  const error = new AppError(message, 404);
  next(error);
};

/**
 * Request logging middleware
 */
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.request(req, res, duration);
  });
  
  next();
};

/**
 * Rate limiting error handler
 */
const rateLimitHandler = (req, res) => {
  const error = new AppError('Too many requests, please try again later', 429);
  
  logger.warn('Rate limit exceeded', {
    ip: req.ip,
    url: req.url,
    userAgent: req.get('User-Agent')
  });

  res.status(429).json({
    success: false,
    message: error.message,
    timestamp: error.timestamp
  });
};

/**
 * Graceful shutdown handler
 */
const gracefulShutdown = (server, signal) => {
  logger.info(`${signal} received, starting graceful shutdown`);
  
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });

  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

module.exports = {
  AppError,
  asyncHandler,
  globalErrorHandler,
  notFoundHandler,
  requestLogger,
  rateLimitHandler,
  gracefulShutdown,
  handleDatabaseError,
  handleValidationError,
  handleJWTError,
  handleMulterError
};
