const connection = require('../models/db');

/**
 * Middleware pour vérifier l'accès aux fichiers de cours
 * @param {string} fileType - Type de fichier ('cours', 'devoir', etc.)
 */
function checkFileAccess(fileType = 'cours') {
  return (req, res, next) => {
    const fileId = req.params.id;
    const userMatricule = req.session.matricule;
    const userRole = req.session.role;

    if (!userMatricule || !userRole) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentification requise.' 
      });
    }

    if (fileType === 'cours') {
      checkCoursAccess(fileId, userMatricule, userRole, req, res, next);
    } else {
      return res.status(400).json({ 
        success: false, 
        message: 'Type de fichier non supporté.' 
      });
    }
  };
}

/**
 * Vérifier l'accès à un cours
 */
function checkCoursAccess(coursId, userMatricule, userRole, req, res, next) {
  const getCours = `
    SELECT c.cours_id, c.fichier, c.titre, c.professeur_matricule, c.nom_classe, c.matiere_id,
           p.nom as professeur_nom, p.prenom as professeur_prenom,
           m.nom as matiere_nom
    FROM cours c 
    JOIN professeur p ON c.professeur_matricule = p.matricule
    JOIN matiere m ON c.matiere_id = m.matiere_id
    WHERE c.cours_id = ?
  `;

  connection.query(getCours, [coursId], (err, coursResult) => {
    if (err) {
      console.error('Erreur récupération cours:', err);
      return res.status(500).json({ 
        success: false, 
        message: 'Erreur serveur lors de la vérification d\'accès.' 
      });
    }

    if (coursResult.length === 0) {
      return res.status(404).json({ 
        success: false, 
        message: 'Cours non trouvé.' 
      });
    }

    const cours = coursResult[0];
    req.cours = cours; // Ajouter les infos du cours à la requête

    // Vérifier les autorisations selon le rôle
    if (userRole === 'admin') {
      // L'admin a accès à tout
      return next();
    } else if (userRole === 'professeur') {
      // Le professeur peut accéder à ses propres cours
      if (cours.professeur_matricule === userMatricule) {
        return next();
      } else {
        return res.status(403).json({ 
          success: false, 
          message: 'Vous n\'êtes pas autorisé à accéder à ce cours.' 
        });
      }
    } else if (userRole === 'eleve') {
      // L'élève peut accéder aux cours de sa classe
      const getEleveClasse = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
      connection.query(getEleveClasse, [userMatricule], (err, eleveResult) => {
        if (err || eleveResult.length === 0) {
          console.error('Erreur récupération classe élève:', err);
          return res.status(500).json({ 
            success: false, 
            message: 'Erreur lors de la vérification de votre classe.' 
          });
        }

        const eleveClasse = eleveResult[0].nom_classe;
        if (cours.nom_classe === eleveClasse) {
          return next();
        } else {
          return res.status(403).json({ 
            success: false, 
            message: 'Ce cours n\'est pas accessible pour votre classe.' 
          });
        }
      });
    } else {
      return res.status(403).json({ 
        success: false, 
        message: 'Rôle non autorisé pour l\'accès aux fichiers.' 
      });
    }
  });
}

/**
 * Middleware pour enregistrer les activités de téléchargement
 */
function logDownloadActivity(req, res, next) {
  const originalSend = res.sendFile;
  
  res.sendFile = function(filePath, options, callback) {
    // Enregistrer l'activité de téléchargement
    if (req.cours && req.session.matricule) {
      const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut) 
                            VALUES (?, ?, NOW(), ?)`;
      connection.query(activiteQuery, [
        req.session.matricule, 
        `Téléchargement cours: ${req.cours.titre} (${req.cours.matiere_nom})`, 
        'Succès'
      ], (err) => {
        if (err) console.error('Erreur enregistrement activité:', err);
      });
    }

    // Appeler la méthode originale
    return originalSend.call(this, filePath, options, callback);
  };

  next();
}

/**
 * Middleware pour vérifier la limite de téléchargements
 * @param {number} maxDownloads - Nombre maximum de téléchargements par heure
 */
function rateLimitDownloads(maxDownloads = 50) {
  const downloadCounts = new Map();

  return (req, res, next) => {
    const userMatricule = req.session.matricule;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (!downloadCounts.has(userMatricule)) {
      downloadCounts.set(userMatricule, []);
    }

    const userDownloads = downloadCounts.get(userMatricule);
    
    // Nettoyer les téléchargements de plus d'une heure
    const recentDownloads = userDownloads.filter(timestamp => now - timestamp < oneHour);
    downloadCounts.set(userMatricule, recentDownloads);

    if (recentDownloads.length >= maxDownloads) {
      return res.status(429).json({ 
        success: false, 
        message: `Limite de téléchargements atteinte. Maximum ${maxDownloads} téléchargements par heure.` 
      });
    }

    // Ajouter ce téléchargement
    recentDownloads.push(now);
    downloadCounts.set(userMatricule, recentDownloads);

    next();
  };
}

/**
 * Middleware pour sécuriser les noms de fichiers
 */
function sanitizeFilename(req, res, next) {
  if (req.cours && req.cours.fichier) {
    const originalName = req.cours.fichier.split('-').slice(1).join('-');
    const safeFilename = originalName.replace(/[^a-zA-Z0-9.-]/g, '_');
    req.safeFilename = safeFilename;
  }
  next();
}

module.exports = {
  checkFileAccess,
  logDownloadActivity,
  rateLimitDownloads,
  sanitizeFilename
};
