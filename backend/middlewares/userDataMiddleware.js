/**
 * User Data Middleware for NS School Manager
 * Fetches user data for topbar display across all pages
 */

const { connection } = require('../models/db');

// Middleware to fetch user data for topbar display
const fetchUserData = (req, res, next) => {
  if (!req.session || !req.session.matricule || !req.session.role) {
    return next();
  }

  const matricule = req.session.matricule;
  const role = req.session.role;

  let query;
  switch (role) {
    case 'admin':
      query = 'SELECT matricule, nom, prenom, email, sexe, telephone FROM admin WHERE matricule = ?';
      break;
    case 'professeur':
      query = 'SELECT matricule, nom, prenom, email, sexe, telephone, specialiste FROM professeur WHERE matricule = ?';
      break;
    case 'eleve':
      query = 'SELECT matricule, nom, prenom, sexe, date_naissance, nom_classe FROM eleve WHERE matricule = ?';
      break;
    default:
      return next();
  }

  connection.query(query, [matricule], (err, results) => {
    if (err) {
      console.error('Error fetching user data:', err);
      return next();
    }

    if (results.length > 0) {
      const userData = results[0];
      
      // Create standardized user object for templates
      res.locals.currentUser = {
        matricule: userData.matricule,
        nom: userData.nom || '',
        prenom: userData.prenom || '',
        email: userData.email || '',
        sexe: userData.sexe || '',
        telephone: userData.telephone || '',
        specialiste: userData.specialiste || '',
        date_naissance: userData.date_naissance || '',
        nom_classe: userData.nom_classe || '',
        role: role,
        fullName: `${userData.prenom || ''} ${userData.nom || ''}`.trim(),
        initials: `${(userData.prenom || '').charAt(0)}${(userData.nom || '').charAt(0)}`.toUpperCase() || 
                  (role === 'admin' ? 'AD' : role === 'professeur' ? 'PR' : 'EL')
      };

      // Also get notification count
      const notificationQuery = 'SELECT COUNT(*) AS count FROM message WHERE receiver_matricule = ? AND lu = 0';
      connection.query(notificationQuery, [matricule], (err, notifResults) => {
        if (!err && notifResults.length > 0) {
          res.locals.currentUser.notificationsCount = notifResults[0].count;
        } else {
          res.locals.currentUser.notificationsCount = 0;
        }
        next();
      });
    } else {
      // Create default user object if no data found
      res.locals.currentUser = {
        matricule: matricule,
        nom: '',
        prenom: '',
        email: '',
        role: role,
        fullName: role === 'admin' ? 'Administrateur' : role === 'professeur' ? 'Professeur' : 'Élève',
        initials: role === 'admin' ? 'AD' : role === 'professeur' ? 'PR' : 'EL',
        notificationsCount: 0
      };
      next();
    }
  });
};

module.exports = {
  fetchUserData
};
