/**
 * Hybrid Authentication Middleware for NS School Manager
 * Supports both session-based (web) and JWT-based (mobile) authentication
 */

const { verifyToken, extractTokenFromHeader } = require('../utils/jwt');
const logger = require('../utils/logger');

/**
 * Hybrid authentication middleware that checks both session and JWT
 * @param {Object} options - Configuration options
 * @param {boolean} options.requireAuth - Whether authentication is required
 * @param {Array} options.roles - Allowed roles (optional)
 * @param {boolean} options.apiOnly - Only allow JWT authentication (for API endpoints)
 * @returns {Function} Express middleware function
 */
function hybridAuth(options = {}) {
  const { requireAuth = true, roles = [], apiOnly = false } = options;

  return (req, res, next) => {
    let user = null;
    let authMethod = null;

    // Try JWT authentication first (for mobile/API clients)
    const authHeader = req.headers.authorization;
    if (authHeader) {
      try {
        const token = extractTokenFromHeader(authHeader);
        if (token) {
          const decoded = verifyToken(token);
          user = {
            matricule: decoded.matricule,
            role: decoded.role
          };
          authMethod = 'jwt';
          req.user = user;
          req.authMethod = authMethod;
        }
      } catch (error) {
        // JWT verification failed, but don't return error yet
        // We'll try session auth next (unless apiOnly is true)
        if (apiOnly) {
          return res.status(401).json({
            success: false,
            message: 'Invalid or expired token',
            error: 'INVALID_TOKEN'
          });
        }
      }
    }

    // Try session authentication (for web clients) if JWT failed and not API-only
    if (!user && !apiOnly && req.session && req.session.matricule && req.session.role) {
      user = {
        matricule: req.session.matricule,
        role: req.session.role
      };
      authMethod = 'session';
      req.user = user;
      req.authMethod = authMethod;
    }

    // Check if authentication is required
    if (requireAuth && !user) {
      if (apiOnly || req.headers.authorization) {
        // API request or JWT attempt failed
        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          error: 'AUTHENTICATION_REQUIRED'
        });
      } else {
        // Web request - redirect to login
        return res.redirect('/');
      }
    }

    // Check role permissions if specified
    if (user && roles.length > 0 && !roles.includes(user.role)) {
      if (apiOnly || req.headers.authorization) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
          error: 'INSUFFICIENT_PERMISSIONS'
        });
      } else {
        return res.status(403).send('Accès refusé');
      }
    }

    // Authentication successful or not required
    next();
  };
}

/**
 * Convenience middleware for API-only authentication
 */
function apiAuth(options = {}) {
  return hybridAuth({ ...options, apiOnly: true });
}

/**
 * Convenience middleware for web-only authentication (session only)
 */
function webAuth(options = {}) {
  return (req, res, next) => {
    const { requireAuth = true, roles = [] } = options;

    // Only check session authentication
    if (requireAuth && (!req.session || !req.session.matricule || !req.session.role)) {
      return res.redirect('/');
    }

    if (req.session && req.session.matricule && req.session.role) {
      req.user = {
        matricule: req.session.matricule,
        role: req.session.role
      };
      req.authMethod = 'session';

      // Check role permissions
      if (roles.length > 0 && !roles.includes(req.user.role)) {
        return res.status(403).send('Accès refusé');
      }
    }

    next();
  };
}

/**
 * Role-specific middleware generators
 */
const requireRole = (role) => hybridAuth({ requireAuth: true, roles: [role] });
const requireRoleAPI = (role) => apiAuth({ requireAuth: true, roles: [role] });

// Specific role middlewares
const requireAdmin = () => requireRole('admin');
const requireProfesseur = () => requireRole('professeur');
const requireEleve = () => requireRole('eleve');

const requireAdminAPI = () => requireRoleAPI('admin');
const requireProfesseurAPI = () => requireRoleAPI('professeur');
const requireEleveAPI = () => requireRoleAPI('eleve');

/**
 * Optional authentication middleware (doesn't require auth but sets user if available)
 */
const optionalAuth = () => hybridAuth({ requireAuth: false });

module.exports = {
  hybridAuth,
  apiAuth,
  webAuth,
  requireRole,
  requireRoleAPI,
  requireAdmin,
  requireProfesseur,
  requireEleve,
  requireAdminAPI,
  requireProfesseurAPI,
  requireEleveAPI,
  optionalAuth
};
