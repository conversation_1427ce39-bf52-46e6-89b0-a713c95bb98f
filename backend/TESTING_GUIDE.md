# 🧪 NS School Manager - Testing & Utility Guide

This guide provides step-by-step instructions for testing the NS School Manager application with sample data.

## 🚀 Quick Start

### 1. Setup Database with Sample Data

```bash
# Navigate to backend directory
cd backend

# Install dependencies (if not done)
npm install

# Setup database tables
node models/setup.js

# Seed database with sample data
node seed.js
```

### 2. Start the Application

```bash
# Development mode with auto-reload
npm run dev

# Or production mode
npm start
```

The application will be available at:
- **Student/Teacher Login**: http://localhost:8080/
- **Admin Login**: http://localhost:8080/ad

## 🔑 Test Accounts

### 👨‍💼 Admin Accounts

| Matricule | Password | Role | Access URL |
|-----------|----------|------|------------|
| `ADM001` | `admin123` | Admin | http://localhost:8080/ad |
| `ADM002` | `admin123` | Admin | http://localhost:8080/ad |

**Admin Features to Test:**
- User management (create/edit/delete users)
- Class management
- Subject assignments
- System parameters
- Reports and analytics
- Messaging system

### 👨‍🏫 Teacher Accounts

| Matricule | Password | Subject | Classes | Access URL |
|-----------|----------|---------|---------|------------|
| `PROF001` | `prof123` | Mathématiques | 6eme A, 6eme B | http://localhost:8080/ |
| `PROF002` | `prof123` | Français | 6eme A, 5eme A | http://localhost:8080/ |
| `PROF003` | `prof123` | Sciences Physiques | 4eme A, 3eme A | http://localhost:8080/ |
| `PROF004` | `prof123` | Histoire-Géographie | 6eme A, 5eme A | http://localhost:8080/ |

**Teacher Features to Test:**
- Course upload/management
- Grade entry
- Attendance tracking
- Assignment creation
- Student messaging
- Class reports

### 👨‍🎓 Student Accounts

| Matricule | Password | Name | Class | Access URL |
|-----------|----------|------|-------|------------|
| `ELV001` | `eleve123` | Pierre Mballa | 6eme A | http://localhost:8080/ |
| `ELV002` | `eleve123` | Aminata Fouda | 6eme A | http://localhost:8080/ |
| `ELV003` | `eleve123` | Claude Biya | 5eme A | http://localhost:8080/ |
| `ELV004` | `eleve123` | Grace Essomba | 4eme A | http://localhost:8080/ |
| `ELV005` | `eleve123` | David Atangana | 3eme A | http://localhost:8080/ |
| `ELV006` | `eleve123` | Fatima Nkomo | 6eme B | http://localhost:8080/ |

**Student Features to Test:**
- View courses and materials
- Check grades
- View attendance
- Submit assignments
- View announcements
- Download course materials

## 📋 Testing Scenarios

### Scenario 1: Admin Login and User Management

1. **Login as Admin**:
   - Go to http://localhost:8080/ad
   - Matricule: `ADM001`
   - Password: `admin123`

2. **Test Admin Features**:
   - Navigate to "Utilisateurs" to see all users
   - Try creating a new student/teacher
   - Edit existing user information
   - View system statistics

### Scenario 2: Teacher Course Management

1. **Login as Teacher**:
   - Go to http://localhost:8080/
   - Matricule: `PROF001`
   - Password: `prof123`
   - Role: Select "Professeur"

2. **Test Teacher Features**:
   - Navigate to "Cours" section
   - Upload a sample course file
   - View existing courses
   - Try downloading a course file

### Scenario 3: Student Course Access

1. **Login as Student**:
   - Go to http://localhost:8080/
   - Matricule: `ELV001`
   - Password: `eleve123`
   - Role: Select "Élève"

2. **Test Student Features**:
   - Navigate to "Cours" to see available courses
   - Try downloading course materials
   - Check "Notes" section for grades
   - View "Emploi du temps" for schedule

### Scenario 4: Cross-Role Interactions

1. **Teacher uploads course** (as PROF001)
2. **Student accesses course** (as ELV001)
3. **Admin monitors activity** (as ADM001)

## 🔧 Utility Commands

### Database Management

```bash
# Complete reset: Drop database, recreate tables, and seed data
npm run db:reset

# Clear existing data and add fresh seed data
npm run db:clear

# Keep existing data and add seeds
npm run db:seed-only

# Setup database tables only
npm run db:init

# Create individual users
node createUser.js
```

### Application Management

```bash
# Start development server
npm run dev

# Start production server
npm start

# Check application logs
tail -f logs/app.log

# Check error logs
tail -f logs/error.log
```

### Testing File Uploads

1. **Prepare test files**:
   - Create sample PDF files for courses
   - Ensure files are under 10MB
   - Use descriptive names

2. **Test upload as teacher**:
   - Login as any teacher account
   - Go to "Cours" section
   - Click "Ajouter un cours"
   - Upload your test file

3. **Verify download as student**:
   - Login as student in same class
   - Go to "Cours" section
   - Try downloading the uploaded file

## 🐛 Troubleshooting

### Common Issues

1. **Port 8080 already in use**:
   ```bash
   # Kill existing process
   lsof -ti:8080 | xargs kill -9
   
   # Or change port in .env file
   PORT=3000
   ```

2. **Database connection error**:
   - Check MySQL is running
   - Verify database credentials in `.env`
   - Ensure database exists

3. **Login not working**:
   - Verify you're using correct URL for role
   - Check credentials match seeded data
   - Clear browser cache/cookies

4. **File upload fails**:
   - Check file size (max 10MB)
   - Ensure uploads directory exists
   - Verify file permissions

### Debug Mode

```bash
# Enable debug logging
NODE_ENV=development npm run dev

# Check detailed logs
tail -f logs/app.log | grep ERROR
```

## 📊 Sample Data Overview

The seed data creates:
- **2 Admin accounts** with full system access
- **4 Teacher accounts** with different subjects
- **6 Student accounts** across different classes
- **5 Classes** from 6eme to 3eme
- **8 Subject assignments** linking teachers to classes
- **4 Sample courses** with downloadable materials
- **System parameters** for school configuration

## 🔄 Resetting Data

To start fresh:

```bash
# Complete reset (recommended)
npm run db:reset

# This will:
# 1. Drop and recreate the entire database
# 2. Create all tables with proper structure
# 3. Add all sample users with hashed passwords
# 4. Setup classes and subjects
# 5. Create sample courses
# 6. Configure system parameters
```

## 📞 Support

If you encounter issues:

1. Check the application logs in `logs/` directory
2. Verify database connection and data
3. Ensure all dependencies are installed
4. Check file permissions for uploads directory

For development questions, refer to the main README.md file.
