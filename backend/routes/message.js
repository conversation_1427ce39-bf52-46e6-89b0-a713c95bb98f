const express = require('express');
const router = express.Router();
const { connection } = require('../models/db');
const { fetchUserData } = require('../middlewares/userDataMiddleware');
const { promisify } = require('util');

// Promisify the query method for better error handling
const query = promisify(connection.query).bind(connection);

router.get('/message', fetchUserData, async (req, res) => {
  console.log('🔍 Message page - Session:', req.session);
  const matricule = req.session.matricule;
  const role = req.session.role;

  if (!matricule) {
    console.log('❌ Access denied - matricule:', matricule, 'role:', role);
    return res.redirect('/ad');
  }

  try {
    // Récupérer les professeurs pour pouvoir leur envoyer un message privé
    const profQuery = `
      SELECT u.matricule, p.nom, p.prenom, u.role
      FROM utilisateur u
      LEFT JOIN professeur p ON u.matricule = p.matricule
      WHERE u.role = 'professeur'
    `;
    const profs = await query(profQuery);

    // Récupérer les messages reçus par cet admin (privés ou annonces générales)
    const messagesQuery = `
      SELECT m.*,
             CASE
               WHEN m.sender_matricule != ? THEN
                 COALESCE(p.nom, a.nom, m.sender_matricule)
               ELSE 'Vous'
             END as sender_name,
             u.role as sender_role
      FROM message m
      LEFT JOIN utilisateur u ON m.sender_matricule = u.matricule
      LEFT JOIN professeur p ON m.sender_matricule = p.matricule
      LEFT JOIN admin a ON m.sender_matricule = a.matricule
      WHERE m.receiver_matricule = ?
         OR (m.nom_classe IS NULL AND m.type = 'annonce')
      ORDER BY m.date_envoi DESC
    `;
    const messages = await query(messagesQuery, [matricule, matricule]);

    // Créer des conversations groupées pour la nouvelle interface
    const conversationsMap = new Map();

    messages.forEach(message => {
      const isFromCurrentUser = message.sender_matricule === matricule;
      const participantId = isFromCurrentUser ? message.receiver_matricule : message.sender_matricule;
      const participantName = isFromCurrentUser ?
        (message.receiver_matricule || 'Classe') :
        message.sender_name;

      if (!conversationsMap.has(participantId)) {
        conversationsMap.set(participantId, {
          id: participantId,
          participant_name: participantName,
          participant_role: message.sender_role || 'utilisateur',
          last_message: message.contenu,
          last_message_time: message.date_envoi,
          unread_count: 0,
          type: message.type,
          messages: []
        });
      }

      const conversation = conversationsMap.get(participantId);
      conversation.messages.push(message);

      // Compter les messages non lus
      if (!message.lu && !isFromCurrentUser) {
        conversation.unread_count++;
      }
    });

    const conversations = Array.from(conversationsMap.values());

    // Fetch all available classes for announcements
    const classesQuery = `SELECT nom_classe FROM classe ORDER BY nom_classe`;
    const classes = await query(classesQuery);

    res.render('admin/message', {
      profs,
      messages,
      conversations,
      classes, // Add classes for compose-modal.ejs
      matricule: req.session.matricule,
      role: req.session.role
    });
  } catch (error) {
    console.error('❌ Erreur lors du chargement de la messagerie :', error.message);
    return res.status(500).send('Erreur lors du chargement de la messagerie.');
  }
});

module.exports = router;
