const express = require('express');
const router = express.Router();
const { fetchUserData } = require('../middlewares/userDataMiddleware');

router.get('/', fetchUserData, (req, res) => {
  res.render('admin/emploi_temps'); // correspond à frontend/views/admin/emploi_temps.ejs
});

router.get('/salles', fetchUserData, (req, res) => {
  res.render('admin/salles'); // correspond à frontend/views/admin/salles.ejs
});

module.exports = router;
