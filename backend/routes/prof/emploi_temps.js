const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Middleware: protection accès prof uniquement
router.use((req, res, next) => {
  if (!req.session || req.session.role !== 'professeur') {
    return res.redirect('/');
  }
  next();
});

// Route GET principale : affichage de l'emploi du temps du professeur
router.get('/', fetchUserData, async (req, res) => {
  try {
    const professeurMatricule = req.session.matricule;
    if (!professeurMatricule) return res.redirect('/');

    // Récupérer les informations du professeur
    const professeurQuery = `
      SELECT nom, prenom, specialiste 
      FROM professeur 
      WHERE matricule = ?
    `;
    const professeur = await query(professeurQuery, [professeurMatricule]);

    // Récupérer les classes enseignées par ce professeur
    const classesQuery = `
      SELECT DISTINCT c.nom_classe
      FROM enseignant_classe ec
      JOIN classe c ON ec.nom_classe = c.nom_classe
      WHERE ec.professeur_matricule = ?
      ORDER BY c.nom_classe
    `;
    const classes = await query(classesQuery, [professeurMatricule]);

    // Récupérer les matières enseignées par ce professeur
    const matieresQuery = `
      SELECT DISTINCT m.matiere_id, m.nom AS nom_matiere, m.nom_classe
      FROM matiere m
      WHERE m.professeur_matricule = ?
      ORDER BY m.nom_classe, m.nom
    `;
    const matieres = await query(matieresQuery, [professeurMatricule]);

    // Récupérer l'emploi du temps complet du professeur
    const emploiTempsQuery = `
      SELECT
        et.id,
        et.jour_semaine,
        et.heure_debut,
        et.heure_fin,
        et.nom_classe,
        m.nom AS nom_matiere,
        COALESCE(s.nom_salle, et.salle) AS salle_nom,
        et.salle_id
      FROM emploi_temps et
      JOIN matiere m ON et.matiere_id = m.matiere_id
      LEFT JOIN salle s ON et.salle_id = s.salle_id
      WHERE m.professeur_matricule = ?
      ORDER BY
        FIELD(et.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'),
        et.heure_debut
    `;
    const emploiTemps = await query(emploiTempsQuery, [professeurMatricule]);

    // Structurer les données par jour de la semaine
    const joursSemaine = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    const emploiStructure = {};
    
    joursSemaine.forEach(jour => {
      emploiStructure[jour] = emploiTemps.filter(creneau => creneau.jour_semaine === jour);
    });

    // Calculer les statistiques
    const stats = {
      totalCreneaux: emploiTemps.length,
      totalClasses: classes.length,
      totalMatieres: matieres.length,
      heuresParSemaine: emploiTemps.reduce((total, creneau) => {
        const debut = new Date(`1970-01-01T${creneau.heure_debut}`);
        const fin = new Date(`1970-01-01T${creneau.heure_fin}`);
        return total + (fin - debut) / (1000 * 60 * 60); // Convertir en heures
      }, 0)
    };

    res.render('prof/emploi_temps', {
      professeur: professeur[0] || {},
      classes,
      matieres,
      emploiTemps,
      emploiStructure,
      joursSemaine,
      stats,
      activites: [] // Pour la compatibilité avec les partials
    });

  } catch (error) {
    console.error('❌ Erreur lors du chargement de l\'emploi du temps:', error.message);
    return res.status(500).send('Erreur serveur');
  }
});

module.exports = router;
