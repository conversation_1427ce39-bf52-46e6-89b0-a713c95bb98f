const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Afficher la page de messagerie
router.get('/', fetchUserData, async (req, res) => {
  const matricule = req.session.matricule; // Assure-toi que le prof est connecté

  if (!matricule) {
    return res.redirect('/');
  }

  try {
    // Récupérer les admins (juste matricule et role)
    const adminQuery = `SELECT matricule FROM utilisateur WHERE role = 'admin'`;
    const admins = await query(adminQuery);

    // Récupérer les messages reçus par ce prof (privés ou annonces de ses classes)
    const messagesQuery = `
      SELECT * FROM message
      WHERE receiver_matricule = ?
         OR nom_classe IN (
            SELECT nom_classe
            FROM enseignant_classe
            WHERE professeur_matricule = ?
         )
      ORDER BY date_envoi DESC
    `;
    const messages = await query(messagesQuery, [matricule, matricule]);

    // Get all other professors (excluding the current user)
    const profsQuery = `SELECT u.matricule, p.nom, p.prenom, u.role FROM utilisateur u JOIN professeur p ON u.matricule = p.matricule WHERE u.role = 'professeur' AND u.matricule != ?`;
    const profs = await query(profsQuery, [matricule]);

    // Merge admins and profs into availableRecipients
    const availableRecipients = [
      ...admins.map(a => ({ ...a, role: 'admin' })),
      ...profs.map(p => ({ ...p, role: 'professeur' }))
    ];

    // Fetch classes assigned to this professor
    const classesQuery = `
      SELECT c.nom_classe
      FROM enseignant_classe ec
      JOIN classe c ON ec.nom_classe = c.nom_classe
      WHERE ec.professeur_matricule = ?
    `;
    const classes = await query(classesQuery, [matricule]);

    res.render('prof/message', {
      admins,
      messages,
      activites: [],
      availableRecipients, // for compose-modal.ejs
      userRole: 'professeur',
      matricule,
      classes // for compose-modal.ejs
    });
  } catch (err) {
    console.error('❌ Erreur SQL :', err.message);
    res.status(500).send('Erreur lors du chargement de la messagerie.');
  }
});

// Note: Message sending is now handled by /api/message/send endpoint
// This old endpoint is kept for backward compatibility but should not be used

module.exports = router;
