const express = require('express');
const router = express.Router();
const { query, connection } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Vérifier si le dossier d'upload existe, sinon le créer
const uploadDir = path.join(__dirname, '../../uploads/cours');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Filtrer les types de fichiers autorisés
const fileFilter = (req, file, cb) => {
  // Types de fichiers autorisés
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'application/zip',
    'application/x-zip-compressed'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Type de fichier non autorisé. Formats acceptés: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, JPG, PNG, ZIP'), false);
  }
};

// 📁 Configuration de multer pour l'upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Nettoyer le nom du fichier pour éviter les problèmes de sécurité
    const originalName = file.originalname.replace(/[^a-zA-Z0-9.]/g, '_');
    const uniqueName = Date.now() + '-' + originalName;
    cb(null, uniqueName);
  }
});

// Configuration de multer avec limites et filtres
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10 MB max
    files: 1 // Un seul fichier à la fois
  },
  fileFilter: fileFilter
});

// 🔐 Middleware de vérification
router.use((req, res, next) => {
  if (!req.session || req.session.role !== 'professeur') {
    return res.redirect('/');
  }
  next();
});

// 📥 Route POST : Ajouter un cours avec validation améliorée
router.post('/ajouter', (req, res) => {
  upload.single('fichier')(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          success: false,
          message: 'Le fichier est trop volumineux. Taille maximale: 10 MB.'
        });
      }
      return res.status(400).json({
        success: false,
        message: 'Erreur lors de l\'upload: ' + err.message
      });
    } else if (err) {
      return res.status(400).json({
        success: false,
        message: err.message
      });
    }

    // Validation des champs requis
    const { titre, matiere_id, nom_classe } = req.body;

    if (!titre || !matiere_id || !nom_classe) {
      // Supprimer le fichier uploadé en cas d'erreur de validation
      if (req.file) {
        fs.unlink(req.file.path, (unlinkErr) => {
          if (unlinkErr) console.error('Erreur suppression fichier:', unlinkErr);
        });
      }
      return res.status(400).json({
        success: false,
        message: 'Titre, matière et classe sont requis.'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Aucun fichier sélectionné.'
      });
    }

    const fichier = req.file.filename;
    const professeur_matricule = req.session.matricule;

    // Vérifier que le professeur enseigne bien cette matière dans cette classe
    const verifyTeacher = `
      SELECT m.matiere_id
      FROM matiere m
      WHERE m.matiere_id = ?
        AND m.professeur_matricule = ?
        AND m.nom_classe = ?
    `;

    connection.query(verifyTeacher, [matiere_id, professeur_matricule, nom_classe], (err, results) => {
      if (err) {
        console.error('Erreur vérification professeur:', err);
        // Supprimer le fichier uploadé
        fs.unlink(req.file.path, (unlinkErr) => {
          if (unlinkErr) console.error('Erreur suppression fichier:', unlinkErr);
        });
        return res.status(500).json({
          success: false,
          message: 'Erreur serveur lors de la vérification.'
        });
      }

      if (results.length === 0) {
        // Supprimer le fichier uploadé
        fs.unlink(req.file.path, (unlinkErr) => {
          if (unlinkErr) console.error('Erreur suppression fichier:', unlinkErr);
        });
        return res.status(403).json({
          success: false,
          message: 'Vous n\'êtes pas autorisé à ajouter un cours pour cette matière/classe.'
        });
      }

      // Insérer le cours dans la base de données
      const sql = `
        INSERT INTO cours (titre, fichier, professeur_matricule, matiere_id, nom_classe)
        VALUES (?, ?, ?, ?, ?)
      `;

      connection.query(sql, [titre, fichier, professeur_matricule, matiere_id, nom_classe], (err, result) => {
        if (err) {
          console.error('Erreur ajout cours :', err);
          // Supprimer le fichier uploadé en cas d'erreur
          fs.unlink(req.file.path, (unlinkErr) => {
            if (unlinkErr) console.error('Erreur suppression fichier:', unlinkErr);
          });
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de l\'ajout du cours.'
          });
        }

        // Enregistrer l'activité
        const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut)
                              VALUES (?, ?, NOW(), ?)`;
        connection.query(activiteQuery, [
          professeur_matricule,
          `Ajout cours: ${titre}`,
          'Succès'
        ]);

        // Réponse selon le type de requête
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
          res.status(201).json({
            success: true,
            message: 'Cours ajouté avec succès.',
            data: {
              cours_id: result.insertId,
              titre,
              fichier,
              matiere_id,
              nom_classe
            }
          });
        } else {
          res.redirect('/professeur/cours');
        }
      });
    });
  });
});

// 📄 Route GET : Lister les cours du professeur
router.get('/', fetchUserData, (req, res) => {
  const professeur_matricule = req.session.matricule;

  const sql = `
    SELECT c.*, m.nom AS matiere
    FROM cours c
    JOIN matiere m ON c.matiere_id = m.matiere_id
    WHERE c.professeur_matricule = ?
    ORDER BY c.date_ajout DESC
  `;

  connection.query(sql, [professeur_matricule], (err, result) => {
    if (err) {
      console.error('Erreur liste cours :', err);
      return res.status(500).send('Erreur serveur');
    }

    res.render('prof/cours', { cours: result, activites: []});

  });
});

// 🗑️ Route GET : Supprimer un cours (pour compatibilité)
router.get('/supprimer/:id', (req, res) => {
  const cours_id = req.params.id;
  const professeur_matricule = req.session.matricule;

  // Vérifier que le cours appartient bien au professeur connecté
  const verifyCours = 'SELECT fichier, titre FROM cours WHERE cours_id = ? AND professeur_matricule = ?';
  connection.query(verifyCours, [cours_id, professeur_matricule], (err, rows) => {
    if (err) {
      console.error('Erreur vérification cours:', err);
      return res.status(500).send('Erreur serveur');
    }

    if (rows.length === 0) {
      return res.status(404).send('Cours non trouvé ou non autorisé');
    }

    const cours = rows[0];
    const filePath = path.join(__dirname, '../../uploads/cours', cours.fichier);

    // Supprimer le fichier physique
    fs.unlink(filePath, (fsErr) => {
      if (fsErr) console.error('Erreur suppression fichier:', fsErr);

      // Supprimer l'enregistrement de la base de données
      connection.query('DELETE FROM cours WHERE cours_id = ?', [cours_id], (deleteErr) => {
        if (deleteErr) {
          console.error('Erreur suppression cours:', deleteErr);
          return res.status(500).send('Erreur lors de la suppression');
        }

        // Enregistrer l'activité
        const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut)
                              VALUES (?, ?, NOW(), ?)`;
        connection.query(activiteQuery, [
          professeur_matricule,
          `Suppression cours: ${cours.titre}`,
          'Succès'
        ]);

        res.redirect('/professeur/cours');
      });
    });
  });
});

// 🗑️ API Route DELETE : Supprimer un cours
router.delete('/api/:id', (req, res) => {
  const cours_id = req.params.id;
  const professeur_matricule = req.session.matricule;

  // Vérifier que le cours appartient bien au professeur connecté
  const verifyCours = 'SELECT fichier, titre FROM cours WHERE cours_id = ? AND professeur_matricule = ?';
  connection.query(verifyCours, [cours_id, professeur_matricule], (err, rows) => {
    if (err) {
      console.error('Erreur vérification cours:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la vérification.'
      });
    }

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé ou non autorisé.'
      });
    }

    const cours = rows[0];
    const filePath = path.join(__dirname, '../../uploads/cours', cours.fichier);

    // Supprimer le fichier physique
    fs.unlink(filePath, (fsErr) => {
      if (fsErr) console.error('Erreur suppression fichier:', fsErr);

      // Supprimer l'enregistrement de la base de données
      connection.query('DELETE FROM cours WHERE cours_id = ?', [cours_id], (deleteErr) => {
        if (deleteErr) {
          console.error('Erreur suppression cours:', deleteErr);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la suppression.'
          });
        }

        // Enregistrer l'activité
        const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut)
                              VALUES (?, ?, NOW(), ?)`;
        connection.query(activiteQuery, [
          professeur_matricule,
          `Suppression cours: ${cours.titre}`,
          'Succès'
        ]);

        res.json({
          success: true,
          message: 'Cours supprimé avec succès.'
        });
      });
    });
  });
});

// 📊 API Route GET : Obtenir les statistiques des cours du professeur
router.get('/api/stats', (req, res) => {
  const professeur_matricule = req.session.matricule;

  const statsQuery = `
    SELECT
      COUNT(*) as total_cours,
      COUNT(DISTINCT nom_classe) as classes_enseignees,
      COUNT(DISTINCT matiere_id) as matieres_enseignees
    FROM cours
    WHERE professeur_matricule = ?
  `;

  connection.query(statsQuery, [professeur_matricule], (err, results) => {
    if (err) {
      console.error('Erreur récupération statistiques:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des statistiques.'
      });
    }

    res.json({
      success: true,
      data: results[0]
    });
  });
});

// 📋 API Route GET : Obtenir les matières du professeur
router.get('/api/matieres', (req, res) => {
  const professeur_matricule = req.session.matricule;

  const matieresQuery = `
    SELECT m.matiere_id, m.nom, m.nom_classe, c.niveau
    FROM matiere m
    JOIN classe c ON m.nom_classe = c.nom_classe
    WHERE m.professeur_matricule = ?
    ORDER BY c.niveau, m.nom_classe, m.nom
  `;

  connection.query(matieresQuery, [professeur_matricule], (err, results) => {
    if (err) {
      console.error('Erreur récupération matières:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des matières.'
      });
    }

    res.json({
      success: true,
      data: results
    });
  });
});

// 📥 Route GET : Télécharger un fichier de cours (Professeur)
router.get('/download/:id', (req, res) => {
  const cours_id = req.params.id;
  const professeur_matricule = req.session.matricule;

  // Vérifier que le cours appartient bien au professeur connecté
  const verifyCours = 'SELECT fichier, titre FROM cours WHERE cours_id = ? AND professeur_matricule = ?';
  connection.query(verifyCours, [cours_id, professeur_matricule], (err, rows) => {
    if (err) {
      console.error('Erreur vérification cours:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur lors de la vérification.'
      });
    }

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Cours non trouvé ou non autorisé.'
      });
    }

    const cours = rows[0];
    const filePath = path.join(__dirname, '../../uploads/cours', cours.fichier);

    // Vérifier que le fichier existe
    fs.access(filePath, fs.constants.F_OK, (err) => {
      if (err) {
        console.error('Fichier non trouvé:', filePath);
        return res.status(404).json({
          success: false,
          message: 'Fichier non trouvé sur le serveur.'
        });
      }

      // Enregistrer l'activité de téléchargement
      const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut)
                            VALUES (?, ?, NOW(), ?)`;
      connection.query(activiteQuery, [
        professeur_matricule,
        `Téléchargement cours: ${cours.titre}`,
        'Succès'
      ]);

      // Définir les en-têtes pour le téléchargement
      const originalName = cours.fichier.split('-').slice(1).join('-'); // Enlever le timestamp
      res.setHeader('Content-Disposition', `attachment; filename="${originalName}"`);
      res.setHeader('Content-Type', 'application/octet-stream');

      // Envoyer le fichier
      res.sendFile(filePath, (err) => {
        if (err) {
          console.error('Erreur envoi fichier:', err);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: 'Erreur lors du téléchargement.'
            });
          }
        }
      });
    });
  });
});

module.exports = router;
