const express = require('express');
const router = express.Router();
const path = require('path');
const multer = require('multer');
const { query, connection } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');
const { hybridAuth } = require('../../middlewares/hybridAuth');

// Middleware : protection accès prof uniquement
router.use((req, res, next) => {
  if (!req.session || req.session.role !== 'professeur') {
    return res.redirect('/'); // ou '/' selon ta config
  }
  next();
});

// Configuration multer pour stockage fichiers devoirs
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/devoirs');  // dossier où sont stockés les fichiers
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});
const upload = multer({ storage });

// Route GET /prof/devoir : page devoirs avec matières et devoirs + leurs soumissions
router.get('/', fetchUserData, (req, res) => {
  const profMatricule = req.session.matricule;
  console.log("Matricule du professeur connecté :", profMatricule);

  const getMatieres = `SELECT * FROM matiere WHERE professeur_matricule = ?`;
  const getDevoirs = `
    SELECT d.*, m.nom AS nom_matiere
    FROM devoir d
    JOIN matiere m ON d.matiere_id = m.matiere_id
    WHERE d.matricule = ?
    ORDER BY d.date_creation DESC
  `;

  connection.query(getMatieres, [profMatricule], (err, matieres) => {
    if (err) {
      console.error('Erreur récupération matières:', err);
      return res.status(500).send('Erreur lors de la récupération des matières');
    }

    connection.query(getDevoirs, [profMatricule], (err2, devoirs) => {
      if (err2) {
        console.error('Erreur récupération devoirs:', err2);
        return res.status(500).send('Erreur lors de la récupération des devoirs');
      }

      // Si aucun devoir, on renvoie directement
      if (devoirs.length === 0) {
        return res.render('prof/devoir', { matieres, devoirs: [], activites: [] });
      }

      // Sinon, on récupère les soumissions pour chaque devoir
      let completed = 0;
      devoirs.forEach((devoir, index) => {
        const getSoumissions = `
          SELECT s.*, e.nom AS nom_eleve, e.prenom AS prenom_eleve
          FROM soumission_devoir s
          JOIN eleve e ON s.eleve_matricule = e.matricule
          WHERE s.devoir_id = ?
        `;
        connection.query(getSoumissions, [devoir.devoir_id], (err3, soumissions) => {
          if (err3) {
            console.error('Erreur récupération soumissions:', err3);
            devoirs[index].soumissions = [];
          } else {
            devoirs[index].soumissions = soumissions || [];
          }
          completed++;
          if (completed === devoirs.length) {
            // Toutes les soumissions chargées -> on rend la page
            res.render('prof/devoir', { matieres, devoirs, activites: []});
          }
        });
      });
    });
  });
});

// Route POST /prof/devoir/ajouter : ajout d'un devoir avec fichier
router.post('/ajouter', upload.single('fichier'), (req, res) => {
  const { titre, description, matiere_id, date_limite } = req.body;
  const fichier = req.file ? req.file.filename : null;
  const profMatricule = req.session.matricule;

  const insertQuery = `
    INSERT INTO devoir (titre, description, fichier, date_limite, matiere_id, matricule, date_creation)
    VALUES (?, ?, ?, ?, ?, ?, NOW())
  `;

  connection.query(insertQuery, [titre, description, fichier, date_limite, matiere_id, profMatricule], (err) => {
    if (err) {
      console.error('Erreur ajout devoir:', err);
      return res.status(500).send('Erreur lors de l\'ajout du devoir');
    }
    // Reste sur la même page après ajout
    res.redirect('/prof/devoir');
  });
});

// Route POST /prof/devoir/corriger/:soumission_id : correction d'une soumission
router.post('/corriger/:soumission_id', (req, res) => {
  const soumissionId = req.params.soumission_id;
  const { note, remarque } = req.body;

  const update = `UPDATE soumission SET note = ?, remarque = ? WHERE id = ?`;
  connection.query(update, [note, remarque, soumissionId], (err) => {
    if (err) {
      console.error('Erreur correction:', err);
      return res.status(500).send('Erreur lors de la correction');
    }
    res.redirect('/prof/devoir');
  });
});

// API Route : récupérer les devoirs du professeur connecté (JSON)
router.get('/api', hybridAuth(), async (req, res) => {
  try {
    const { matricule, role } = req.user;

    // Vérifier que c'est bien un professeur
    if (role !== 'professeur') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    const getMatieres = `SELECT * FROM matiere WHERE professeur_matricule = ?`;
    const getDevoirs = `
      SELECT d.*, m.nom AS nom_matiere
      FROM devoir d
      JOIN matiere m ON d.matiere_id = m.matiere_id
      WHERE d.matricule = ?
      ORDER BY d.date_creation DESC
    `;

    // Récupérer les matières
    const matieres = await query(getMatieres, [matricule]);

    // Récupérer les devoirs
    const devoirs = await query(getDevoirs, [matricule]);

    // Pour chaque devoir, récupérer les soumissions
    const devoirsWithSoumissions = await Promise.all(
      devoirs.map(async (devoir) => {
        const getSoumissions = `
          SELECT s.*, e.nom AS nom_eleve, e.prenom AS prenom_eleve
          FROM soumission_devoir s
          JOIN eleve e ON s.eleve_matricule = e.matricule
          WHERE s.devoir_id = ?
        `;
        const soumissions = await query(getSoumissions, [devoir.devoir_id]);
        return {
          ...devoir,
          soumissions: soumissions || []
        };
      })
    );

    res.json({
      success: true,
      data: {
        matieres: matieres,
        devoirs: devoirsWithSoumissions,
        count: devoirsWithSoumissions.length
      },
      message: 'Devoirs récupérés avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des devoirs:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des devoirs'
    });
  }
});

module.exports = router;
