const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

function isProf(req, res, next) {
  if (req.session && req.session.matricule && req.session.role === 'professeur') {
    return next();
  }
  res.redirect('/');
}

router.get('/', isProf, fetchUserData, async (req, res) => {
  try {
    const profMatricule = req.session.matricule;

    // 1. Obtenir les matières du prof
    const matiereQuery = `
      SELECT * FROM matiere WHERE professeur_matricule = ?
    `;
    const matieres = await query(matiereQuery, [profMatricule]);

    // 2. Obtenir les élèves de la/les classes concernées par ces matières
    const classes = matieres.map(m => m.nom_classe);
    let eleves = [];
    if (classes.length > 0) {
      const placeholders = classes.map(() => '?').join(',');
      const getElevesQuery = `
        SELECT matricule, nom, prenom
        FROM eleve
        WHERE nom_classe IN (${placeholders})
      `;
      eleves = await query(getElevesQuery, classes);
    }

    // 3. Obtenir les notes attribuées
    const notesQuery = `
      SELECT n.note_id, n.note, n.type_evaluation, n.date_evaluation,
             e.nom AS eleve_nom, e.prenom AS eleve_prenom, e.matricule AS eleve_matricule,
             m.nom AS matiere_nom, m.matiere_id
      FROM note n
      JOIN eleve e ON n.eleve_matricule = e.matricule
      JOIN matiere m ON n.matiere_id = m.matiere_id
      WHERE m.professeur_matricule = ?
      ORDER BY n.date_evaluation DESC
    `;
    const notes = await query(notesQuery, [profMatricule]);

    res.render('prof/note', {
      matieres,
      eleves,
      notes,
      activites: []
    });
  } catch (error) {
    console.error('❌ Erreur lors du chargement des notes:', error.message);
    return res.status(500).send('Erreur lors du chargement des notes.');
  }
});

router.post('/ajouter', isProf, async (req, res) => {
  try {
    let { eleve_matricule, matiere_id, note, type_evaluation, date_evaluation } = req.body;
// Convert undefined to null for SQL NULL
if (typeof type_evaluation === 'undefined' || type_evaluation === '') type_evaluation = null;
if (typeof date_evaluation === 'undefined' || date_evaluation === '') date_evaluation = null;
const sql = `
  INSERT INTO note (eleve_matricule, matiere_id, note, type_evaluation, date_evaluation)
  VALUES (?, ?, ?, ?, ?)
`;
await query(sql, [eleve_matricule, matiere_id, note, type_evaluation, date_evaluation]);
res.redirect('/prof/note');
  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout de la note:', error.message);
    return res.status(500).send('Erreur lors de l\'ajout de la note.');
  }
});

router.post('/modifier/:id', isProf, async (req, res) => {
  try {
    const noteId = req.params.id;
    const { note, type_evaluation, date_evaluation } = req.body;

    const sql = `
      UPDATE note
      SET note = ?, type_evaluation = ?, date_evaluation = ?
      WHERE note_id = ?
    `;
    await query(sql, [note, type_evaluation, date_evaluation, noteId]);
    res.redirect('/prof/note');
  } catch (error) {
    console.error('❌ Erreur lors de la modification de la note:', error.message);
    return res.status(500).send('Erreur lors de la modification de la note.');
  }
});

router.post('/supprimer/:id', isProf, (req, res) => {
  const noteId = req.params.id;
  connection.query('DELETE FROM note WHERE note_id = ?', [noteId], (err) => {
    if (err) return res.status(500).send('Erreur suppression de la note');
    res.redirect('/prof/note');
  });
});

module.exports = router;
