// 🔁 backend: routes/prof/absence.js
const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Route GET principale : affichage de la vue
router.get('/', fetchUserData, async (req, res) => {
  try {
    const professeurMatricule = req.session.matricule;
    if (!professeurMatricule) return res.redirect('/');

    const sql = `
      SELECT c.nom_classe
      FROM enseignant_classe ec
      JOIN classe c ON ec.nom_classe = c.nom_classe
      WHERE ec.professeur_matricule = ?
    `;

    const classesResult = await query(sql, [professeurMatricule]);
    res.render('prof/absence', {
      classes: classesResult,
      selectedClasse: '',
      activites: []
    });
  } catch (error) {
    console.error('❌ Erreur lors du chargement des absences:', error.message);
    return res.status(500).send('Erreur serveur');
  }
});


// API : récupérer les élèves d'une classe
router.get('/eleves/:nom_classe', async (req, res) => {
  try {
    const nomClasse = req.params.nom_classe;
    const sql = `SELECT matricule, nom, prenom FROM eleve WHERE nom_classe = ?`;

    const results = await query(sql, [nomClasse]);
    res.json(results);
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des élèves:', error.message);
    return res.status(500).json({ error: 'Erreur serveur' });
  }
});

// API POST : enregistrer les absences
router.post('/enregistrer-absences', async (req, res) => {
  try {
    const { nom_classe, absents, duree } = req.body;

    if (!nom_classe || !Array.isArray(absents) || !duree) {
      return res.status(400).json({ error: 'Données manquantes ou invalides.' });
    }

    const now = new Date();

    // Insert each absence individually to avoid bulk insert issues
    // Find matiere_id for this professor and class (first subject found)
    const matieres = await query(
      'SELECT matiere_id FROM matiere WHERE professeur_matricule = ? AND nom_classe = ? LIMIT 1',
      [req.session.matricule, nom_classe]
    );
    if (!matieres.length) {
      return res.status(400).json({ error: 'Aucune matière trouvée pour ce professeur et cette classe.' });
    }
    const matiere_id = matieres[0].matiere_id;
    const today = new Date().toISOString().split('T')[0];
    let skipped = 0;
    for (const matricule of absents) {
      // Check for duplicate
      const existing = await query(
        'SELECT absence_id FROM absence WHERE eleve_matricule = ? AND matiere_id = ? AND date_absence = ?',
        [matricule, matiere_id, today]
      );
      if (existing.length > 0) {
        skipped++;
        continue;
      }
      const sql = `INSERT INTO absence (eleve_matricule, matiere_id, date_absence, justifiee, motif) VALUES (?, ?, ?, ?, ?)`;
      await query(sql, [matricule, matiere_id, today, false, null]);
    }
    let message = 'Absences enregistrées avec succès.';
    if (skipped > 0) message += ` (${skipped} doublon(s) ignoré(s))`;
    res.json({ message: 'Absences enregistrées avec succès.' });
  } catch (error) {
    console.error('❌ Erreur lors de l\'enregistrement des absences:', error.message);
    return res.status(500).json({ error: 'Erreur lors de l\'enregistrement' });
  }
});

module.exports = router;
