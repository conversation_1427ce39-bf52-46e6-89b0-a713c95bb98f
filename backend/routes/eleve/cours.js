const express = require('express');
const router = express.Router();
const { connection } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');
const path = require('path');
const fs = require('fs');

// Middleware pour vérifier que l'utilisateur est connecté et est un élève
function isEleve(req, res, next) {
  if (req.session && req.session.matricule && req.session.role === 'eleve') {
    return next();
  } else {
    return res.redirect('/'); // Redirige vers la connexion si non connecté
  }
}

// Route pour afficher les cours liés à la classe de l'élève
router.get('/', isEleve, fetchUserData, (req, res) => {
  const matriculeEleve = req.session.matricule;

  // Récupérer la classe de l'élève
  const queryClasse = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
  connection.query(queryClasse, [matriculeEleve], (err, result) => {
    if (err || result.length === 0) {
      console.error('Erreur récupération classe élève :', err);
      return res.status(500).send('Erreur récupération classe');
    }

    const nomClasse = result[0].nom_classe;

    // Récupérer les cours liés à cette classe
    const queryCours = `
      SELECT c.*, p.nom AS professeur_nom 
      FROM cours c 
      JOIN professeur p ON c.professeur_matricule = p.matricule 
      WHERE c.nom_classe = ?
      ORDER BY c.date_ajout DESC
    `;

    connection.query(queryCours, [nomClasse], (err, cours) => {
      if (err) {
        console.error('Erreur récupération cours :', err);
        return res.status(500).send('Erreur récupération cours');
      }

      // Afficher la page avec les cours
      res.render('eleve/cours', {
        titre: 'Cours',
        cours: cours
      });
    });
  });
});

// 📥 Route GET : Télécharger un fichier de cours (Élève)
router.get('/download/:id', isEleve, (req, res) => {

  const cours_id = req.params.id;
  const matriculeEleve = req.session.matricule;

  // Récupérer la classe de l'élève
  const queryClasse = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
  connection.query(queryClasse, [matriculeEleve], (err, eleveResult) => {
    if (err || eleveResult.length === 0) {
      console.error('Erreur récupération classe élève:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification de votre classe.'
      });
    }

    const nomClasse = eleveResult[0].nom_classe;

    // Vérifier que le cours appartient bien à la classe de l'élève
    const verifyCours = `
      SELECT c.fichier, c.titre, c.nom_classe, p.nom as professeur_nom
      FROM cours c
      JOIN professeur p ON c.professeur_matricule = p.matricule
      WHERE c.cours_id = ? AND c.nom_classe = ?
    `;


    connection.query(verifyCours, [cours_id, nomClasse], (err, coursResult) => {
      if (err) {
        console.error('Erreur vérification cours:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur serveur lors de la vérification.'
        });
      }

      if (coursResult.length === 0) {

        return res.status(404).json({
          success: false,
          message: 'Cours non trouvé ou non accessible pour votre classe.'
        });
      }

      const cours = coursResult[0];
      const filePath = path.join(__dirname, '../../uploads/cours', cours.fichier);


      // Vérifier que le fichier existe
      fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
          console.error('Fichier non trouvé:', filePath);
          return res.status(404).json({
            success: false,
            message: 'Fichier non trouvé sur le serveur.'
          });
        }

        // Enregistrer l'activité de téléchargement
        const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut)
                              VALUES (?, ?, NOW(), ?)`;
        connection.query(activiteQuery, [
          matriculeEleve,
          `Téléchargement cours: ${cours.titre}`,
          'Succès'
        ]);


        // Définir les en-têtes pour le téléchargement
        const originalName = cours.fichier.split('-').slice(1).join('-'); // Enlever le timestamp
        res.setHeader('Content-Disposition', `attachment; filename="${originalName}"`);
        res.setHeader('Content-Type', 'application/octet-stream');


        // Envoyer le fichier
        res.sendFile(filePath, (err) => {
          if (err) {
            console.error('Erreur envoi fichier:', err);
            if (!res.headersSent) {
              res.status(500).json({
                success: false,
                message: 'Erreur lors du téléchargement.'
              });
            }
          } else {

          }
        });
      });
    });
  });
});

// 📊 API Route GET : Obtenir les cours avec informations de téléchargement (Élève)
router.get('/api', isEleve, (req, res) => {
  const matriculeEleve = req.session.matricule;

  // Récupérer la classe de l'élève
  const queryClasse = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
  connection.query(queryClasse, [matriculeEleve], (err, eleveResult) => {
    if (err || eleveResult.length === 0) {
      console.error('Erreur récupération classe élève:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération de votre classe.'
      });
    }

    const nomClasse = eleveResult[0].nom_classe;

    // Récupérer les cours avec informations détaillées
    const queryCours = `
      SELECT
        c.cours_id,
        c.titre,
        c.fichier,
        c.date_ajout,
        c.nom_classe,
        p.nom AS professeur_nom,
        p.prenom AS professeur_prenom,
        m.nom AS matiere_nom
      FROM cours c
      JOIN professeur p ON c.professeur_matricule = p.matricule
      JOIN matiere m ON c.matiere_id = m.matiere_id
      WHERE c.nom_classe = ?
      ORDER BY c.date_ajout DESC
    `;

    connection.query(queryCours, [nomClasse], (err, cours) => {
      if (err) {
        console.error('Erreur récupération cours:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la récupération des cours.'
        });
      }

      // Ajouter des informations sur les fichiers
      const coursWithFileInfo = cours.map(c => {
        const originalName = c.fichier ? c.fichier.split('-').slice(1).join('-') : '';
        const fileExtension = path.extname(originalName).toLowerCase();

        return {
          ...c,
          original_filename: originalName,
          file_extension: fileExtension,
          download_url: `/eleve/cours/download/${c.cours_id}`,
          professeur_complet: `${c.professeur_prenom} ${c.professeur_nom}`
        };
      });

      res.json({
        success: true,
        data: {
          classe: nomClasse,
          cours: coursWithFileInfo
        }
      });
    });
  });
});

module.exports = router;
