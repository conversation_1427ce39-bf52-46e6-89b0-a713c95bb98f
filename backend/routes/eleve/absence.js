// backend/routes/eleve/absence.js
const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Voir les absences de l'élève connecté
router.get('/absences', fetchUserData, async (req, res) => {
  try {
    const matricule = req.session.matricule;
    if (!matricule) return res.redirect('/');

    const sql = `
      SELECT date_absence, motif
      FROM absence
      WHERE eleve_matricule = ?
      ORDER BY date_absence DESC
    `;

    const absences = await query(sql, [matricule]);
    res.render('eleve/absence', { absences });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des absences:', error.message);
    return res.status(500).send('Erreur serveur');
  }
});

module.exports = router;
