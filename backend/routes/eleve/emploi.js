/**
 * Routes pour l'emploi du temps des élèves
 * NS School Manager - Student Timetable Routes
 */

const express = require('express');
const router = express.Router();
const { db, query } = require('../../models/db');
const EmploiTempsService = require('../../services/EmploiTempsService');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');
const logger = require('../../utils/logger');

// Initialisation du service
const emploiTempsService = new EmploiTempsService();

/**
 * GET /eleve/emploi-temps
 * Affiche l'emploi du temps pour un élève connecté
 */
router.get('/emploi-temps', fetchUserData, async (req, res) => {
  try {
    const matricule = req.session.matricule;

    if (!matricule) {
      logger.warn('Tentative d\'accès non autorisé à l\'emploi du temps');
      return res.status(401).render('error', {
        message: 'Accès non autorisé',
        error: { status: 401 }
      });
    }

    // 1. Récupérer la classe de l'élève
    const requeteClasse = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
    const resultatsClasse = await db.query(requeteClasse, [matricule]);

    if (resultatsClasse.length === 0) {
      logger.warn(`Élève non trouvé avec le matricule: ${matricule}`);
      return res.status(404).render('error', {
        message: 'Élève non trouvé',
        error: { status: 404 }
      });
    }

    const nomClasse = resultatsClasse[0].nom_classe;
    logger.info(`Récupération de l'emploi du temps pour l'élève ${matricule} de la classe ${nomClasse}`);

    // 2. Récupérer l'emploi du temps via le service
    const emploiTempsData = await emploiTempsService.obtenirEmploiTempsClasse(nomClasse);

    // 3. Formater les données pour la vue (compatibilité avec l'ancien format)
    const emploi = emploiTempsData.creneaux.map(creneau => ({
      jour_semaine: creneau.jour_semaine,
      heure_debut: creneau.heure_debut,
      heure_fin: creneau.heure_fin,
      salle: creneau.salle,
      nom_matiere: creneau.nom_matiere
    }));

    // 4. Récupérer les informations de l'élève pour le topbar (nom, prénom, matricule)
    const eleveInfosSql = 'SELECT nom, prenom FROM eleve WHERE matricule = ? LIMIT 1';
    let eleveInfos = [];
    try {
      eleveInfos = await query(eleveInfosSql, [matricule]);
    } catch (e) {
      // Ne bloque pas l'affichage en cas d'erreur, on log seulement
      logger.warn('Impossible de récupérer nom/prenom pour le topbar élève:', e?.message || e);
      eleveInfos = [];
    }
    const eleve = {
      nom: eleveInfos[0]?.nom || '',
      prenom: eleveInfos[0]?.prenom || '',
      matricule
    };

    // 5. Rendre la vue avec les données + infos élève pour un topbar cohérent
    res.render('eleve/emploi_temps', {
      emploi,
      nomClasse,
      emploiStructure: emploiTempsData.structure,
      joursSemaine: emploiTempsData.joursSemaine,
      creneauxDisponibles: emploiTempsData.creneauxDisponibles,
      eleve,
      notificationsCount: 0
    });

  } catch (erreur) {
    logger.error('Erreur lors de la récupération de l\'emploi du temps élève:', erreur);
    res.status(500).render('error', {
      message: 'Erreur lors du chargement de l\'emploi du temps',
      error: { status: 500, stack: erreur.stack }
    });
  }
});

module.exports = router;
