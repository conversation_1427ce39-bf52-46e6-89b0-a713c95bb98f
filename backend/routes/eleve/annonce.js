const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware'); // adapte si tu as un autre chemin

// Page d'annonces pour un élève
router.get('/annonces', fetchUserData, async (req, res) => {
  try {
    const matricule = req.session.matricule;

    // Trouver la classe de l'élève
    const getClasseQuery = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
    const result = await query(getClasseQuery, [matricule]);

    if (result.length === 0) return res.status(500).send("Erreur serveur");

    const nomClasse = result[0].nom_classe;

    // Récupérer les annonces pour la classe
    const getAnnoncesQuery = `
      SELECT m.*, u.role AS expediteur_role
      FROM message m
      JOIN utilisateur u ON u.matricule = m.sender_matricule
      WHERE m.nom_classe = ? AND m.type = 'annonce'
      ORDER BY m.date_envoi DESC
    `;
    const annonces = await query(getAnnoncesQuery, [nomClasse]);

    res.render('eleve/annonces', { annonces, nomClasse });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des annonces:', error.message);
    return res.status(500).send("Erreur serveur");
  }
});

module.exports = router;
