const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Middleware : Vérifie que l'utilisateur est connecté comme élève
router.use((req, res, next) => {
  console.log('Session actuelle:', req.session);
  if (!req.session || !req.session.matricule || req.session.role !== 'eleve') {
    return res.redirect('/');
  }
  next();
});

// Route GET : afficher les devoirs de l'élève connecté
router.get('/', fetchUserData, async (req, res) => {
  try {
    const eleveMatricule = req.session.matricule;

    // Récupérer la classe (nom_classe) de l'élève
    const classeQuery = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
    const classeResult = await query(classeQuery, [eleveMatricule]);

    if (classeResult.length === 0) {
      return res.status(404).send('Élève introuvable');
    }

    const nomClasse = classeResult[0].nom_classe;

    // Rechercher tous les devoirs liés à la classe de l'élève (via la table matiere)
    const devoirQuery = `
      SELECT d.devoir_id, d.titre, d.description, d.date_limite, d.date_creation,
             m.nom AS matiere, sd.fichier, sd.date_soumission, sd.note, sd.remarque,
             p.nom AS prof_nom, p.prenom AS prof_prenom
      FROM devoir d
      JOIN matiere m ON d.matiere_id = m.matiere_id
      JOIN professeur p ON d.matricule = p.matricule
      LEFT JOIN soumission_devoir sd
        ON sd.devoir_id = d.devoir_id AND sd.eleve_matricule = ?
      WHERE m.nom_classe = ?
      ORDER BY d.date_limite DESC
    `;

    const devoirs = await query(devoirQuery, [eleveMatricule, nomClasse]);
    res.render('eleve/devoir', { devoirs });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des devoirs:', error.message);
    return res.status(500).send('Erreur serveur');
  }
});

// Ajout pour gestion de l'upload de devoirs
const path = require('path');
const multer = require('multer');

const fs = require('fs');
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const dir = path.join(__dirname, '../../uploads/soumissions');
    fs.mkdir(dir, { recursive: true }, (err) => {
      cb(err, dir);
    });
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});
const upload = multer({ storage });

// Route POST : soumettre un devoir
router.post('/soumettre/:id', upload.single('fichier'), async (req, res) => {
  try {
    const devoirId = req.params.id;
    const eleveMatricule = req.session.matricule;
    if (!req.file) {
      // Récupérer les devoirs pour réafficher la page avec l'erreur
      const eleveMatricule = req.session.matricule;
      const classeQuery = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
      const classeResult = await query(classeQuery, [eleveMatricule]);
      const nomClasse = classeResult.length > 0 ? classeResult[0].nom_classe : '';
      const devoirQuery = `
        SELECT d.devoir_id, d.titre, d.description, d.date_limite, d.date_creation,
               m.nom AS matiere, sd.fichier, sd.date_soumission, sd.note, sd.remarque,
               p.nom AS prof_nom, p.prenom AS prof_prenom
        FROM devoir d
        JOIN matiere m ON d.matiere_id = m.matiere_id
        JOIN professeur p ON d.matricule = p.matricule
        LEFT JOIN soumission_devoir sd
          ON sd.devoir_id = d.devoir_id AND sd.eleve_matricule = ?
        WHERE m.nom_classe = ?
        ORDER BY d.date_limite DESC
      `;
      const devoirs = await query(devoirQuery, [eleveMatricule, nomClasse]);
      return res.status(400).render('eleve/devoir', { devoirs, errorMsg: 'Aucun fichier soumis.' });
    }
    // Vérifier que l'élève n'a pas déjà soumis ce devoir
    const checkQuery = 'SELECT * FROM soumission_devoir WHERE devoir_id = ? AND eleve_matricule = ?';
    const check = await query(checkQuery, [devoirId, eleveMatricule]);
    if (check.length > 0) {
      // Récupérer les devoirs pour réafficher la page avec l'erreur
      const eleveMatricule = req.session.matricule;
      const classeQuery = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
      const classeResult = await query(classeQuery, [eleveMatricule]);
      const nomClasse = classeResult.length > 0 ? classeResult[0].nom_classe : '';
      const devoirQuery = `
        SELECT d.devoir_id, d.titre, d.description, d.date_limite, d.date_creation,
               m.nom AS matiere, sd.fichier, sd.date_soumission, sd.note, sd.remarque,
               p.nom AS prof_nom, p.prenom AS prof_prenom
        FROM devoir d
        JOIN matiere m ON d.matiere_id = m.matiere_id
        JOIN professeur p ON d.matricule = p.matricule
        LEFT JOIN soumission_devoir sd
          ON sd.devoir_id = d.devoir_id AND sd.eleve_matricule = ?
        WHERE m.nom_classe = ?
        ORDER BY d.date_limite DESC
      `;
      const devoirs = await query(devoirQuery, [eleveMatricule, nomClasse]);
      return res.status(400).render('eleve/devoir', { devoirs, errorMsg: 'Vous avez déjà soumis ce devoir.' });
    }
    // Insérer la soumission
    const insertQuery = `INSERT INTO soumission_devoir (devoir_id, eleve_matricule, fichier, date_soumission) VALUES (?, ?, ?, NOW())`;
    await query(insertQuery, [devoirId, eleveMatricule, req.file.filename]);
    res.redirect('/eleve/devoirs');
  } catch (error) {
    console.error('❌ Erreur lors de la soumission du devoir:', error.message);
    // Afficher l'erreur sur la page
    try {
      const eleveMatricule = req.session.matricule;
      const classeQuery = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
      const classeResult = await query(classeQuery, [eleveMatricule]);
      const nomClasse = classeResult.length > 0 ? classeResult[0].nom_classe : '';
      const devoirQuery = `
        SELECT d.devoir_id, d.titre, d.description, d.date_limite, d.date_creation,
               m.nom AS matiere, sd.fichier, sd.date_soumission, sd.note, sd.remarque,
               p.nom AS prof_nom, p.prenom AS prof_prenom
        FROM devoir d
        JOIN matiere m ON d.matiere_id = m.matiere_id
        JOIN professeur p ON d.matricule = p.matricule
        LEFT JOIN soumission_devoir sd
          ON sd.devoir_id = d.devoir_id AND sd.eleve_matricule = ?
        WHERE m.nom_classe = ?
        ORDER BY d.date_limite DESC
      `;
      const devoirs = await query(devoirQuery, [eleveMatricule, nomClasse]);
      return res.status(500).render('eleve/devoir', { devoirs, errorMsg: 'Erreur serveur lors de la soumission du devoir.' });
    } catch (err2) {
      return res.status(500).send('Erreur serveur lors de la soumission du devoir.');
    }
  }
});

module.exports = router;
