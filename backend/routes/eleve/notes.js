const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');
const { hybridAuth } = require('../../middlewares/hybridAuth');

// Middleware de sécurité (optionnel mais recommandé)
function ensureEleve(req, res, next) {
  if (req.session && req.session.role === 'eleve') {
    return next();
  }
  return res.redirect('/');
}

// Route : afficher les notes de l'élève connecté (HTML)
router.get('/notes', ensureEleve, fetchUserData, async (req, res) => {
  try {
    const matricule = req.session.matricule;

    const notesQuery = `
      SELECT m.nom AS matiere, n.note, n.type_evaluation, n.date_evaluation
      FROM note n
      JOIN matiere m ON n.matiere_id = m.matiere_id
      WHERE n.eleve_matricule = ?
      ORDER BY n.date_evaluation DESC
    `;

    const results = await query(notesQuery, [matricule]);

    // Calcul de la moyenne (optionnel)
    let moyenne = null;
    if (results.length > 0) {
      const total = results.reduce((acc, curr) => acc + parseFloat(curr.note), 0);
      moyenne = (total / results.length).toFixed(2);
    }

    res.render('eleve/notes', { notes: results, moyenne });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des notes:', error.message);
    return res.status(500).send('Erreur serveur');
  }
});

// API Route : récupérer les notes de l'élève connecté (JSON)
router.get('/notes/api', hybridAuth(), async (req, res) => {
  try {
    const { matricule, role } = req.user;

    // Vérifier que c'est bien un élève
    if (role !== 'eleve') {
      return res.status(403).json({
        success: false,
        message: 'Accès non autorisé'
      });
    }

    const notesQuery = `
      SELECT m.nom AS matiere, n.note, n.type_evaluation, n.date_evaluation
      FROM note n
      JOIN matiere m ON n.matiere_id = m.matiere_id
      WHERE n.eleve_matricule = ?
      ORDER BY n.date_evaluation DESC
    `;

    const results = await query(notesQuery, [matricule]);

    // Calcul de la moyenne (optionnel)
    let moyenne = null;
    if (results.length > 0) {
      const total = results.reduce((acc, curr) => acc + parseFloat(curr.note), 0);
      moyenne = (total / results.length).toFixed(2);
    }

    res.json({
      success: true,
      data: {
        notes: results,
        moyenne: moyenne,
        count: results.length
      },
      message: 'Notes récupérées avec succès'
    });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des notes:', error.message);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des notes'
    });
  }
});

module.exports = router;
