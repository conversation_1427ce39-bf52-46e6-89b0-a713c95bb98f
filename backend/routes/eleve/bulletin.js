// backend/routes/eleve/bulletin.js
const express = require('express');
const router = express.Router();
const { query } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Middleware pour vérifier si l'élève est connecté
router.use((req, res, next) => {
  if (!req.session.matricule || req.session.role !== 'eleve') {
    return res.redirect('/');
  }
  next();
});

// Route GET pour afficher la page bulletin
router.get('/', fetchUserData, async (req, res) => {
  try {
    const matricule = req.session.matricule;

    const sql = `
      SELECT b.*, e.nom, e.prenom
      FROM bulletin b
      JOIN eleve e ON b.eleve_matricule = e.matricule
      WHERE b.eleve_matricule = ?
      ORDER BY b.date_creation DESC
    `;

    const results = await query(sql, [matricule]);
    res.render('eleve/bulletin', { bulletins: results });
  } catch (error) {
    console.error('❌ Erreur lors de la récupération des bulletins:', error.message);
    return res.status(500).send('Erreur serveur');
  }
});

module.exports = router;
