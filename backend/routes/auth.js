const express = require('express');
const router = express.Router();
const authController = require('../controllers/authcontroller');
const authMiddleware = require('../middlewares/authmiddleware');

// Connexion
router.post('/login', authController.login);

// Dashboards protégés
// router.get('/dashboard-admin', authMiddleware.isAuthenticated, authMiddleware.isAdmin, (req, res) => {
//   res.render('dashboard_admin');
// });

router.get('/dashboard-eleve', authMiddleware.isAuthenticated, authMiddleware.isEleve, (req, res) => {
  res.render('dashboard_eleve');
});

// router.get('/dashboard-parent', authMiddleware.isAuthenticated, authMiddleware.isParent, (req, res) => {
//   res.render('dashboard_parent');
// });

router.get('/dashboard-prof', authMiddleware.isAuthenticated, authMiddleware.isProf, (req, res) => {
  res.render('dashboard_prof');
});

// Déconnexion
router.get('/logout', (req, res) => {
  req.session.destroy(() => {
    res.redirect('/');
  });
});

module.exports = router;
