/**
 * Profile Routes for NS School Manager
 * Handles profile pages for all user types
 */

const express = require('express');
const router = express.Router();
const { connection } = require('../models/db');
const { fetchUserData } = require('../middlewares/userDataMiddleware');

// Middleware to check authentication
const isAuthenticated = (req, res, next) => {
  if (req.session && req.session.matricule && req.session.role) {
    return next();
  }
  return res.redirect('/');
};

// Admin profile page
router.get('/admin/profil', isAuthenticated, fetchUserData, (req, res) => {
  if (req.session.role !== 'admin') {
    return res.redirect('/ad');
  }

  const matricule = req.session.matricule;
  const query = 'SELECT matricule, nom, prenom, email, sexe, telephone FROM admin WHERE matricule = ?';
  
  connection.query(query, [matricule], (err, results) => {
    if (err) {
      console.error('Erreur récupération profil admin:', err);
      return res.status(500).send('Erreur serveur');
    }

    const profile = results.length > 0 ? results[0] : {
      matricule: matricule,
      nom: '',
      prenom: '',
      email: '',
      sexe: '',
      telephone: ''
    };

    res.render('admin/profil', {
      profile: profile,
      matricule: req.session.matricule,
      role: req.session.role,
      currentPage: 'profil'
    });
  });
});

// Professor profile page
router.get('/prof/profil', isAuthenticated, fetchUserData, (req, res) => {
  if (req.session.role !== 'professeur') {
    return res.redirect('/');
  }

  const matricule = req.session.matricule;
  const query = 'SELECT matricule, nom, prenom, email, sexe, telephone, specialiste FROM professeur WHERE matricule = ?';
  
  connection.query(query, [matricule], (err, results) => {
    if (err) {
      console.error('Erreur récupération profil professeur:', err);
      return res.status(500).send('Erreur serveur');
    }

    const profile = results.length > 0 ? results[0] : {
      matricule: matricule,
      nom: '',
      prenom: '',
      email: '',
      sexe: '',
      telephone: '',
      specialiste: ''
    };

    res.render('prof/profil', {
      profile: profile,
      matricule: req.session.matricule,
      role: req.session.role,
      currentPage: 'profil'
    });
  });
});

// Student profile page
router.get('/eleve/profil', isAuthenticated, fetchUserData, (req, res) => {
  if (req.session.role !== 'eleve') {
    return res.redirect('/');
  }

  const matricule = req.session.matricule;
  const query = 'SELECT matricule, nom, prenom, sexe, date_naissance, nom_classe FROM eleve WHERE matricule = ?';
  
  connection.query(query, [matricule], (err, results) => {
    if (err) {
      console.error('Erreur récupération profil élève:', err);
      return res.status(500).send('Erreur serveur');
    }

    const profile = results.length > 0 ? results[0] : {
      matricule: matricule,
      nom: '',
      prenom: '',
      sexe: '',
      date_naissance: '',
      nom_classe: ''
    };

    res.render('eleve/profil', {
      profile: profile,
      matricule: req.session.matricule,
      role: req.session.role,
      currentPage: 'profil'
    });
  });
});

module.exports = router;
