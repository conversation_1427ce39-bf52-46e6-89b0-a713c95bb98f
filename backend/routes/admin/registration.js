const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const { query, connection } = require('../../models/db');
const { isAuthenticated, isAdmin } = require('../../middlewares/authmiddleware2');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Page d'enregistrement des utilisateurs (Admin only)
router.get('/', isAuthenticated, isAdmin, fetchUserData, async (req, res) => {
  try {
    // Récupérer les classes disponibles pour les élèves (promise-based, comme prof/cours.js utilise { query })
    const classes = await query('SELECT nom_classe, niveau, annee_scolaire FROM classe ORDER BY niveau, nom_classe');

    res.render('admin/registration', {
      classes: classes,
      matricule: req.session.matricule,
      role: req.session.role
    });
  } catch (err) {
    console.error('Erreur récupération classes:', err);
    return res.status(500).send('Erreur serveur');
  }
});

// API pour l'enregistrement complet d'utilisateurs (Admin only)
router.post('/register', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { 
      matricule, 
      password, 
      confirmPassword,
      role, 
      nom, 
      prenom, 
      email, 
      sexe, 
      telephone,
      date_naissance,
      nom_classe,
      specialiste 
    } = req.body;

    // Validation des champs requis
    if (!matricule || !password || !role) {
      return res.status(400).json({ 
        success: false, 
        message: 'Matricule, mot de passe et rôle sont requis.' 
      });
    }

    // Validation de la confirmation du mot de passe
    if (password !== confirmPassword) {
      return res.status(400).json({ 
        success: false, 
        message: 'Les mots de passe ne correspondent pas.' 
      });
    }

    // Validation de la force du mot de passe
    if (password.length < 6) {
      return res.status(400).json({ 
        success: false, 
        message: 'Le mot de passe doit contenir au moins 6 caractères.' 
      });
    }

    // Validation du rôle
    const validRoles = ['admin', 'eleve', 'professeur'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Rôle invalide. Doit être admin, eleve ou professeur.' 
      });
    }

    // Validation spécifique selon le rôle
    if (role === 'eleve') {
      if (!nom || !prenom || !nom_classe || !sexe) {
        return res.status(400).json({ 
          success: false, 
          message: 'Nom, prénom, sexe et classe sont requis pour un élève.' 
        });
      }
    }

    if (role === 'professeur') {
      if (!nom || !prenom || !email || !sexe) {
        return res.status(400).json({ 
          success: false, 
          message: 'Nom, prénom, email et sexe sont requis pour un professeur.' 
        });
      }
      
      // Validation de l'email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ 
          success: false, 
          message: 'Format d\'email invalide.' 
        });
      }
    }

    if (role === 'admin') {
      if (!nom || !prenom || !email || !sexe) {
        return res.status(400).json({ 
          success: false, 
          message: 'Nom, prénom, email et sexe sont requis pour un admin.' 
        });
      }
      
      // Validation de l'email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ 
          success: false, 
          message: 'Format d\'email invalide.' 
        });
      }
    }

    // Vérifier si le matricule existe déjà
    const checkUser = 'SELECT matricule FROM utilisateur WHERE matricule = ?';
    const existing = await query(checkUser, [matricule]);
    if (existing.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'Ce matricule existe déjà.'
      });
    }

    // Vérifier l'email pour les professeurs et admins
    if ((role === 'professeur' || role === 'admin') && email) {
      const checkEmail = role === 'professeur'
        ? 'SELECT email FROM professeur WHERE email = ?'
        : 'SELECT email FROM admin WHERE email = ?';
      const emailResults = await query(checkEmail, [email]);
      if (emailResults.length > 0) {
        return res.status(409).json({
          success: false,
          message: 'Cet email est déjà utilisé.'
        });
      }
    }

    // Procéder à la création du compte
    async function createUserAccount() {
      try {
        // Hash du mot de passe
        const hashedPassword = await bcrypt.hash(password, 10);

        // Insérer dans la table utilisateur
        const insertUser = 'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)';
        await query(insertUser, [matricule, hashedPassword, role]);

        // Insérer dans la table spécifique selon le rôle
        let roleSpecificQuery = '';
        let roleSpecificParams = [];

        if (role === 'eleve') {
          roleSpecificQuery = `INSERT INTO eleve (matricule, nom, prenom, sexe, date_naissance, nom_classe)
                              VALUES (?, ?, ?, ?, ?, ?)`;
          roleSpecificParams = [matricule, nom, prenom, sexe, date_naissance || null, nom_classe];
        } else if (role === 'professeur') {
          roleSpecificQuery = `INSERT INTO professeur (matricule, nom, prenom, sexe, specialiste, email, telephone)
                              VALUES (?, ?, ?, ?, ?, ?, ?)`;
          roleSpecificParams = [matricule, nom, prenom, sexe, specialiste || null, email, telephone || null];
        } else if (role === 'admin') {
          roleSpecificQuery = `INSERT INTO admin (matricule, nom, prenom, email, sexe, telephone)
                              VALUES (?, ?, ?, ?, ?, ?)`;
          roleSpecificParams = [matricule, nom, prenom, email, sexe, telephone || null];
        }

        if (roleSpecificQuery) {
          await query(roleSpecificQuery, roleSpecificParams);
        }

        // Enregistrer l'activité (non bloquant)
        const activiteQuery = `INSERT INTO activite (utilisateur_id, action, date, statut)
                              VALUES (?, ?, NOW(), ?)`;
        query(activiteQuery, [
          req.session.matricule,
          `Création compte ${role} - ${matricule}`,
          'Succès'
        ]).catch(() => {});

        res.status(201).json({
          success: true,
          message: `Compte ${role} créé avec succès pour ${matricule}.`,
          data: { matricule, role, nom, prenom }
        });
      } catch (err) {
        console.error('Erreur lors de la création du compte:', err);
        // Rollback manuel si erreur sur insertion spécifique
        await query('DELETE FROM utilisateur WHERE matricule = ?', [matricule]).catch(() => {});
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la création du compte.'
        });
      }
    }

    await createUserAccount();

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Erreur serveur interne.' 
    });
  }
});

module.exports = router;
