/**
 * Unified Authentication Routes for NS School Manager
 * Handles all authentication endpoints in one organized file
 */

const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const config = require('../config/config');
const { connection } = require('../models/db');
const logger = require('../utils/logger');
const { generateToken, generateRefreshToken, verifyToken, extractTokenFromHeader } = require('../utils/jwt');

/**
 * Student/Teacher Login
 * POST /api/auth/login
 */
router.post('/login', async (req, res) => {
  try {
    const { matricule, password, role, remember } = req.body;

    // Input validation
    if (!matricule || !password || !role) {
      return res.status(400).json({ 
        success: false,
        message: "Veuillez remplir tous les champs." 
      });
    }

    // Validate role
    const validRoles = ['eleve', 'professeur'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ 
        success: false,
        message: "Rôle invalide." 
      });
    }

    // Query user from database
    connection.query(
      'SELECT * FROM utilisateur WHERE matricule = ? AND role = ?',
      [matricule, role],
      async (err, results) => {
        if (err) {
          logger.error("Database error during login", err, { matricule, role });
          return res.status(500).json({ 
            success: false,
            message: "Erreur de base de données." 
          });
        }

        if (results.length === 0) {
          logger.auth('login', matricule, false, { role, reason: 'user_not_found' });
          return res.status(401).json({ 
            success: false,
            message: "Identifiants incorrects." 
          });
        }

        const utilisateur = results[0];

        // Verify password
        const passwordMatch = await bcrypt.compare(password, utilisateur.password);
        if (!passwordMatch) {
          logger.auth('login', matricule, false, { role, reason: 'invalid_password' });
          return res.status(401).json({ 
            success: false,
            message: "Identifiants incorrects." 
          });
        }

        // Determine if this is an API request (mobile) or web request
        // Only treat as API request if explicitly requested or has Authorization header
        const isApiRequest = req.headers.authorization ||
                            req.query.api === 'true' ||
                            req.body.api === 'true' ||
                            req.body.api === true;

        let tokens = null;

        // Generate JWT tokens for API/mobile requests
        if (isApiRequest) {
          try {
            const accessToken = generateToken(utilisateur, remember === 'on');
            const refreshToken = generateRefreshToken(utilisateur);
            tokens = { accessToken, refreshToken };
          } catch (error) {
            logger.error('Error generating tokens', error);
            return res.status(500).json({
              success: false,
              message: "Erreur lors de la génération des tokens"
            });
          }
        } else {
          // Store user session for web requests
          req.session.matricule = utilisateur.matricule;
          req.session.role = utilisateur.role;

          // Set session expiry based on remember option
          if (remember === 'on') {
            req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
          } else {
            req.session.cookie.maxAge = config.session.cookie.maxAge; // Default from config
          }
        }

        // Log successful login
        logger.auth('login', matricule, true, { role, method: isApiRequest ? 'jwt' : 'session' });
        const loginActivity = `INSERT INTO activite (utilisateur_id, action, date, statut) VALUES (?, ?, NOW(), ?)`;
        connection.query(loginActivity, [matricule, 'Connexion', 'Succès'], (err) => {
          if (err) logger.error('Error logging activity', err);
        });

        // Prepare response
        const response = {
          success: true,
          message: "Connexion réussie",
          user: {
            matricule: utilisateur.matricule,
            role: utilisateur.role
          }
        };

        // Add tokens for API requests
        if (tokens) {
          response.tokens = tokens;
          response.authMethod = 'jwt';
        } else {
          // Add redirect for web requests
          let redirectURL = '/';
          switch (role) {
            case 'professeur':
              redirectURL = '/dashboard-prof';
              break;
            case 'eleve':
              redirectURL = '/dashboard-eleve';
              break;
            default:
              redirectURL = '/';
          }
          response.redirect = redirectURL;
          response.authMethod = 'session';
        }

        return res.status(200).json(response);
      }
    );

  } catch (error) {
    logger.error("Login error", error);
    return res.status(500).json({ 
      success: false,
      message: "Erreur serveur interne." 
    });
  }
});

/**
 * Admin Login
 * POST /api/auth/login_ad
 */
router.post('/login_ad', async (req, res) => {
  try {
    const { matricule, password, remember } = req.body;

    // Input validation
    if (!matricule || !password) {
      return res.status(400).json({ 
        success: false,
        message: "Veuillez remplir tous les champs." 
      });
    }

    // Query admin user from database
    connection.query(
      'SELECT * FROM utilisateur WHERE matricule = ? AND role = "admin"',
      [matricule],
      async (err, results) => {
        if (err) {
          logger.error("Database error during admin login", err, { matricule });
          return res.status(500).json({ 
            success: false,
            message: "Erreur de base de données." 
          });
        }

        if (results.length === 0) {
          logger.auth('admin_login', matricule, false, { reason: 'admin_not_found' });
          return res.status(401).json({ 
            success: false,
            message: "Identifiants administrateur incorrects." 
          });
        }

        const utilisateur = results[0];

        // Verify password
        const passwordMatch = await bcrypt.compare(password, utilisateur.password);
        if (!passwordMatch) {
          logger.auth('admin_login', matricule, false, { reason: 'invalid_password' });
          return res.status(401).json({ 
            success: false,
            message: "Identifiants administrateur incorrects." 
          });
        }

        // Determine if this is an API request (mobile) or web request
        // Only treat as API request if explicitly requested or has Authorization header
        const isApiRequest = req.headers.authorization ||
                            req.query.api === 'true' ||
                            req.body.api === 'true' ||
                            req.body.api === true;

        let tokens = null;

        // Generate JWT tokens for API/mobile requests
        if (isApiRequest) {
          try {
            const accessToken = generateToken(utilisateur, remember === 'on');
            const refreshToken = generateRefreshToken(utilisateur);
            tokens = { accessToken, refreshToken };
          } catch (error) {
            logger.error('Error generating admin tokens', error);
            return res.status(500).json({
              success: false,
              message: "Erreur lors de la génération des tokens"
            });
          }
        } else {
          // Store admin session for web requests
          req.session.matricule = utilisateur.matricule;
          req.session.role = utilisateur.role;

          // Set session expiry based on remember option
          if (remember === 'on') {
            req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
          } else {
            req.session.cookie.maxAge = config.session.cookie.maxAge; // Default from config
          }
        }

        // Log successful admin login
        logger.auth('admin_login', matricule, true, { method: isApiRequest ? 'jwt' : 'session' });
        const loginActivity = `INSERT INTO activite (utilisateur_id, action, date, statut) VALUES (?, ?, NOW(), ?)`;
        connection.query(loginActivity, [matricule, 'Connexion Admin', 'Succès'], (err) => {
          if (err) logger.error('Error logging admin activity', err);
        });

        // Prepare response
        const response = {
          success: true,
          message: "Connexion administrateur réussie",
          user: {
            matricule: utilisateur.matricule,
            role: utilisateur.role
          }
        };

        // Add tokens for API requests
        if (tokens) {
          response.tokens = tokens;
          response.authMethod = 'jwt';
        } else {
          // Add redirect for web requests
          response.redirect = "/dashboard-admin";
          response.authMethod = 'session';
        }

        return res.status(200).json(response);
      }
    );

  } catch (error) {
    logger.error("Admin login error", error);
    return res.status(500).json({ 
      success: false,
      message: "Erreur serveur interne." 
    });
  }
});

/**
 * Logout for all users
 * POST /api/auth/logout
 */
router.post('/logout', (req, res) => {
  const matricule = req.session?.matricule;
  const role = req.session?.role;

  req.session.destroy((err) => {
    if (err) {
      logger.error('Error destroying session', err, { matricule });
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la déconnexion'
      });
    }

    // Log logout
    if (matricule) {
      logger.auth('logout', matricule, true, { role });
      const logoutActivity = `INSERT INTO activite (utilisateur_id, action, date, statut) VALUES (?, ?, NOW(), ?)`;
      connection.query(logoutActivity, [matricule, 'Déconnexion', 'Succès'], (err) => {
        if (err) logger.error('Error logging logout activity', err);
      });
    }

    res.json({
      success: true,
      message: 'Déconnexion réussie',
      redirect: role === 'admin' ? '/ad' : '/'
    });
  });
});

/**
 * Check authentication status
 * GET /api/auth/status
 */
router.get('/status', (req, res) => {
  let user = null;
  let authMethod = null;

  // Check JWT token first
  const authHeader = req.headers.authorization;
  if (authHeader) {
    try {
      const token = extractTokenFromHeader(authHeader);
      if (token) {
        const decoded = verifyToken(token);
        user = {
          matricule: decoded.matricule,
          role: decoded.role
        };
        authMethod = 'jwt';
      }
    } catch (error) {
      // JWT verification failed, continue to session check
    }
  }

  // Check session if no valid JWT
  if (!user && req.session && req.session.matricule && req.session.role) {
    user = {
      matricule: req.session.matricule,
      role: req.session.role
    };
    authMethod = 'session';
  }

  if (user) {
    res.json({
      success: true,
      authenticated: true,
      authMethod,
      user
    });
  } else {
    res.json({
      success: true,
      authenticated: false
    });
  }
});

/**
 * Refresh JWT token
 * POST /api/auth/refresh
 */
router.post('/refresh', (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: "Refresh token requis"
      });
    }

    // Verify refresh token
    const decoded = verifyToken(refreshToken);

    if (decoded.type !== 'refresh') {
      return res.status(400).json({
        success: false,
        message: "Token de rafraîchissement invalide"
      });
    }

    // Generate new access token
    const user = {
      matricule: decoded.matricule,
      role: decoded.role
    };

    const newAccessToken = generateToken(user);

    res.json({
      success: true,
      message: "Token rafraîchi avec succès",
      tokens: {
        accessToken: newAccessToken,
        refreshToken // Keep the same refresh token
      }
    });

  } catch (error) {
    logger.error('Error refreshing token', error);
    res.status(401).json({
      success: false,
      message: "Token de rafraîchissement invalide ou expiré"
    });
  }
});

module.exports = router;
