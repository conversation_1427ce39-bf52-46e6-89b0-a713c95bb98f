const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const db = require('../models/db'); // adapte si besoin

// Route de connexion dédiée pour les ADMIN (depuis login_ad.ejs)
router.post('/login_ad', (req, res) => {
  const { matricule, password } = req.body;

  if (!matricule || !password) {
    return res.status(400).json({ message: "Tous les champs sont requis." });
  }

  const sql = 'SELECT * FROM utilisateur WHERE matricule = ? AND role = "admin"';

  db.query(sql, [matricule], async (err, results) => {
    if (err) {
      console.error("Erreur SQL :", err);
      return res.status(500).json({ message: "Erreur serveur. Veuillez réessayer." });
    }

    if (results.length === 0) {
      return res.status(401).json({ message: "Matricule ou rôle invalide (pas un admin)." });
    }

    const admin = results[0];

    try {
      const match = await bcrypt.compare(password, admin.password);

      if (!match) {
        return res.status(401).json({ message: "Mot de passe incorrect." });
      }

      // Stockage dans la session
      req.session.matricule = admin.matricule;
      req.session.role = admin.role;

      // ✅ Réponse JSON pour rediriger vers le tableau de bord admin
      return res.status(200).json({
        message: "Connexion réussie.",
        redirect: "/dashboard-admin"
      });

    } catch (error) {
      console.error("Erreur de comparaison de mot de passe :", error);
      return res.status(500).json({ message: "Erreur interne. Veuillez réessayer." });
    }
  });
});

module.exports = router;
