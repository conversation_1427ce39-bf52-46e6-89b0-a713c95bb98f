const express = require('express');
const router = express.Router();
const db = require('../models/db');
const { fetchUserData } = require('../middlewares/userDataMiddleware'); // adapte selon ton projet

// ✅ Route GET - Afficher tous les élèves avec les classes
router.get('/', fetchUserData, async (req, res) => {
  const matriculeAdmin = req.session.matricule;

  if (!matriculeAdmin) {
    return res.redirect('/ad'); // Redirection si l’admin n’est pas connecté
  }

  try {
    // 1. Récupérer les infos de l’admin
    const [admin] = await db.query('SELECT * FROM admin WHERE matricule = ?', [matriculeAdmin]);

    if (!admin) {
      return res.status(404).send("Admin introuvable");
    }

    // 2. Compter les notifications non lues
    const [{ count: notificationsCount }] = await db.query(
      'SELECT COUNT(*) AS count FROM message WHERE receiver_matricule = ? AND lu = 0',
      [matriculeAdmin]
    );

    // 3. Récupérer les élèves
    const eleves = await db.query('SELECT * FROM eleve');

    // 4. Récupérer les classes
    const classes = await db.query('SELECT * FROM classe');

    // 5. Rendu de la vue avec toutes les données nécessaires
    res.render('admin/eleve', {
      title: 'Gestion des élèves',
      eleves,
      classes,
      admin,
      notificationsCount
    });
  } catch (err) { 
    console.error(err);
    res.status(500).send("Erreur serveur");
  }
});

module.exports = router;
