/**
 * Routes API pour la gestion des salles
 * NS School Manager - Professional Room Management API
 */

const express = require('express');
const router = express.Router();
const SalleService = require('../../services/SalleService');
const logger = require('../../utils/logger');

const salleService = new SalleService();

/**
 * Middleware de gestion d'erreurs pour les routes de salles
 */
const handleError = (res, error, defaultMessage = 'Erreur serveur', path = '/') => {
  logger.error('Erreur API Salles:', error);

  const statusCode = error.message.includes('non trouvée') ? 404 :
                    error.message.includes('existe déjà') ? 409 :
                    error.message.includes('invalide') ? 400 : 500;

  res.status(statusCode).json({
    success: false,
    error: {
      message: error.message || defaultMessage,
      timestamp: new Date().toISOString(),
      statusCode: statusCode
    }
  });
};

/**
 * GET /api/salles
 * Récupère toutes les salles disponibles
 */
router.get('/', async (req, res) => {
  try {
    const salles = await salleService.obtenirToutesSalles();
    
    res.json({
      success: true,
      data: salles,
      count: salles.length,
      message: 'Salles récupérées avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la récupération des salles');
  }
});

/**
 * GET /api/salles/types/:type
 * Récupère les salles par type
 */
router.get('/types/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const salles = await salleService.obtenirSallesParType(type);
    
    res.json({
      success: true,
      data: salles,
      count: salles.length,
      message: `Salles de type ${type} récupérées avec succès`,
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la récupération des salles par type');
  }
});

/**
 * GET /api/salles/statistiques
 * Récupère les statistiques des salles
 */
router.get('/statistiques', async (req, res) => {
  try {
    const salles = await salleService.obtenirToutesSalles();

    const stats = {
      total: salles.length,
      disponibles: salles.filter(s => s.disponible).length,
      capaciteTotal: salles.reduce((sum, s) => sum + s.capacite, 0),
      parType: {}
    };

    // Grouper par type
    salles.forEach(salle => {
      if (!stats.parType[salle.type_salle]) {
        stats.parType[salle.type_salle] = {
          count: 0,
          capaciteTotal: 0
        };
      }
      stats.parType[salle.type_salle].count++;
      stats.parType[salle.type_salle].capaciteTotal += salle.capacite;
    });

    res.json({
      success: true,
      data: stats,
      message: 'Statistiques des salles récupérées avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la récupération des statistiques');
  }
});

/**
 * GET /api/salles/:id
 * Récupère une salle par son ID
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const salle = await salleService.obtenirSalleParId(parseInt(id));

    res.json({
      success: true,
      data: salle,
      message: 'Salle récupérée avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la récupération de la salle');
  }
});

/**
 * POST /api/salles
 * Ajoute une nouvelle salle
 */
router.post('/', async (req, res) => {
  try {
    const donneesSalle = req.body;
    const resultat = await salleService.ajouterSalle(donneesSalle);
    
    res.status(201).json({
      success: true,
      data: resultat,
      message: 'Salle ajoutée avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de l\'ajout de la salle');
  }
});

/**
 * PUT /api/salles/:id
 * Modifie une salle existante
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const donneesSalle = req.body;
    const resultat = await salleService.modifierSalle(parseInt(id), donneesSalle);
    
    res.json({
      success: true,
      data: resultat,
      message: 'Salle modifiée avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la modification de la salle');
  }
});

/**
 * DELETE /api/salles/:id
 * Supprime (désactive) une salle
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const resultat = await salleService.supprimerSalle(parseInt(id));
    
    res.json({
      success: true,
      data: resultat,
      message: 'Salle supprimée avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la suppression de la salle');
  }
});

/**
 * POST /api/salles/verifier-disponibilite
 * Vérifie la disponibilité d'une salle à un horaire donné
 */
router.post('/verifier-disponibilite', async (req, res) => {
  try {
    const { salle_id, jour_semaine, heure_debut, heure_fin, exclude_id } = req.body;
    
    if (!salle_id || !jour_semaine || !heure_debut || !heure_fin) {
      return res.status(400).json({
        success: false,
        message: 'Paramètres manquants: salle_id, jour_semaine, heure_debut, heure_fin requis',
        timestamp: new Date().toISOString(),
        path: req.path
      });
    }

    const disponibilite = await salleService.verifierDisponibiliteSalle(
      salle_id, jour_semaine, heure_debut, heure_fin, exclude_id
    );
    
    res.json({
      success: true,
      data: disponibilite,
      message: disponibilite.disponible ? 
        'Salle disponible' : 
        `Salle non disponible - ${disponibilite.conflits.length} conflit(s) détecté(s)`,
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la vérification de disponibilité');
  }
});

/**
 * GET /api/salles/options/select
 * Récupère les salles formatées pour les select/dropdown
 */
router.get('/options/select', async (req, res) => {
  try {
    const salles = await salleService.obtenirToutesSalles();
    
    // Grouper par type pour un meilleur affichage
    const sallesGroupees = salles.reduce((acc, salle) => {
      const type = salle.type_salle;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push({
        value: salle.salle_id,
        text: salle.nom_salle,
        capacite: salle.capacite,
        etage: salle.etage,
        batiment: salle.batiment
      });
      return acc;
    }, {});

    // Convertir en format pour optgroups
    const options = Object.keys(sallesGroupees).map(type => ({
      label: getTypeSalleLabel(type),
      options: sallesGroupees[type]
    }));

    res.json({
      success: true,
      data: {
        grouped: options,
        flat: salles.map(salle => ({
          value: salle.salle_id,
          text: salle.nom_salle,
          type: salle.type_salle,
          capacite: salle.capacite,
          info: `${salle.nom_salle} (${salle.capacite} places, ${salle.etage})`
        }))
      },
      count: salles.length,
      message: 'Options de salles récupérées avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la récupération des options de salles');
  }
});

/**
 * GET /api/salles/options/form
 * Récupère les options pour les formulaires de création/édition de salles
 */
router.get('/options/form', async (req, res) => {
  try {
    const options = await salleService.obtenirOptionsFormulaire();

    res.json({
      success: true,
      data: options,
      message: 'Options de formulaire récupérées avec succès',
      timestamp: new Date().toISOString(),
      path: req.path
    });
  } catch (error) {
    handleError(res, error, 'Erreur lors de la récupération des options de formulaire');
  }
});

/**
 * Utilitaire pour obtenir le label français du type de salle
 */
function getTypeSalleLabel(type) {
  const labels = {
    'normale': 'Salles Normales',
    'laboratoire': 'Laboratoires',
    'informatique': 'Salles Informatique',
    'sport': 'Installations Sportives',
    'bibliotheque': 'Bibliothèques',
    'autre': 'Autres Salles'
  };
  return labels[type] || type;
}

module.exports = router;
