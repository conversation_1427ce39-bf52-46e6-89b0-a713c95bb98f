const express = require('express');
const router = express.Router();
const { connection, getPool } = require('../../models/db');
const bcrypt = require('bcrypt');
const config = require('../../config/config');
const { requireAdmin } = require('../../middlewares/hybridAuth');

// GET - liste des élèves
router.get('/', requireAdmin(), (req, res) => {
  connection.query('SELECT * FROM eleve', (err, result) => {
    if (err) return res.status(500).json({ success: false, message: 'Database error', error: err.message });
    res.json({ success: true, data: result });
  });
});

// POST - ajouter un élève
router.post('/', requireAdmin(), async (req, res) => {
  const { matricule, nom, prenom, sexe, date_naissance, classe, password } = req.body;

  // Use provided password or default
  const userPassword = password && password.trim() !== '' ? password : 'eleve123';

  let dbConnection;
  try {
    const hashedPassword = await bcrypt.hash(userPassword, config.security.bcryptRounds);

    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    // 1. Create user in utilisateur table
    const userSql = 'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)';
    await dbConnection.query(userSql, [matricule, hashedPassword, 'eleve']);

    // 2. Create student in eleve table
    const eleveSql = `INSERT INTO eleve (matricule, nom, prenom, sexe, date_naissance, nom_classe)
                      VALUES (?, ?, ?, ?, ?, ?)`;
    await dbConnection.query(eleveSql, [matricule, nom, prenom, sexe, date_naissance, classe]);

    await dbConnection.commit();
    res.json({
      message: 'Élève créé avec succès',
      success: true,
      matricule,
      passwordUsed: password && password.trim() !== '' ? 'custom' : 'default'
    });
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Student creation failed:', error);
    res.status(500).json({ error: 'Creation failed', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

// PUT - modifier un élève
router.put('/:matricule', requireAdmin(), async (req, res) => {
  const { nom, prenom, classe, password } = req.body;
  const { matricule } = req.params;

  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    // Update student information
    const eleveSql = 'UPDATE eleve SET nom = ?, prenom = ?, nom_classe = ? WHERE matricule = ?';
    await dbConnection.query(eleveSql, [nom, prenom, classe, matricule]);

    // Update password if provided
    if (password && password.trim() !== '') {
      const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);
      const userSql = 'UPDATE utilisateur SET password = ? WHERE matricule = ?';
      await dbConnection.query(userSql, [hashedPassword, matricule]);
    }

    await dbConnection.commit();
    res.json({
      message: 'Élève modifié avec succès',
      passwordChanged: password && password.trim() !== '' ? true : false
    });
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Student update failed:', error);
    res.status(500).json({ error: 'Update failed', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

// DELETE - supprimer un élève
router.delete('/:matricule', requireAdmin(), async (req, res) => {
  const matricule = req.params.matricule;
  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    await dbConnection.query('DELETE FROM note WHERE eleve_matricule = ?', [matricule]);
    await dbConnection.query('DELETE FROM absence WHERE eleve_matricule = ?', [matricule]);
    await dbConnection.query('DELETE FROM soumission_devoir WHERE eleve_matricule = ?', [matricule]);
    await dbConnection.query('DELETE FROM eleve WHERE matricule = ?', [matricule]);
    await dbConnection.query('DELETE FROM utilisateur WHERE matricule = ?', [matricule]);

    await dbConnection.commit();
    res.sendStatus(200);
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Erreur lors de la suppression de l\'élève :', error);
    res.status(500).json({ message: 'Erreur lors de la suppression.', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

module.exports = router;