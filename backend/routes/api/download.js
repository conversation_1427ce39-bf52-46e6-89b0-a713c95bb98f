const express = require('express');
const router = express.Router();
const connection = require('../../models/db');
const path = require('path');
const fs = require('fs');
const {
  checkFileAccess,
  logDownloadActivity,
  rateLimitDownloads,
  sanitizeFilename
} = require('../../middlewares/fileAccess');

// Middleware pour vérifier l'authentification
function isAuthenticated(req, res, next) {
  if (req.session && req.session.matricule && req.session.role) {
    return next();
  }
  return res.status(401).json({ 
    success: false, 
    message: 'Authentification requise.' 
  });
}

// 📥 Route sécurisée pour télécharger un fichier de cours
router.get('/cours/:id',
  rateLimitDownloads(50),
  checkFileAccess('cours'),
  sanitizeFilename,
  logDownloadActivity,
  (req, res) => {
    // Les middlewares ont déjà vérifié l'accès et préparé les données
    const cours = req.cours;
    const safeFilename = req.safeFilename;
    const filePath = path.join(__dirname, '../../uploads/cours', cours.fichier);

    // Vérifier que le fichier existe
    fs.access(filePath, fs.constants.F_OK, (err) => {
      if (err) {
        console.error('Fichier non trouvé:', filePath);
        return res.status(404).json({
          success: false,
          message: 'Fichier non trouvé sur le serveur.'
        });
      }

      // Définir les en-têtes pour le téléchargement
      res.setHeader('Content-Disposition', `attachment; filename="${safeFilename}"`);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      // Envoyer le fichier
      res.sendFile(filePath, (err) => {
        if (err) {
          console.error('Erreur envoi fichier:', err);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              message: 'Erreur lors du téléchargement.'
            });
          }
        }
      });
    });
  });

// 📊 Route pour obtenir les informations d'un fichier sans le télécharger
router.get('/cours/:id/info', checkFileAccess('cours'), sanitizeFilename, (req, res) => {
  // Les middlewares ont déjà vérifié l'accès et préparé les données
  const cours = req.cours;
  const safeFilename = req.safeFilename;
  const filePath = path.join(__dirname, '../../uploads/cours', cours.fichier);
  const originalName = cours.fichier.split('-').slice(1).join('-');
  const fileExtension = path.extname(originalName).toLowerCase();

  // Obtenir la taille du fichier
  fs.stat(filePath, (err, stats) => {
    const fileSize = err ? null : stats.size;
    const fileSizeFormatted = fileSize ? formatFileSize(fileSize) : 'Inconnu';

    res.json({
      success: true,
      data: {
        cours_id: cours.cours_id,
        titre: cours.titre,
        matiere: cours.matiere_nom,
        professeur: `${cours.professeur_prenom} ${cours.professeur_nom}`,
        classe: cours.nom_classe,
        date_ajout: cours.date_ajout,
        fichier: {
          nom_original: originalName,
          nom_securise: safeFilename,
          extension: fileExtension,
          taille: fileSizeFormatted,
          taille_bytes: fileSize
        },
        download_url: `/api/download/cours/${cours.cours_id}`,
        can_download: true
      }
    });
  });
});

// Fonction utilitaire pour formater la taille des fichiers
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

module.exports = router;
