// routes/admin/utilisateur.js
const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const { connection } = require('../../models/db');
const { isAuthenticated, isAdmin } = require('../../middlewares/authmiddleware2');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// Afficher tous les utilisateurs dans la vue (Admin only)
router.get('/admin/utilisateur', isAuthenticated, isAdmin, fetchUserData, (req, res) => {
  const sql = `
    SELECT u.matricule, u.role,
           COALESCE(e.nom, p.nom, a.nom) as nom,
           COALESCE(e.prenom, p.prenom, a.prenom) as prenom,
           COALESCE(e.nom_classe, '') as classe,
           COALESCE(p.email, a.email, '') as email
    FROM utilisateur u
    LEFT JOIN eleve e ON u.matricule = e.matricule
    LEFT JOIN professeur p ON u.matricule = p.matricule
    LEFT JOIN admin a ON u.matricule = a.matricule
    ORDER BY u.role, u.matricule
  `;

  connection.query(sql, (err, results) => {
    if (err) {
      console.error('Erreur récupération utilisateurs:', err);
      return res.status(500).send('Erreur serveur');
    }
    res.render('admin/utilisateur', { utilisateurs: results });
  });
});

// API endpoint for comprehensive user registration (Admin only)
router.post('/admin/utilisateur/register', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const {
      matricule,
      password,
      role,
      nom,
      prenom,
      email,
      sexe,
      telephone,
      date_naissance,
      nom_classe,
      specialiste
    } = req.body;

    // Validation des champs requis
    if (!matricule || !password || !role) {
      return res.status(400).json({
        success: false,
        message: 'Matricule, mot de passe et rôle sont requis.'
      });
    }

    // Validation du rôle
    const validRoles = ['admin', 'eleve', 'professeur'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Rôle invalide. Doit être admin, eleve ou professeur.'
      });
    }

    // Validation spécifique selon le rôle
    if (role === 'eleve' && (!nom || !prenom || !nom_classe)) {
      return res.status(400).json({
        success: false,
        message: 'Nom, prénom et classe sont requis pour un élève.'
      });
    }

    if (role === 'professeur' && (!nom || !prenom || !email)) {
      return res.status(400).json({
        success: false,
        message: 'Nom, prénom et email sont requis pour un professeur.'
      });
    }

    if (role === 'admin' && (!nom || !prenom || !email)) {
      return res.status(400).json({
        success: false,
        message: 'Nom, prénom et email sont requis pour un admin.'
      });
    }

    // Vérifier si le matricule existe déjà
    const checkUser = 'SELECT matricule FROM utilisateur WHERE matricule = ?';
    connection.query(checkUser, [matricule], async (err, results) => {
      if (err) {
        console.error('Erreur vérification utilisateur:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur serveur lors de la vérification.'
        });
      }

      if (results.length > 0) {
        return res.status(409).json({
          success: false,
          message: 'Ce matricule existe déjà.'
        });
      }

      // Hash du mot de passe
      const hashedPassword = await bcrypt.hash(password, 10);

      // Insérer dans la table utilisateur
      const insertUser = 'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)';
      connection.query(insertUser, [matricule, hashedPassword, role], (err) => {
        if (err) {
          console.error('Erreur insertion utilisateur:', err);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la création du compte utilisateur.'
          });
        }

        // Insérer dans la table spécifique selon le rôle
        let roleSpecificQuery = '';
        let roleSpecificParams = [];

        if (role === 'eleve') {
          roleSpecificQuery = `INSERT INTO eleve (matricule, nom, prenom, sexe, date_naissance, nom_classe)
                              VALUES (?, ?, ?, ?, ?, ?)`;
          roleSpecificParams = [matricule, nom, prenom, sexe, date_naissance, nom_classe];
        } else if (role === 'professeur') {
          roleSpecificQuery = `INSERT INTO professeur (matricule, nom, prenom, sexe, specialiste, email, telephone)
                              VALUES (?, ?, ?, ?, ?, ?, ?)`;
          roleSpecificParams = [matricule, nom, prenom, sexe, specialiste, email, telephone];
        } else if (role === 'admin') {
          roleSpecificQuery = `INSERT INTO admin (matricule, nom, prenom, email, sexe, telephone)
                              VALUES (?, ?, ?, ?, ?, ?)`;
          roleSpecificParams = [matricule, nom, prenom, email, sexe, telephone];
        }

        if (roleSpecificQuery) {
          connection.query(roleSpecificQuery, roleSpecificParams, (err) => {
            if (err) {
              console.error('Erreur insertion données spécifiques:', err);
              // Supprimer l'utilisateur créé en cas d'erreur
              connection.query('DELETE FROM utilisateur WHERE matricule = ?', [matricule]);
              return res.status(500).json({
                success: false,
                message: 'Erreur lors de la création des données spécifiques.'
              });
            }

            res.status(201).json({
              success: true,
              message: `Compte ${role} créé avec succès pour ${matricule}.`
            });
          });
        } else {
          res.status(201).json({
            success: true,
            message: `Compte utilisateur créé avec succès pour ${matricule}.`
          });
        }
      });
    });

  } catch (error) {
    console.error('Erreur lors de l\'enregistrement:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur interne.'
    });
  }
});

// Ajouter un utilisateur (ancienne méthode, conservée pour compatibilité)
router.post('/admin/utilisateur/ajouter', isAuthenticated, isAdmin, async (req, res) => {
  const { matricule, password, role } = req.body;
  const hashed = await bcrypt.hash(password, 10);
  const sql = 'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)';

  connection.query(sql, [matricule, hashed, role], (err) => {
    if (err) return res.status(500).send('Erreur ajout utilisateur');
    res.redirect('/admin/utilisateur');
  });
});

// Modifier un utilisateur (Admin only)
router.post('/admin/utilisateur/modifier/:matricule', isAuthenticated, isAdmin, async (req, res) => {
  const { role, password } = req.body;
  const matricule = req.params.matricule;

  let sql, params;

  if (password && password.trim() !== '') {
    const hashed = await bcrypt.hash(password, 10);
    sql = 'UPDATE utilisateur SET password = ?, role = ? WHERE matricule = ?';
    params = [hashed, role, matricule];
  } else {
    sql = 'UPDATE utilisateur SET role = ? WHERE matricule = ?';
    params = [role, matricule];
  }

  connection.query(sql, params, (err) => {
    if (err) return res.status(500).send('Erreur modification utilisateur');
    res.redirect('/admin/utilisateur');
  });
});

// API endpoint pour modifier un utilisateur (Admin only)
router.put('/admin/utilisateur/:matricule', isAuthenticated, isAdmin, async (req, res) => {
  try {
    const { role, password } = req.body;
    const matricule = req.params.matricule;

    // Validation du rôle si fourni
    if (role) {
      const validRoles = ['admin', 'eleve', 'professeur'];
      if (!validRoles.includes(role)) {
        return res.status(400).json({
          success: false,
          message: 'Rôle invalide. Doit être admin, eleve ou professeur.'
        });
      }
    }

    let sql, params;

    if (password && password.trim() !== '') {
      const hashed = await bcrypt.hash(password, 10);
      if (role) {
        sql = 'UPDATE utilisateur SET password = ?, role = ? WHERE matricule = ?';
        params = [hashed, role, matricule];
      } else {
        sql = 'UPDATE utilisateur SET password = ? WHERE matricule = ?';
        params = [hashed, matricule];
      }
    } else if (role) {
      sql = 'UPDATE utilisateur SET role = ? WHERE matricule = ?';
      params = [role, matricule];
    } else {
      return res.status(400).json({
        success: false,
        message: 'Aucune modification spécifiée.'
      });
    }

    connection.query(sql, params, (err, result) => {
      if (err) {
        console.error('Erreur modification utilisateur:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la modification.'
        });
      }

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé.'
        });
      }

      res.json({
        success: true,
        message: 'Utilisateur modifié avec succès.'
      });
    });

  } catch (error) {
    console.error('Erreur lors de la modification:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur interne.'
    });
  }
});

// Supprimer un utilisateur (Admin only)
router.post('/admin/utilisateur/supprimer/:matricule', isAuthenticated, isAdmin, (req, res) => {
  connection.query('DELETE FROM utilisateur WHERE matricule = ?', [req.params.matricule], (err) => {
    if (err) return res.status(500).send('Erreur suppression utilisateur');
    res.redirect('/admin/utilisateur');
  });
});

// API endpoint pour supprimer un utilisateur (Admin only)
router.delete('/admin/utilisateur/:matricule', isAuthenticated, isAdmin, (req, res) => {
  const matricule = req.params.matricule;

  // Empêcher la suppression de son propre compte
  if (req.session.matricule === matricule) {
    return res.status(400).json({
      success: false,
      message: 'Vous ne pouvez pas supprimer votre propre compte.'
    });
  }

  connection.query('DELETE FROM utilisateur WHERE matricule = ?', [matricule], (err, result) => {
    if (err) {
      console.error('Erreur suppression utilisateur:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la suppression.'
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé.'
      });
    }

    res.json({
      success: true,
      message: 'Utilisateur supprimé avec succès.'
    });
  });
});

// API endpoint pour obtenir la liste des utilisateurs (Admin only)
router.get('/admin/utilisateur/api', isAuthenticated, isAdmin, (req, res) => {
  const sql = `
    SELECT u.matricule, u.role,
           COALESCE(e.nom, p.nom, a.nom) as nom,
           COALESCE(e.prenom, p.prenom, a.prenom) as prenom,
           COALESCE(e.nom_classe, '') as classe,
           COALESCE(p.email, a.email, '') as email
    FROM utilisateur u
    LEFT JOIN eleve e ON u.matricule = e.matricule
    LEFT JOIN professeur p ON u.matricule = p.matricule
    LEFT JOIN admin a ON u.matricule = a.matricule
    ORDER BY u.role, u.matricule
  `;

  connection.query(sql, (err, results) => {
    if (err) {
      console.error('Erreur récupération utilisateurs:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des utilisateurs.'
      });
    }

    res.json({
      success: true,
      data: results
    });
  });
});

// API endpoint pour vérifier la disponibilité d'un matricule (Admin only)
router.get('/admin/utilisateur/check/:matricule', isAuthenticated, isAdmin, (req, res) => {
  const matricule = req.params.matricule;

  connection.query('SELECT matricule FROM utilisateur WHERE matricule = ?', [matricule], (err, results) => {
    if (err) {
      console.error('Erreur vérification matricule:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la vérification.'
      });
    }

    res.json({
      success: true,
      available: results.length === 0
    });
  });
});

// API endpoint pour obtenir les classes disponibles (Admin only)
router.get('/admin/utilisateur/classes', isAuthenticated, isAdmin, (req, res) => {
  connection.query('SELECT nom_classe, niveau, annee_scolaire FROM classe ORDER BY niveau, nom_classe', (err, results) => {
    if (err) {
      console.error('Erreur récupération classes:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de la récupération des classes.'
      });
    }

    res.json({
      success: true,
      data: results
    });
  });
});

// User profile and password management routes (for non-admin users)

// Get current user profile
router.get('/profile', (req, res) => {
  if (!req.session.matricule) {
    return res.status(401).json({
      success: false,
      message: 'Non authentifié'
    });
  }

  const matricule = req.session.matricule;
  const role = req.session.role;

  let query, table;
  switch (role) {
    case 'admin':
      query = 'SELECT matricule, nom, prenom, email, sexe, telephone FROM admin WHERE matricule = ?';
      break;
    case 'professeur':
      query = 'SELECT matricule, nom, prenom, email, sexe, telephone, specialiste FROM professeur WHERE matricule = ?';
      break;
    case 'eleve':
      query = 'SELECT matricule, nom, prenom, sexe, date_naissance, nom_classe FROM eleve WHERE matricule = ?';
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'Rôle invalide'
      });
  }

  connection.query(query, [matricule], (err, results) => {
    if (err) {
      console.error('Erreur récupération profil:', err);
      return res.status(500).json({
        success: false,
        message: 'Erreur serveur'
      });
    }

    if (results.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Profil non trouvé'
      });
    }

    res.json({
      success: true,
      data: { ...results[0], role }
    });
  });
});

// Change user password (for authenticated users)
router.post('/change-password', async (req, res) => {
  try {
    if (!req.session.matricule) {
      return res.status(401).json({
        success: false,
        message: 'Non authentifié'
      });
    }

    const { currentPassword, newPassword, confirmPassword } = req.body;
    const matricule = req.session.matricule;

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Tous les champs sont requis'
      });
    }

    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: 'Les nouveaux mots de passe ne correspondent pas'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'Le nouveau mot de passe doit contenir au moins 6 caractères'
      });
    }

    // Verify current password
    const userQuery = 'SELECT password FROM utilisateur WHERE matricule = ?';
    connection.query(userQuery, [matricule], async (err, results) => {
      if (err) {
        console.error('Erreur vérification mot de passe:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur serveur'
        });
      }

      if (results.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Utilisateur non trouvé'
        });
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, results[0].password);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Mot de passe actuel incorrect'
        });
      }

      // Hash new password and update
      const hashedNewPassword = await bcrypt.hash(newPassword, 10);
      const updateQuery = 'UPDATE utilisateur SET password = ? WHERE matricule = ?';

      connection.query(updateQuery, [hashedNewPassword, matricule], (updateErr) => {
        if (updateErr) {
          console.error('Erreur mise à jour mot de passe:', updateErr);
          return res.status(500).json({
            success: false,
            message: 'Erreur lors de la mise à jour du mot de passe'
          });
        }

        res.json({
          success: true,
          message: 'Mot de passe modifié avec succès'
        });
      });
    });

  } catch (error) {
    console.error('Erreur changement mot de passe:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur interne'
    });
  }
});

// Update user profile (for authenticated users)
router.put('/profile', async (req, res) => {
  try {
    if (!req.session.matricule) {
      return res.status(401).json({
        success: false,
        message: 'Non authentifié'
      });
    }

    const matricule = req.session.matricule;
    const role = req.session.role;
    const updates = req.body;

    let query, params;
    switch (role) {
      case 'admin':
        query = 'UPDATE admin SET nom = ?, prenom = ?, email = ?, sexe = ?, telephone = ? WHERE matricule = ?';
        params = [updates.nom, updates.prenom, updates.email, updates.sexe, updates.telephone, matricule];
        break;
      case 'professeur':
        query = 'UPDATE professeur SET nom = ?, prenom = ?, email = ?, sexe = ?, telephone = ?, specialiste = ? WHERE matricule = ?';
        params = [updates.nom, updates.prenom, updates.email, updates.sexe, updates.telephone, updates.specialiste, matricule];
        break;
      case 'eleve':
        query = 'UPDATE eleve SET nom = ?, prenom = ?, sexe = ?, date_naissance = ? WHERE matricule = ?';
        params = [updates.nom, updates.prenom, updates.sexe, updates.date_naissance, matricule];
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Rôle invalide'
        });
    }

    connection.query(query, params, (err) => {
      if (err) {
        console.error('Erreur mise à jour profil:', err);
        return res.status(500).json({
          success: false,
          message: 'Erreur lors de la mise à jour du profil'
        });
      }

      res.json({
        success: true,
        message: 'Profil mis à jour avec succès'
      });
    });

  } catch (error) {
    console.error('Erreur mise à jour profil:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur serveur interne'
    });
  }
});

module.exports = router;
