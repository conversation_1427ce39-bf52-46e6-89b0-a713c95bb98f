const express = require('express');
const router = express.Router();
const { connection, getPool, query } = require('../../models/db');
const bcrypt = require('bcrypt');
const config = require('../../config/config');
const { requireAdmin } = require('../../middlewares/hybridAuth');
let logger;
try {
  logger = require('../../utils/logger');
} catch (e) {
  logger = console;
}

// GET - Liste des profs
router.get('/', requireAdmin(), (req, res) => {
  connection.query('SELECT * FROM professeur', (err, result) => {
    if (err) return res.status(500).json({ success: false, message: 'Database error', error: err.message });
    res.json({ success: true, data: result });
  });
});

// POST - Ajouter
router.post('/', requireAdmin(), async (req, res) => {
  const { matricule, nom, prenom, sexe, specialiste, email, telephone, password } = req.body;

  // Use provided password or default
  const userPassword = password && password.trim() !== '' ? password : 'prof123';

  let dbConnection;
  try {
    const hashedPassword = await bcrypt.hash(userPassword, config.security.bcryptRounds);

    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    // 1. Create user in utilisateur table
    const userSql = 'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)';
    await dbConnection.query(userSql, [matricule, hashedPassword, 'professeur']);

    // 2. Create professor in professeur table
    const profSql = `INSERT INTO professeur (matricule, nom, prenom, sexe, specialiste, email, telephone)
                     VALUES (?, ?, ?, ?, ?, ?, ?)`;
    await dbConnection.query(profSql, [matricule, nom, prenom, sexe, specialiste, email, telephone]);

    await dbConnection.commit();
    res.json({
      message: 'Professeur créé avec succès',
      matricule,
      passwordUsed: password && password.trim() !== '' ? 'custom' : 'default'
    });
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Professor creation failed:', error);
    res.status(500).json({ error: 'Creation failed', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

// PUT - Modifier
router.put('/:matricule', requireAdmin(), async (req, res) => {
  const { nom, prenom, sexe, specialiste, email, telephone, password } = req.body;
  const { matricule } = req.params;

  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    // Update professor information
    const profSql = `UPDATE professeur SET nom = ?, prenom = ?, sexe = ?, specialiste = ?, email = ?, telephone = ?
                     WHERE matricule = ?`;
    await dbConnection.query(profSql, [nom, prenom, sexe, specialiste, email, telephone, matricule]);

    // Update password if provided
    if (password && password.trim() !== '') {
      const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);
      const userSql = 'UPDATE utilisateur SET password = ? WHERE matricule = ?';
      await dbConnection.query(userSql, [hashedPassword, matricule]);
    }

    await dbConnection.commit();
    res.json({
      message: 'Professeur modifié avec succès',
      passwordChanged: password && password.trim() !== '' ? true : false
    });
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Professor update failed:', error);
    res.status(500).json({ error: 'Update failed', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

// DELETE - Supprimer
router.delete('/:matricule', requireAdmin(), async (req, res) => {
  const matricule = req.params.matricule;
  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    // 1. Mettre à jour les références dans la table `matiere`
    await dbConnection.query('UPDATE matiere SET professeur_matricule = NULL WHERE professeur_matricule = ?', [matricule]);

    // 2. Supprimer le professeur de la table `professeur`
    await dbConnection.query('DELETE FROM professeur WHERE matricule = ?', [matricule]);

    // 3. Supprimer l'utilisateur de la table `utilisateur`
    await dbConnection.query('DELETE FROM utilisateur WHERE matricule = ?', [matricule]);

    await dbConnection.commit();
    res.sendStatus(200);
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Professor deletion failed:', error);
    res.status(500).json({ error: 'Delete failed', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

// GET - Liste des specialites (public/auth consistent with other /api/prof routes -> no admin required)
router.get('/specialites', async (req, res) => {
  // Predefined specialties derived from seeds (teachers' specialiste values)
  const predefinedSpecialties = [
    'Français',
    'Histoire-Géographie',
    'Mathématiques',
    'Sciences Physiques'
  ];

  // Helper: normalize for dedup - trim, collapse internal whitespace, lowercase (accent-preserving)
  const normalizeKey = (s) =>
    s
      .trim()
      .replace(/\s+/g, ' ')
      .toLocaleLowerCase();

  // Build initial map from predefined to preserve their casing labels
  const labelByKey = new Map();
  for (const label of predefinedSpecialties) {
    labelByKey.set(normalizeKey(label), label);
  }

  // DISTINCT query to fetch DB values
  const sql =
    "SELECT DISTINCT specialiste FROM professeur WHERE specialiste IS NOT NULL AND TRIM(specialiste) != ''";

  try {
    // Prefer centralized query() if available, otherwise use connection.query callback style
    const runQuery = async () => {
      if (typeof query === 'function') {
        return await query(sql);
      }
      // fallback to promise-wrapped connection.query
      return await new Promise((resolve, reject) => {
        connection.query(sql, (err, rows) => {
          if (err) return reject(err);
          resolve(rows);
        });
      });
    };

    const rows = await runQuery();

    // Merge DB values into map; for DB labels, keep trimmed single-spaced label as-is
    for (const row of rows || []) {
      const raw = String(row.specialiste || '');
      const trimmed = raw.trim().replace(/\s+/g, ' ');
      if (!trimmed) continue;
      const key = normalizeKey(trimmed);
      if (!labelByKey.has(key)) {
        // keep readable capitalization from DB while avoiding trailing/duplicate spaces
        labelByKey.set(key, trimmed);
      }
    }

    // Sort alphabetically by display label
    const data = Array.from(labelByKey.values()).sort((a, b) =>
      a.localeCompare(b, undefined, { sensitivity: 'base' })
    );

    return res.json({ success: true, data });
  } catch (error) {
    // Log error and return predefined list only
    (logger && logger.error ? logger.error : console.error)(
      'Error fetching specialites:', error
    );
    const data = Array.from(new Set(predefinedSpecialties)).sort((a, b) =>
      a.localeCompare(b, undefined, { sensitivity: 'base' })
    );
    return res.json({ success: true, data });
  }
});

module.exports = router;