// ✅ backend/routes/api/classe.js

const express = require('express');
const router = express.Router();
const { connection, getPool } = require('../../models/db');

// 🔹 Récupérer toutes les classes
router.get('/', (req, res) => {
  const sql = 'SELECT * FROM classe ORDER BY nom_classe ASC';
  connection.query(sql, (err, rows) => {
    if (err) {
      console.error('Erreur lors de la récupération des classes :', err);
      return res.status(500).json({ message: 'Erreur serveur' });
    }
    res.json(rows);
  });
});

// 🔹 Ajouter une classe
router.post('/', (req, res) => {
  const { nom_classe, niveau, annee_scolaire } = req.body;

  if (!nom_classe || !niveau || !annee_scolaire) {
    return res.status(400).json({ message: 'Tous les champs sont requis.' });
  }

  const sql = 'INSERT INTO classe (nom_classe, niveau, annee_scolaire) VALUES (?, ?, ?)';
  connection.query(sql, [nom_classe, niveau, annee_scolaire], err => {
    if (err) {
      console.error("Erreur lors de l'ajout de la classe :", err);
      return res.status(500).json({ message: "Erreur lors de l'ajout." });
    }
    res.sendStatus(200);
  });
});

// 🔹 Modifier une classe (transactional)
router.put('/:nom_classe', async (req, res) => {
  const oldNomClasse = req.params.nom_classe;
  const { nom_classe: newNomClasse, niveau, annee_scolaire } = req.body;

  if (!newNomClasse || !niveau || !annee_scolaire) {
    return res.status(400).json({ message: 'Tous les champs sont requis pour la modification.' });
  }

  if (oldNomClasse === newNomClasse) {
    const sql = `UPDATE classe SET niveau = ?, annee_scolaire = ? WHERE nom_classe = ?`;
    connection.query(sql, [niveau, annee_scolaire, oldNomClasse], err => {
        if (err) {
            console.error('Erreur lors de la modification (sans changement de nom) :', err);
            return res.status(500).json({ message: 'Erreur lors de la mise à jour.' });
        }
        return res.sendStatus(200);
    });
    return;
  }

  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    await dbConnection.query('SET FOREIGN_KEY_CHECKS=0;');

    await dbConnection.query('UPDATE eleve SET nom_classe = ? WHERE nom_classe = ?', [newNomClasse, oldNomClasse]);
    await dbConnection.query('UPDATE matiere SET nom_classe = ? WHERE nom_classe = ?', [newNomClasse, oldNomClasse]);
    
    const updateClasseSql = `UPDATE classe SET nom_classe = ?, niveau = ?, annee_scolaire = ? WHERE nom_classe = ?`;
    await dbConnection.query(updateClasseSql, [newNomClasse, niveau, annee_scolaire, oldNomClasse]);

    await dbConnection.query('SET FOREIGN_KEY_CHECKS=1;');

    await dbConnection.commit();
    res.sendStatus(200);
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Erreur lors de la modification de la classe :', error);
    res.status(500).json({ message: 'Erreur lors de la mise à jour.', details: error.message });
  } finally {
    if (dbConnection) {
      try { await dbConnection.query('SET FOREIGN_KEY_CHECKS=1;'); } catch (e) {}
      dbConnection.release();
    }
  }
});

// 🔹 Supprimer une classe (transactional)
router.delete('/:nom_classe', async (req, res) => {
  const nomClasse = req.params.nom_classe;
  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    await dbConnection.query('UPDATE eleve SET nom_classe = NULL WHERE nom_classe = ?', [nomClasse]);
    await dbConnection.query('DELETE FROM matiere WHERE nom_classe = ?', [nomClasse]);
    await dbConnection.query('DELETE FROM classe WHERE nom_classe = ?', [nomClasse]);

    await dbConnection.commit();
    res.sendStatus(200);
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Erreur lors de la suppression de la classe :', error);
    res.status(500).json({ message: 'Erreur lors de la suppression.', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

module.exports = router;