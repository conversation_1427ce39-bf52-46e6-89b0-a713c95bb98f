const express = require('express');
const router = express.Router();
const { connection } = require('../../models/db');

// 🔹 GET - Récupérer les paramètres
router.get('/', (req, res) => {
  connection.query('SELECT * FROM parametre WHERE id = 1', (err, rows) => {
    if (err) return res.status(500).json(err);
    res.json(rows[0] || {});
  });
});

// 🔹 PUT - Modifier les paramètres
router.put('/', (req, res) => {
  const { annee_scolaire, nb_trimestres, theme, messagerie_active } = req.body;
  connection.query(
    `UPDATE parametre SET annee_scolaire=?, nb_trimestres=?, theme=?, messagerie_active=? WHERE id=1`,
    [annee_scolaire, nb_trimestres, theme, messagerie_active],
    (err) => {
      if (err) return res.status(500).json(err);
      res.sendStatus(200);
    }
  );
});

module.exports = router;
