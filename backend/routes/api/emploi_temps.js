/**
 * API Routes professionnelles pour la gestion des emplois du temps
 * NS School Manager - Emploi du Temps API
 */

const express = require('express');
const router = express.Router();
const EmploiTempsService = require('../../services/EmploiTempsService');
const EmploiTempsUtils = require('../../utils/emploiTempsUtils');
const logger = require('../../utils/logger');
const { hybridAuth } = require('../../middlewares/hybridAuth');
const { query } = require('../../models/db');

// Initialisation du service
const emploiTempsService = new EmploiTempsService();

// Middleware pour parser les données JSON et URL-encoded
router.use(express.json());
router.use(express.urlencoded({ extended: true }));

/**
 * Middleware de gestion d'erreurs pour les routes d'emploi du temps
 */
const gestionErreurs = (fn) => {
  return async (req, res, next) => {
    try {
      await fn(req, res, next);
    } catch (erreur) {
      logger.error(`Erreur dans l'API emploi du temps: ${erreur.message}`, {
        route: req.route.path,
        method: req.method,
        params: req.params,
        body: req.body,
        erreur: erreur.stack
      });

      // Réponse d'erreur standardisée
      res.status(erreur.status || 500).json({
        success: false,
        message: erreur.message || 'Erreur interne du serveur',
        timestamp: new Date().toISOString(),
        path: req.path
      });
    }
  };
};

/**
 * GET /api/emploi_temps/eleve
 * Récupère l'emploi du temps de la classe de l'élève connecté
 */
router.get('/eleve', hybridAuth(), gestionErreurs(async (req, res) => {
  const { matricule, role } = req.user;

  // Vérifier que c'est bien un élève
  if (role !== 'eleve') {
    return res.status(403).json({
      success: false,
      message: 'Accès non autorisé'
    });
  }

  // Récupérer la classe de l'élève
  const classeQuery = 'SELECT nom_classe FROM eleve WHERE matricule = ?';
  const classeResult = await query(classeQuery, [matricule]);

  if (classeResult.length === 0) {
    return res.status(404).json({
      success: false,
      message: 'Élève non trouvé'
    });
  }

  const nomClasse = classeResult[0].nom_classe;

  // Récupérer l'emploi du temps de la classe
  const emploiTemps = await emploiTempsService.obtenirEmploiTempsClasse(nomClasse);

  res.json({
    success: true,
    data: {
      nomClasse: nomClasse,
      emploiTemps: emploiTemps.creneaux,
      emploiStructure: emploiTemps.structure,
      stats: {
        totalCreneaux: emploiTemps.creneaux.length,
        joursSemaine: emploiTemps.joursSemaine
      }
    },
    message: `Emploi du temps récupéré avec succès pour la classe ${nomClasse}`
  });
}));

/**
 * GET /api/emploi_temps/professeur
 * Récupère l'emploi du temps du professeur connecté
 */
router.get('/professeur', hybridAuth(), gestionErreurs(async (req, res) => {
  const { matricule, role } = req.user;

  // Vérifier que c'est bien un professeur
  if (role !== 'professeur') {
    return res.status(403).json({
      success: false,
      message: 'Accès non autorisé'
    });
  }

  // Récupérer l'emploi du temps complet du professeur
  const emploiTempsQuery = `
    SELECT
      et.id,
      et.jour_semaine,
      et.heure_debut,
      et.heure_fin,
      et.nom_classe,
      m.nom AS nom_matiere,
      COALESCE(s.nom_salle, et.salle) AS salle_nom,
      et.salle_id
    FROM emploi_temps et
    JOIN matiere m ON et.matiere_id = m.matiere_id
    LEFT JOIN salle s ON et.salle_id = s.salle_id
    WHERE m.professeur_matricule = ?
    ORDER BY
      FIELD(et.jour_semaine, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'),
      et.heure_debut
  `;
  const emploiTemps = await query(emploiTempsQuery, [matricule]);

  // Structurer les données par jour de la semaine
  const joursSemaine = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
  const emploiStructure = {};

  joursSemaine.forEach(jour => {
    emploiStructure[jour] = emploiTemps.filter(creneau => creneau.jour_semaine === jour);
  });

  res.json({
    success: true,
    data: {
      emploiTemps: emploiTemps,
      emploiStructure: emploiStructure,
      stats: {
        totalCreneaux: emploiTemps.length,
        joursSemaine: joursSemaine
      }
    },
    message: 'Emploi du temps récupéré avec succès'
  });
}));

/**
 * GET /api/emploi_temps/classes
 * Récupère toutes les classes avec leurs détails
 */
router.get('/classes', gestionErreurs(async (req, res) => {
  const classes = await emploiTempsService.obtenirClasses();

  res.json({
    success: true,
    data: classes,
    count: classes.length,
    message: 'Classes récupérées avec succès'
  });
}));

/**
 * GET /api/emploi_temps/matieres
 * Récupère toutes les matières ou les matières d'une classe spécifique
 */
router.get('/matieres', gestionErreurs(async (req, res) => {
  const { nom_classe } = req.query;
  const matieres = await emploiTempsService.obtenirMatieres(nom_classe);

  res.json({
    success: true,
    data: matieres,
    count: matieres.length,
    message: `Matières récupérées avec succès${nom_classe ? ` pour la classe ${nom_classe}` : ''}`
  });
}));

/**
 * GET /api/emploi_temps/planning/:nom_classe
 * Récupère l'emploi du temps complet d'une classe
 */
router.get('/planning/:nom_classe', gestionErreurs(async (req, res) => {
  const { nom_classe } = req.params;

  if (!nom_classe) {
    return res.status(400).json({
      success: false,
      message: 'Le nom de la classe est requis'
    });
  }

  const emploiTemps = await emploiTempsService.obtenirEmploiTempsClasse(nom_classe);

  res.json({
    success: true,
    data: emploiTemps,
    message: `Emploi du temps récupéré avec succès pour la classe ${nom_classe}`
  });
}));

/**
 * GET /api/emploi_temps/planning-jour/:jour_semaine
 * Récupère tous les créneaux d'un jour spécifique pour toutes les classes
 */
router.get('/planning-jour/:jour_semaine', gestionErreurs(async (req, res) => {
  const { jour_semaine } = req.params;

  if (!jour_semaine) {
    return res.status(400).json({
      success: false,
      message: 'Le jour de la semaine est requis'
    });
  }

  const emploiTemps = await emploiTempsService.obtenirEmploiTempsParJour(jour_semaine);

  res.json({
    success: true,
    data: emploiTemps,
    message: `Emploi du temps du ${jour_semaine} récupéré avec succès`
  });
}));

/**
 * POST /api/emploi_temps
 * Ajoute un nouveau créneau à l'emploi du temps
 */
router.post('/', gestionErreurs(async (req, res) => {
  const donneesCreneau = req.body;

  // Log de la requête pour le débogage
  logger.info('Nouvelle demande d\'ajout de créneau:', donneesCreneau);

  const resultat = await emploiTempsService.ajouterCreneau(donneesCreneau);

  res.status(201).json({
    success: true,
    data: resultat,
    message: 'Créneau ajouté avec succès à l\'emploi du temps'
  });
}));

/**
 * PUT /api/emploi_temps/:id
 * Met à jour un créneau existant
 */
router.put('/:id', gestionErreurs(async (req, res) => {
  const idCreneau = parseInt(req.params.id);
  const donneesCreneau = req.body;

  if (!idCreneau || isNaN(idCreneau)) {
    return res.status(400).json({
      success: false,
      message: 'ID de créneau invalide'
    });
  }

  logger.info(`Modification du créneau ${idCreneau}:`, donneesCreneau);

  const resultat = await emploiTempsService.modifierCreneau(idCreneau, donneesCreneau);

  res.json({
    success: true,
    data: resultat,
    message: 'Créneau modifié avec succès'
  });
}));

/**
 * DELETE /api/emploi_temps/:id
 * Supprime un créneau de l'emploi du temps
 */
router.delete('/:id', gestionErreurs(async (req, res) => {
  const idCreneau = parseInt(req.params.id);

  if (!idCreneau || isNaN(idCreneau)) {
    return res.status(400).json({
      success: false,
      message: 'ID de créneau invalide'
    });
  }

  logger.info(`Suppression du créneau ${idCreneau}`);

  const resultat = await emploiTempsService.supprimerCreneau(idCreneau);

  res.json({
    success: true,
    data: resultat,
    message: 'Créneau supprimé avec succès'
  });
}));

/**
 * POST /api/emploi_temps/verifier-conflits
 * Vérifie les conflits pour un créneau sans l'ajouter
 */
router.post('/verifier-conflits', gestionErreurs(async (req, res) => {
  const donneesCreneau = req.body;
  const { id_creneau } = req.query;

  const conflits = await emploiTempsService.verifierConflits(donneesCreneau, id_creneau);

  res.json({
    success: true,
    data: conflits,
    message: conflits.aConflits ? 'Conflits détectés' : 'Aucun conflit détecté'
  });
}));

/**
 * GET /api/emploi_temps/statistiques
 * Récupère les statistiques de l'emploi du temps
 */
router.get('/statistiques', gestionErreurs(async (req, res) => {
  const statistiques = await emploiTempsService.obtenirStatistiques();

  res.json({
    success: true,
    data: statistiques,
    message: 'Statistiques récupérées avec succès'
  });
}));

/**
 * GET /api/emploi_temps/creneaux-disponibles
 * Récupère les créneaux horaires disponibles
 */
router.get('/creneaux-disponibles', gestionErreurs(async (req, res) => {
  res.json({
    success: true,
    data: {
      creneauxHoraires: emploiTempsService.creneauxHoraires,
      joursSemaine: emploiTempsService.joursSemaine
    },
    message: 'Créneaux disponibles récupérés avec succès'
  });
}));

// ===== FONCTIONNALITÉS AVANCÉES =====

/**
 * GET /api/emploi_temps/export/csv/:nom_classe
 * Exporte l'emploi du temps au format CSV
 */
router.get('/export/csv/:nom_classe', gestionErreurs(async (req, res) => {
  const { nom_classe } = req.params;

  const csvData = await EmploiTempsUtils.exportToCSV(nom_classe);

  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', `attachment; filename="emploi_temps_${nom_classe}.csv"`);
  res.send(csvData);
}));

/**
 * GET /api/emploi_temps/export/json/:nom_classe
 * Exporte l'emploi du temps au format JSON
 */
router.get('/export/json/:nom_classe', gestionErreurs(async (req, res) => {
  const { nom_classe } = req.params;

  const jsonData = await EmploiTempsUtils.exportToJSON(nom_classe);

  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Content-Disposition', `attachment; filename="emploi_temps_${nom_classe}.json"`);
  res.json(jsonData);
}));

/**
 * POST /api/emploi_temps/import/:nom_classe
 * Importe l'emploi du temps depuis JSON
 */
router.post('/import/:nom_classe', gestionErreurs(async (req, res) => {
  const { nom_classe } = req.params;
  const importData = req.body;

  const result = await EmploiTempsUtils.importFromJSON(importData, nom_classe);

  res.json({
    success: true,
    data: result,
    message: 'Import terminé avec succès'
  });
}));

/**
 * POST /api/emploi_temps/verifier-disponibilite-professeur
 * Vérifie la disponibilité d'un professeur
 */
router.post('/verifier-disponibilite-professeur', gestionErreurs(async (req, res) => {
  const { matricule_professeur, jour_semaine, heure_debut, heure_fin, exclude_id } = req.body;

  if (!matricule_professeur || !jour_semaine || !heure_debut || !heure_fin) {
    return res.status(400).json({
      success: false,
      message: 'Paramètres manquants: matricule_professeur, jour_semaine, heure_debut, heure_fin requis'
    });
  }

  const disponibilite = await EmploiTempsUtils.verifierDisponibiliteProfesseur(
    matricule_professeur, jour_semaine, heure_debut, heure_fin, exclude_id
  );

  res.json({
    success: true,
    data: disponibilite,
    message: disponibilite.disponible ? 'Professeur disponible' : 'Professeur non disponible'
  });
}));

/**
 * POST /api/emploi_temps/verifier-disponibilite-salle
 * Vérifie la disponibilité d'une salle
 */
router.post('/verifier-disponibilite-salle', gestionErreurs(async (req, res) => {
  const { salle, jour_semaine, heure_debut, heure_fin, exclude_id } = req.body;

  if (!salle || !jour_semaine || !heure_debut || !heure_fin) {
    return res.status(400).json({
      success: false,
      message: 'Paramètres manquants: salle, jour_semaine, heure_debut, heure_fin requis'
    });
  }

  const disponibilite = await EmploiTempsUtils.verifierDisponibiliteSalle(
    salle, jour_semaine, heure_debut, heure_fin, exclude_id
  );

  res.json({
    success: true,
    data: disponibilite,
    message: disponibilite.disponible ? 'Salle disponible' : 'Salle non disponible'
  });
}));

/**
 * GET /api/emploi_temps/suggestions/:nom_classe
 * Obtient des suggestions d'optimisation pour l'emploi du temps
 */
router.get('/suggestions/:nom_classe', gestionErreurs(async (req, res) => {
  const { nom_classe } = req.params;

  const suggestions = await EmploiTempsUtils.genererSuggestionsOptimisation(nom_classe);

  res.json({
    success: true,
    data: suggestions,
    count: suggestions.length,
    message: `${suggestions.length} suggestion(s) générée(s) pour la classe ${nom_classe}`
  });
}));

/**
 * POST /api/emploi_temps/copier
 * Copie l'emploi du temps d'une classe vers une autre
 */
router.post('/copier', gestionErreurs(async (req, res) => {
  const { classe_source, classe_destination } = req.body;

  if (!classe_source || !classe_destination) {
    return res.status(400).json({
      success: false,
      message: 'Classe source et destination requises'
    });
  }

  const result = await EmploiTempsUtils.copierEmploiTemps(classe_source, classe_destination);

  res.json({
    success: true,
    data: result,
    message: result.message
  });
}));

module.exports = router;
