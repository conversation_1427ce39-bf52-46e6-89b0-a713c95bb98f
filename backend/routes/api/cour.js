const express = require('express');
const router = express.Router();
const { connection, getPool } = require('../../models/db');

// GET - Liste des cours
router.get('/', (req, res) => {
  const sql = `
    SELECT m.matiere_id, m.nom, m.professeur_matricule, p.nom AS professeur_nom, p.prenom AS professeur_prenom,
           m.nom_classe, c.niveau, c.annee_scolaire
    FROM matiere m
    LEFT JOIN professeur p ON m.professeur_matricule = p.matricule
    LEFT JOIN classe c ON m.nom_classe = c.nom_classe
  `;
  connection.query(sql, (err, result) => {
    if (err) return res.status(500).json(err);
    res.json(result);
  });
});

// GET - Professeurs
router.get('/professeurs', (req, res) => {
  connection.query('SELECT matricule, nom, prenom FROM professeur', (err, result) => {
    if (err) return res.status(500).json(err);
    res.json(result);
  });
});

// GET - Classes
router.get('/classes', (req, res) => {
  connection.query('SELECT nom_classe FROM classe', (err, result) => {
    if (err) return res.status(500).json(err);
    res.json(result);
  });
});

// POST - Ajouter cours
router.post('/', (req, res) => {
  const { nom, professeur_matricule, nom_classe } = req.body;
  connection.query('INSERT INTO matiere (nom, professeur_matricule, nom_classe) VALUES (?, ?, ?)',
    [nom, professeur_matricule, nom_classe],
    err => {
      if (err) return res.status(500).json(err);
      res.sendStatus(200);
    }
  );
});

// PUT - Modifier cours
router.put('/:id', (req, res) => {
  const { nom, professeur_matricule, nom_classe } = req.body;
  connection.query('UPDATE matiere SET nom = ?, professeur_matricule = ?, nom_classe = ? WHERE matiere_id = ?',
    [nom, professeur_matricule, nom_classe, req.params.id],
    err => {
      if (err) return res.status(500).json(err);
      res.sendStatus(200);
    }
  );
});

// DELETE - Supprimer cours
router.delete('/:id', async (req, res) => {
  const matiereId = req.params.id;
  let dbConnection;
  try {
    dbConnection = await getPool().getConnection();
    await dbConnection.beginTransaction();

    await dbConnection.query('DELETE FROM note WHERE matiere_id = ?', [matiereId]);
    await dbConnection.query('DELETE FROM absence WHERE matiere_id = ?', [matiereId]);
    await dbConnection.query('DELETE FROM emploi_temps WHERE matiere_id = ?', [matiereId]);
    await dbConnection.query('DELETE FROM cours WHERE matiere_id = ?', [matiereId]);
    await dbConnection.query('DELETE FROM devoir WHERE matiere_id = ?', [matiereId]);
    await dbConnection.query('DELETE FROM matiere WHERE matiere_id = ?', [matiereId]);

    await dbConnection.commit();
    res.sendStatus(200);
  } catch (error) {
    if (dbConnection) await dbConnection.rollback();
    console.error('Erreur lors de la suppression de la matière :', error);
    res.status(500).json({ message: 'Erreur lors de la suppression.', details: error.message });
  } finally {
    if (dbConnection) dbConnection.release();
  }
});

module.exports = router;
