const express = require('express');
const router = express.Router();
const { connection } = require('../../models/db');
const { fetchUserData } = require('../../middlewares/userDataMiddleware');

// 🔒 Middleware de session
router.use((req, res, next) => {
  if (!req.session.matricule) {
    return res.redirect('/ad');
  }
  next();
});

// 🔹 GET - Afficher tous les bulletins
router.get('/', fetchUserData, (req, res) => {
  const adminMatricule = req.session.matricule;

  connection.query('SELECT * FROM admin WHERE matricule = ?', [adminMatricule], (err, adminRes) => {
    if (err || adminRes.length === 0) return res.status(500).send("Erreur admin");

    const admin = adminRes[0];

    connection.query('SELECT COUNT(*) AS count FROM message WHERE receiver_matricule = ? AND lu = 0'
, [adminMatricule], (err, notifRes) => {
      if (err) return res.status(500).send("Erreur notification");

      const notificationsCount = notifRes[0].count;

      connection.query(`
        SELECT b.*, e.nom, e.prenom, c.nom_classe AS classe FROM bulletin b JOIN eleve e ON b.eleve_matricule = e.matricule JOIN classe c ON e.nom_classe = c.nom_classe 
        ORDER BY b.date_creation DESC;

        
      `, (err, bulletins) => {
        if (err) return res.status(500).send("Erreur bulletins");

        connection.query('SELECT * FROM eleve', (err, eleves) => {
          if (err) return res.status(500).send("Erreur élèves");

          res.render('admin/bulletin', {
            title: 'Gestion des bulletins',
            admin,
            notificationsCount,
            bulletins,
            eleves
          });
        });
      });
    });
  });
});

// 🔹 POST - Ajouter un bulletin
router.post('/ajouter', (req, res) => {
  const { eleve_matricule, periode, moyenne_generale, appreciation } = req.body;
  const date_creation = new Date();

  connection.query('INSERT INTO bulletin (eleve_matricule, periode, moyenne_generale, appreciation, date_creation) VALUES (?, ?, ?, ?, ?)',
    [eleve_matricule, periode, moyenne_generale, appreciation, date_creation],
    (err) => {
      if (err) return res.status(500).send("Erreur insertion bulletin");
      res.redirect('/admin/bulletin');
    }
  );
});

// 🔹 POST - Modifier un bulletin
router.post('/modifier/:id', (req, res) => {
  const { id } = req.params;
  const { periode, moyenne_generale, appreciation } = req.body;

  connection.query('UPDATE bulletin SET periode = ?, moyenne_generale = ?, appreciation = ? WHERE id = ?',
    [periode, moyenne_generale, appreciation, id],
    (err) => {
      if (err) return res.status(500).send("Erreur modification bulletin");
      res.redirect('/admin/bulletin');
    }
  );
});

// 🔹 POST - Supprimer un bulletin
router.post('/supprimer/:id', (req, res) => {
  const { id } = req.params;

  connection.query('DELETE FROM bulletin WHERE id = ?', [id], (err) => {
    if (err) return res.status(500).send("Erreur suppression bulletin");
    res.redirect('/admin/bulletin');
  });
});

module.exports = router;
