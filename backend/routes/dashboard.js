/**
 * Dashboard Routes for NS School Manager
 * Handles all dashboard-related routes with proper authentication
 */

const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');
const { fetchUserData } = require('../middlewares/userDataMiddleware');

// Middleware to check authentication
const isAuthenticated = (req, res, next) => {
  if (req.session && req.session.matricule && req.session.role) {
    return next();
  }
  return res.redirect('/');
};

// Middleware to check admin role
const isAdmin = (req, res, next) => {
  if (req.session && req.session.role === 'admin') {
    return next();
  }
  return res.redirect('/ad');
};

// Middleware to check student role
const isStudent = (req, res, next) => {
  if (req.session && req.session.role === 'eleve') {
    return next();
  }
  return res.redirect('/');
};

// Middleware to check professor role
const isProfessor = (req, res, next) => {
  if (req.session && req.session.role === 'professeur') {
    return next();
  }
  return res.redirect('/');
};

// Dashboard routes
router.get('/dashboard-admin', isAuthenticated, isAdmin, fetchUserData, dashboardController.adminDashboard);
router.get('/dashboard-eleve', isAuthenticated, isStudent, fetchUserData, dashboardController.studentDashboard);
router.get('/dashboard-prof', isAuthenticated, isProfessor, fetchUserData, dashboardController.professorDashboard);

module.exports = router;
