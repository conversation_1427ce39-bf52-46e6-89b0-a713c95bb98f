# 🗄️ Database Commands Reference

This guide explains all available database commands for NS School Manager.

## 📋 Command Overview

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run db:reset` | **Complete reset** - Drop database, recreate tables, seed data | First setup, major issues, fresh start |
| `npm run db:clear` | **Clear and seed** - Clear existing data, add fresh seed data | Reset data but keep table structure |
| `npm run db:seed-only` | **Add seed data** - Add sample data without clearing existing | Add test data to existing database |

## 🔄 Detailed Command Explanations

### `npm run db:reset` (Recommended for Development)

**What it does:**
1. 🗑️ Drops the entire database if it exists
2. 📦 Creates a fresh database
3. 🏗️ Creates all tables with proper structure
4. 🌱 Seeds with complete sample data
5. 🔑 Displays login credentials

**Use when:**
- First time setup
- Major database structure changes
- Complete fresh start needed
- Database corruption issues

**Example output:**
```
🔄 NS School Manager - Complete Database Reset
🛠️  MySQL connection established
🗑️  Dropping database "nsschoolmanager" if exists...
📦 Creating fresh database "nsschoolmanager"...
🏗️  Creating database tables...
📄 Table "utilisateur" created
📄 Table "classe" created
... (all tables)
✅ All tables created successfully
🌱 Now running database seeding...
✅ Created 12 users
✅ Created 2 admin profiles
... (all seed data)
🔑 LOGIN CREDENTIALS FOR TESTING
👨‍💼 ADMIN ACCOUNTS:
  Matricule: ADM001 | Password: admin123
```

### `npm run db:clear`

**What it does:**
1. 🧹 Clears all existing data from tables
2. 🌱 Adds fresh seed data
3. 📊 Maintains existing table structure

**Use when:**
- Want to reset data but keep table structure
- Testing with fresh data
- Database exists but need clean slate

### `npm run db:seed-only`

**What it does:**
1. ➕ Adds sample data to existing tables
2. 🔒 Keeps existing data intact
3. ⚠️ May cause conflicts if data already exists

**Use when:**
- Adding test data to existing database
- Want to supplement existing data
- Testing with additional sample data

**Note:** May fail if conflicting data exists (duplicate matricules, etc.)

### `npm run db:init`

**What it does:**
1. 📦 Creates database if it doesn't exist
2. 🏗️ Creates all tables with `CREATE TABLE IF NOT EXISTS`
3. 🚫 Does NOT add any sample data

**Use when:**
- Production setup
- Want empty database structure
- Manual data entry planned

## 🎯 Common Scenarios

### Scenario 1: First Time Setup
```bash
npm run db:reset
npm run dev
# Visit http://localhost:8080/ad and login with ADM001/admin123
```

### Scenario 2: Development Reset
```bash
# When you want to start fresh during development
npm run db:reset
```

### Scenario 3: Production Setup
```bash
# Create empty database structure for production
npm run db:init
# Then manually add real users via admin interface
```

### Scenario 4: Add Test Data to Existing Database
```bash
# Add sample data without affecting existing structure
npm run db:seed-only
```

### Scenario 5: Reset Data Only
```bash
# Keep table structure, reset data
npm run db:clear
```

## 🔍 Troubleshooting

### "Database already exists" error
```bash
# Use complete reset to drop and recreate
npm run db:reset
```

### "Table already exists" error
```bash
# Tables exist but may be empty, try seeding only
npm run db:seed-only
```

### "Duplicate entry" error
```bash
# Data conflicts exist, clear and reseed
npm run db:clear
```

### "Connection refused" error
```bash
# Check MySQL is running
sudo service mysql start
# Or on macOS
brew services start mysql
```

### "Access denied" error
```bash
# Check database credentials in .env file
cat .env | grep DB_
```

## 📊 Sample Data Overview

When seeding is run, you get:

### Users (12 total)
- **2 Admins**: ADM001, ADM002 (password: admin123)
- **4 Teachers**: PROF001-004 (password: prof123)
- **6 Students**: ELV001-006 (password: eleve123)

### Classes (5 total)
- 6eme A, 6eme B, 5eme A, 4eme A, 3eme A

### Subjects (8 assignments)
- Math, French, Physics, History across different classes

### Sample Courses (4 total)
- Ready-to-download course materials

### Complete Profiles
- Names, emails, phone numbers
- Class assignments
- Subject specializations

## 🔐 Security Notes

- All passwords are properly hashed with bcrypt
- Sample data uses realistic but fake information
- Production databases should use `db:init` only
- Never commit real credentials to version control

## 🚀 Quick Reference

```bash
# Most common commands for development:

# Fresh start (recommended)
npm run db:reset

# Start application
npm run dev

# View logs
tail -f logs/app.log

# Check database status
mysql -u root -p -e "SHOW DATABASES; USE nsschoolmanager; SHOW TABLES;"
```

---

**Need help?** Check [TESTING_GUIDE.md](./TESTING_GUIDE.md) for detailed testing scenarios or [README.md](./README.md) for complete documentation.
