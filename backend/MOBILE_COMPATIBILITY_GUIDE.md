# Mobile Compatibility Implementation Guide

## Overview

This guide documents the changes made to make the NS School Manager backend compatible with mobile applications while maintaining full backward compatibility with the existing web version.

## ✅ Implemented Features

### 1. CORS Configuration
- **File**: `backend/app.js`
- **Purpose**: Allow mobile apps to make cross-origin requests
- **Features**:
  - **Allows all origins** for development simplicity
  - **Allows all common HTTP methods** (GET, POST, PUT, DELETE, OPTIONS, PATCH)
  - Credentials support for session-based auth
  - Proper headers for JWT tokens
  - Easy to configure for production later

### 2. JWT Authentication System
- **File**: `backend/utils/jwt.js`
- **Purpose**: Token-based authentication for mobile apps
- **Features**:
  - Access token generation
  - Refresh token support
  - Token verification and validation
  - Configurable expiry times
  - Proper error handling

### 3. Hybrid Authentication Middleware
- **File**: `backend/middlewares/hybridAuth.js`
- **Purpose**: Support both session (web) and JWT (mobile) authentication
- **Features**:
  - Automatic detection of auth method
  - Role-based access control
  - API-only and web-only modes
  - Backward compatibility with existing session auth

### 4. Enhanced Authentication Routes
- **File**: `backend/routes/auth-unified.js`
- **Purpose**: Support both authentication methods in login endpoints
- **Features**:
  - Automatic detection of API vs web requests
  - JWT token generation for mobile
  - Session creation for web
  - Token refresh endpoint
  - Enhanced status endpoint

### 5. Standardized API Responses
- **File**: `backend/utils/apiResponse.js`
- **Purpose**: Consistent response format for mobile apps
- **Features**:
  - Success/error response helpers
  - Validation error handling
  - Database error handling
  - Pagination support
  - Async error wrapper

## 🔄 Authentication Flow

### Web Authentication (Existing - Unchanged)
1. User submits login form
2. Server validates credentials
3. Session is created and stored
4. User is redirected to dashboard
5. Subsequent requests use session cookies

### Mobile Authentication (New)
1. App sends login request with `api: true` flag or Authorization header
2. Server validates credentials
3. JWT access and refresh tokens are generated
4. Tokens are returned to the app
5. App stores tokens securely
6. Subsequent requests include `Authorization: Bearer <token>` header

## 📱 Mobile App Integration

### Login Request
```javascript
// Mobile login request
const response = await fetch('http://localhost:8080/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    matricule: 'user123',
    password: 'password',
    role: 'eleve', // or 'professeur', 'admin'
    api: true // This flag indicates mobile/API request
  })
});

const data = await response.json();
if (data.success) {
  // Store tokens securely
  const { accessToken, refreshToken } = data.tokens;
  // Use accessToken for subsequent requests
}
```

### Authenticated Requests
```javascript
// Making authenticated requests
const response = await fetch('http://localhost:8080/api/eleve', {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  }
});
```

### Token Refresh
```javascript
// Refresh expired token
const response = await fetch('http://localhost:8080/api/auth/refresh', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    refreshToken: refreshToken
  })
});
```

## 🛡️ Security Considerations

### JWT Configuration
- Tokens are signed with a secret key (configure `JWT_SECRET` in environment)
- Access tokens expire in 24 hours by default
- Refresh tokens expire in 7 days
- Tokens include issuer and audience claims

### CORS Security
- **Currently allows all origins** for development ease
- Credentials are enabled for session-based auth
- **For production**: Update CORS configuration to restrict origins to your specific mobile app and web domains

## 🔧 Configuration

### Environment Variables
Add these to your `.env` file:
```env
JWT_SECRET=your-super-secret-jwt-key-here
SESSION_SECRET=your-session-secret-here
FRONTEND_URL=http://localhost:3000
```

### Production CORS Configuration
For production, update CORS configuration in `app.js` to restrict origins:
```javascript
const corsOptions = {
  origin: [
    'https://your-web-app.com',     // Your web application
    'https://your-mobile-app.com',  // Your mobile app domain
    'exp://your-expo-app',          // Expo published app
    'capacitor://localhost',        // Capacitor apps
    'ionic://localhost'             // Ionic apps
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Authorization']
};
```

## 🧪 Testing

### Manual Testing
1. Start the backend: `npm start`
2. Run the test script: `node test-mobile-auth.js`
3. Check that both web and mobile authentication work

### API Endpoints for Mobile
- `POST /api/auth/login` - Login with JWT tokens
- `POST /api/auth/login_ad` - Admin login with JWT tokens
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/status` - Check authentication status
- `POST /api/auth/logout` - Logout (destroys session)

## 🔄 Backward Compatibility

### Web Version Unchanged
- All existing web functionality continues to work
- Session-based authentication is preserved
- Existing middleware and routes are compatible
- No changes required to frontend code

### Gradual Migration
- API endpoints support both authentication methods
- Can migrate endpoints individually
- Hybrid middleware allows mixed usage
- No breaking changes to existing functionality

## 📋 Next Steps

1. **Mobile App Development**: Update mobile app to use JWT authentication
2. **Error Handling**: Implement proper error handling in mobile app
3. **Token Storage**: Use secure storage for tokens in mobile app
4. **Offline Support**: Consider implementing offline capabilities
5. **Push Notifications**: Add push notification support
6. **API Documentation**: Create comprehensive API documentation

## 🐛 Troubleshooting

### Common Issues
1. **CORS Errors**: Check that mobile app origin is in allowed list
2. **Token Expired**: Implement automatic token refresh
3. **Invalid Token**: Check token format and signature
4. **Session Conflicts**: Ensure mobile app doesn't send session cookies

### Debug Mode
Set `NODE_ENV=development` to see detailed error messages and enable CORS for all origins during development.
