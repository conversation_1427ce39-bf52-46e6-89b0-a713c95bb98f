/**
 * Configuration module for NS School Manager
 * Centralizes all environment variables and configuration settings
 */

require('dotenv').config();

const config = {
  // Application settings
  app: {
    name: 'NS School Manager',
    version: '1.0.0',
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 8080,
    baseUrl: process.env.BASE_URL || 'http://localhost:8080',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000'
  },

  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    name: process.env.DB_NAME || 'nsschoolmanager',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    charset: 'utf8mb4',
    timezone: '+00:00'
  },

  // Session configuration
  session: {
    secret: process.env.SESSION_SECRET || 'fallback-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: parseInt(process.env.SESSION_MAX_AGE) || 86400000 // 24 hours
    },
    name: 'ns-school-session'
  },

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback-jwt-secret-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: 'ns-school-manager',
    audience: 'ns-school-users'
  },

  // File upload configuration
  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10485760, // 10MB
    allowedTypes: (process.env.UPLOAD_ALLOWED_TYPES || 'pdf,doc,docx,ppt,pptx,xls,xlsx,txt,jpg,png,zip').split(','),
    destination: 'uploads/',
    coursesPath: 'uploads/cours/',
    devoirsPath: 'uploads/devoirs/'
  },

  // Email configuration
  email: {
    host: process.env.SMTP_HOST || '',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: false,
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASSWORD || ''
    },
    from: process.env.SMTP_FROM || '<EMAIL>'
  },

  // Security settings
  security: {
    bcryptRounds: 10,
    maxLoginAttempts: 5,
    lockoutTime: 15 * 60 * 1000, // 15 minutes
    passwordMinLength: 6,
    sessionTimeout: 24 * 60 * 60 * 1000 // 24 hours
  },

  // Rate limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    downloadMax: 50 // max downloads per hour per user
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: '10m',
    maxFiles: '5d'
  }
};

// Validation function
function validateConfig() {
  const errors = [];

  // Check required environment variables
  if (!config.session.secret || config.session.secret === 'fallback-secret-key-change-in-production') {
    errors.push('SESSION_SECRET must be set in production');
  }

  if (!config.jwt.secret || config.jwt.secret === 'fallback-jwt-secret-change-in-production') {
    errors.push('JWT_SECRET must be set in production');
  }

  if (!config.database.name) {
    errors.push('DB_NAME is required');
  }

  if (!config.database.user) {
    errors.push('DB_USER is required');
  }

  // Warn about production settings
  if (config.app.env === 'production') {
    if (!config.session.cookie.secure) {
      console.warn('⚠️  Warning: SESSION_COOKIE_SECURE should be true in production');
    }

    if (config.database.password === '') {
      console.warn('⚠️  Warning: Database password is empty in production');
    }
  }

  if (errors.length > 0) {
    console.error('❌ Configuration errors:');
    errors.forEach(error => console.error(`   - ${error}`));
    process.exit(1);
  }

  console.log('✅ Configuration validated successfully');
}

// Export configuration and validation
module.exports = {
  ...config,
  validate: validateConfig
};
