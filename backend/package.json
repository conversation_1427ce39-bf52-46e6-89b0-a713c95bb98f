{"name": "backend", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "db:reset": "node scripts/resetDatabase.js", "db:clear": "node seed.js", "db:seed-only": "node seed.js --no-clear", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^3.1.4"}, "description": ""}