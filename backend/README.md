# NS School Manager - Backend

A professional school management system built with Node.js, Express, and MySQL.

## 🚀 Features

- **User Management**: Admin-only registration system for students, teachers, and admins
- **Course Management**: File upload/download system with security controls
- **Role-based Access Control**: Different permissions for admins, teachers, and students
- **Professional Architecture**: Centralized configuration, logging, and error handling
- **Security**: Password hashing, session management, file validation
- **Database**: MySQL with proper connection pooling and error handling

## 📋 Prerequisites

- Node.js (v14 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ns-school-manager/backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your configuration:
   ```env
   # Database Configuration (MySQL)
   DB_HOST=localhost
   DB_PORT=3306
   DB_NAME=ns_school
   DB_USER=root
   DB_PASSWORD=your_mysql_password

   # Session Configuration
   SESSION_SECRET=your-super-secret-session-key-here
   
   # Application Configuration
   PORT=8080
   NODE_ENV=development
   ```

4. **Database Setup**
   ```bash
   # Complete reset: Drop database, recreate tables, and seed data (recommended)
   npm run db:reset

   # Or step by step:
   # 1. Clear existing data and add fresh seed data
   npm run db:clear

   # 2. Add seed data without clearing existing data
   npm run db:seed-only
   ```

5. **Start the application**
   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm start
   ```

6. **Testing the Application**

   After seeding, you can login with these test accounts:

   **Admin Login** (http://localhost:8080/ad):
   - Matricule: `ADM001` | Password: `admin123`

   **Teacher Login** (http://localhost:8080/):
   - Matricule: `PROF001` | Password: `prof123` | Role: Professeur

   **Student Login** (http://localhost:8080/):
   - Matricule: `ELV001` | Password: `eleve123` | Role: Élève

   📖 **See [TESTING_GUIDE.md](./TESTING_GUIDE.md) for complete testing instructions**

## 📁 Project Structure

```
backend/
├── config/
│   └── config.js              # Centralized configuration
├── controllers/
│   ├── authcontroller.js      # Authentication logic
│   ├── authcontroller2.js     # Admin authentication
│   └── dashboardController.js # Dashboard logic
├── middlewares/
│   ├── authmiddleware2.js     # Authentication middleware
│   ├── errorHandler.js        # Error handling middleware
│   └── fileAccess.js          # File access control
├── models/
│   ├── db.js                  # Database connection
│   └── setup.js               # Database setup script
├── routes/
│   ├── admin/                 # Admin routes
│   ├── api/                   # API routes
│   ├── eleve/                 # Student routes
│   ├── prof/                  # Teacher routes
│   ├── auth.js                # Authentication routes
│   └── dashboard.js           # Dashboard routes
├── utils/
│   └── logger.js              # Professional logging system
├── uploads/
│   └── cours/                 # Course file uploads
├── logs/                      # Application logs
├── app.js                     # Main application
├── createUser.js              # User creation utility
└── .env                       # Environment variables
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development/production) | development |
| `PORT` | Server port | 8080 |
| `DB_HOST` | MySQL host | localhost |
| `DB_PORT` | MySQL port | 3306 |
| `DB_NAME` | Database name | ns_school |
| `DB_USER` | Database user | root |
| `DB_PASSWORD` | Database password | (empty) |
| `SESSION_SECRET` | Session secret key | (required) |
| `JWT_SECRET` | JWT secret key | (required) |
| `UPLOAD_MAX_SIZE` | Max file upload size | ******** (10MB) |

### Database Configuration

The application uses MySQL with the following tables:
- `utilisateur` - User accounts
- `admin` - Admin details
- `professeur` - Teacher details
- `eleve` - Student details
- `classe` - Classes
- `matiere` - Subjects
- `cours` - Courses
- `note` - Grades
- `absence` - Absences
- `activite` - Activity logs

## 🔐 Security Features

- **Password Hashing**: bcrypt with configurable rounds
- **Session Management**: Secure session configuration
- **File Upload Security**: Type and size validation
- **Rate Limiting**: Configurable request limits
- **Error Handling**: No sensitive data exposure
- **Activity Logging**: Comprehensive audit trail

## 📊 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/admin-login` - Admin login
- `GET /logout` - User logout

### Admin Registration
- `GET /admin/registration` - Registration page
- `POST /admin/registration/register` - Create new user
- `GET /admin/utilisateur/api` - List users
- `GET /admin/utilisateur/check/:matricule` - Check matricule availability

### Course Management
- `POST /professeur/cours/ajouter` - Upload course file
- `GET /professeur/cours/download/:id` - Download course (teacher)
- `DELETE /professeur/cours/api/:id` - Delete course
- `GET /eleve/cours/api` - List student courses
- `GET /api/download/cours/:id` - Secure download
- `GET /api/download/cours/:id/info` - File information

## 🔍 Logging

The application includes comprehensive logging:
- **Request Logging**: All HTTP requests
- **Error Logging**: Application errors with stack traces
- **Database Logging**: Query performance and errors
- **Authentication Logging**: Login attempts and sessions
- **File Logging**: Upload/download activities

Logs are stored in the `logs/` directory:
- `app.log` - General application logs
- `error.log` - Error-specific logs

## 🚨 Error Handling

Professional error handling includes:
- **Global Error Handler**: Catches all unhandled errors
- **Database Error Handler**: MySQL-specific error handling
- **Validation Error Handler**: Input validation errors
- **File Upload Error Handler**: Multer-specific errors
- **404 Handler**: Route not found errors

## 🧪 Development

### Scripts
```bash
npm run dev      # Start with nodemon
npm start        # Start production server
npm test         # Run tests (if available)
```

### Creating Users
```bash
# Edit createUser.js with desired user data
node createUser.js
```

## 🚀 Production Deployment

1. **Environment Setup**
   ```bash
   NODE_ENV=production
   SESSION_SECRET=strong-production-secret
   JWT_SECRET=strong-jwt-secret
   ```

2. **Database Configuration**
   - Use connection pooling
   - Enable SSL if required
   - Set proper timeouts

3. **Security Considerations**
   - Use HTTPS
   - Set secure session cookies
   - Configure proper CORS
   - Enable rate limiting

4. **Monitoring**
   - Monitor logs in `logs/` directory
   - Set up log rotation
   - Monitor database performance

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions, please contact the development team.
