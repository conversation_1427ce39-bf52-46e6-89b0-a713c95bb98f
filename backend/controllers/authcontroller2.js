/**
 * Admin Authentication Controller for NS School Manager
 * Handles admin-specific authentication with enhanced security
 */

const bcrypt = require('bcrypt');
const connection = require('../models/db');

exports.login = async (req, res) => {
  try {
    const { matricule, password } = req.body;

    // Input validation
    if (!matricule || !password) {
      return res.status(400).json({
        success: false,
        message: "Veuillez remplir tous les champs."
      });
    }

    connection.query(
    'SELECT * FROM utilisateur WHERE matricule = ? AND role = "admin"',
    [matricule],
    async (err, results) => {
      try {
        if (err) {
          console.error("Erreur SQL :", err);
          return res.status(500).json({ message: "Erreur de base de données." });
        }

        if (results.length === 0) {
          return res.status(401).json({ message: "Admin non trouvé." });
        }

        const utilisateur = results[0];

        const passwordMatch = await bcrypt.compare(password, utilisateur.password);
        if (!passwordMatch) {
          return res.status(401).json({ message: "Mot de passe incorrect." });
        }

        // ✅ Stockage en session
        req.session.matricule = utilisateur.matricule;
        req.session.role = utilisateur.role;

        return res.status(200).json({ message: "Connexion réussie", redirect: "/dashboard-admin" });
      } catch (error) {
        console.error("Erreur interne :", error);
        return res.status(500).json({ message: "Erreur interne du serveur." });
      }
    }
  );
  } catch (error) {
    console.error("Erreur inattendue :", error);
    return res.status(500).json({ message: "Une erreur inattendue est survenue." });
  }
};
