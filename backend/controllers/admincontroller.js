// controllers/admincontroller.js
const db = require('../models/db');

exports.renderDashboard = (req, res) => {
  const stats = {};

  // Requêtes pour récupérer les statistiques
  const sqlEleves = 'SELECT COUNT(*) AS count FROM eleve;';
  const sqlProfs = 'SELECT COUNT(*) AS count FROM professeur;';
  const sqlClasses = 'SELECT COUNT(*) AS count FROM classe;';
  const sqlAbsences = 'SELECT COUNT(*) AS count FROM absence;';

  Promise.all([
    new Promise((resolve, reject) => {
      db.query(sqlEleves, (err, result) => {
        if (err) reject(err);
        stats.eleves = result[0].count;
        resolve();
      });
    }),
    new Promise((resolve, reject) => {
      db.query(sqlProfs, (err, result) => {
        if (err) reject(err);
        stats.profs = result[0].count;
        resolve();
      });
    }),
    new Promise((resolve, reject) => {
      db.query(sqlClasses, (err, result) => {
        if (err) reject(err);
        stats.classes = result[0].count;
        resolve();
      });
    }),
    new Promise((resolve, reject) => {
      db.query(sqlAbsences, (err, result) => {
        if (err) reject(err);
        stats.absences = result[0].count;
        resolve();
      });
    }),
  ])
  .then(() => {
    // Récupérer les 3 dernières activités
    const sqlActivites = `
      SELECT a.action, a.date AS date, a.statut,
             u.matricule, u.role,
             COALESCE(e.nom, p.nom, pr.nom, a2.nom) AS nom,
             COALESCE(e.prenom, p.prenom, pr.prenom, a2.prenom) AS prenom
      FROM activite a
      JOIN utilisateur u ON a.utilisateur_id = u.matricule
      LEFT JOIN eleve e ON u.matricule = e.matricule
      LEFT JOIN parent p ON u.matricule = p.matricule
      LEFT JOIN professeur pr ON u.matricule = pr.matricule
      LEFT JOIN admin a2 ON u.matricule = a2.matricule
      ORDER BY a.date DESC
      LIMIT 3
    `;

    db.query(sqlActivites, (err, result) => {
      if (err) {
        console.error('Erreur lors de la récupération des activités:', err);
        return res.status(500).send('Erreur serveur');
      }

      const activites = result.map((a) => {
        const initials = (a.nom?.[0] || '') + (a.prenom?.[0] || '');
        const date = new Date(a.date).toLocaleDateString('fr-FR');
        let statutClass = 'bg-secondary';
        if (a.statut === 'Validé') statutClass = 'bg-success';
        else if (a.statut === 'En attente') statutClass = 'bg-warning text-dark';
        else if (a.statut === 'En cours') statutClass = 'bg-info text-dark';

        return {
          utilisateur: {
            initials,
            fullName: `${a.nom} ${a.prenom}`,
            role: a.role,
          },
          action: a.action,
          date,
          statut: a.statut,
          statutClass,
        };
      });

      res.render('dashboard_admin', { stats, activites });
    });
  })
  .catch((err) => {
    console.error('Erreur lors de la récupération des statistiques:', err);
    res.status(500).send('Erreur serveur');
  });
};