/**
 * Authentication Controller for NS School Manager
 * Handles user authentication with proper security measures
 */

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const { connection } = require('../models/db');

exports.login = async (req, res) => {
  try {
    const { matricule, password, role, remember } = req.body;

    // Input validation
    if (!matricule || !password || !role) {
      return res.status(400).json({
        success: false,
        message: "Veuillez remplir tous les champs."
      });
    }

    // Validate role
    const validRoles = ['admin', 'eleve', 'professeur'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: "Rôle invalide."
      });
    }

    // Query user from database
    connection.query(
      'SELECT * FROM utilisateur WHERE matricule = ? AND role = ?',
      [matricule, role],
      async (err, results) => {
        if (err) {
          console.error("❌ Database error during login:", err);
          return res.status(500).json({
            success: false,
            message: "Erreur de base de données."
          });
        }

        if (results.length === 0) {
          return res.status(401).json({
            success: false,
            message: "Identifiants incorrects."
          });
        }

        const utilisateur = results[0];

        // Verify password
        const passwordMatch = await bcrypt.compare(password, utilisateur.password);
        if (!passwordMatch) {
          return res.status(401).json({
            success: false,
            message: "Identifiants incorrects."
          });
        }

        // Store user session
        req.session.matricule = utilisateur.matricule;
        req.session.role = utilisateur.role;

        // Set session expiry based on remember option
        if (remember) {
          req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
        } else {
          req.session.cookie.maxAge = config.session.cookie.maxAge; // Default from config
        }

        // Log successful login
        const loginActivity = `INSERT INTO activite (utilisateur_id, action, date, statut) VALUES (?, ?, NOW(), ?)`;
        connection.query(loginActivity, [matricule, 'Connexion', 'Succès'], (err) => {
          if (err) console.error('❌ Error logging activity:', err);
        });

        // Determine redirect URL based on role
        let redirectURL = '/';
        switch (role) {
          case 'admin':
            redirectURL = '/dashboard-admin';
            break;
          case 'professeur':
            redirectURL = '/dashboard-prof';
            break;
          case 'eleve':
            redirectURL = '/dashboard-eleve';
            break;
          default:
            redirectURL = '/';
        }

        return res.status(200).json({
          success: true,
          message: "Connexion réussie",
          redirect: redirectURL,
          user: {
            matricule: utilisateur.matricule,
            role: utilisateur.role
          }
        });
      }
    );

  } catch (error) {
    console.error("❌ Login error:", error);
    return res.status(500).json({
      success: false,
      message: "Erreur serveur interne."
    });
  }
};
