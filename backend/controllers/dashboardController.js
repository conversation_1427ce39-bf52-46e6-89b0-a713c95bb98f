/**
 * Dashboard Controller for NS School Manager
 * Handles dashboard routes for different user roles
 */

const { connection } = require('../models/db');

/**
 * Admin Dashboard
 */
exports.adminDashboard = async (req, res) => {
  try {
    if (req.session.role !== 'admin') {
      return res.redirect('/ad');
    }

    const stats = {};
    const activites = [];

    // Get statistics using promises for better error handling
    const getStats = () => {
      return new Promise((resolve, reject) => {
        const queries = [
          'SELECT COUNT(*) AS count FROM eleve',
          'SELECT COUNT(*) AS count FROM professeur', 
          'SELECT COUNT(*) AS count FROM classe',
          'SELECT COUNT(*) AS count FROM absence'
        ];

        let completed = 0;
        const results = {};

        queries.forEach((query, index) => {
          connection.query(query, (err, result) => {
            if (err) {
              reject(err);
              return;
            }

            const keys = ['eleves', 'profs', 'classes', 'absences'];
            results[keys[index]] = result[0].count;
            completed++;

            if (completed === queries.length) {
              resolve(results);
            }
          });
        });
      });
    };

    // Get recent activities
    const getActivities = () => {
      return new Promise((resolve, reject) => {
        const sql = `
          SELECT a.action, a.date, a.statut, a.utilisateur_id,
                 COALESCE(e.nom, p.nom, ad.nom) as nom,
                 COALESCE(e.prenom, p.prenom, ad.prenom) as prenom,
                 u.role
          FROM activite a
          JOIN utilisateur u ON a.utilisateur_id = u.matricule
          LEFT JOIN eleve e ON u.matricule = e.matricule
          LEFT JOIN professeur p ON u.matricule = p.matricule
          LEFT JOIN admin ad ON u.matricule = ad.matricule
          ORDER BY a.date DESC
          LIMIT 5
        `;

        connection.query(sql, (err, results) => {
          if (err) {
            reject(err);
            return;
          }

          const activities = results.map(row => {
            const initials = `${row.prenom?.charAt(0) || ''}${row.nom?.charAt(0) || ''}`.toUpperCase();
            return {
              utilisateur: {
                initials,
                fullName: `${row.prenom || ''} ${row.nom || ''}`.trim(),
                role: row.role
              },
              action: row.action,
              date: row.date,
              statut: row.statut,
              statutClass: getStatutClass(row.statut)
            };
          });

          resolve(activities);
        });
      });
    };

    // Execute both queries
    const [statsResults, activitiesResults] = await Promise.all([
      getStats(),
      getActivities()
    ]);

    res.render('dashboard_admin', { 
      stats: statsResults, 
      activites: activitiesResults,
      matricule: req.session.matricule,
      role: req.session.role
    });

  } catch (error) {
    console.error('❌ Error in admin dashboard:', error);
    res.status(500).send('Erreur lors du chargement du tableau de bord');
  }
};

/**
 * Student Dashboard
 */
exports.studentDashboard = async (req, res) => {
  try {
    const matricule = req.session.matricule;
    
    if (!matricule) {
      return res.redirect('/');
    }

    const data = {
      note: 'N/A',
      programme: 'Semaine',
      cours: 'Mathématiques',
      absence: '0 absence(s) - ---',
      activites: []
    };

    // Get student average grade
    const getAverage = () => {
      return new Promise((resolve) => {
        connection.query(
          'SELECT AVG(note) AS moyenne FROM note WHERE eleve_matricule = ?',
          [matricule],
          (err, result) => {
            if (!err && result[0] && result[0].moyenne !== null && typeof result[0].moyenne === 'number') {
              resolve(parseFloat(result[0].moyenne).toFixed(2));
            } else {
              resolve('N/A');
            }
          }
        );
      });
    };

    // Get student absences
    const getAbsences = () => {
      return new Promise((resolve) => {
        connection.query(
          'SELECT COUNT(*) AS total_absences, MAX(date_absence) AS last_date FROM absence WHERE eleve_matricule = ?',
          [matricule],
          (err, result) => {
            if (!err && result[0]) {
              const totalAbs = result[0].total_absences || 0;
              const lastDate = result[0].last_date
                ? new Date(result[0].last_date).toLocaleDateString('fr-FR')
                : '---';
              resolve(`${totalAbs} absence(s) - ${lastDate}`);
            } else {
              resolve('0 absence(s) - ---');
            }
          }
        );
      });
    };

    // Get student activities
    const getActivities = () => {
      return new Promise((resolve) => {
        const sql = `
          SELECT a.action, a.date, a.statut,
                 e.nom, e.prenom, u.role
          FROM activite a
          JOIN utilisateur u ON a.utilisateur_id = u.matricule
          LEFT JOIN eleve e ON u.matricule = e.matricule
          WHERE a.utilisateur_id = ?
          ORDER BY a.date DESC
          LIMIT 3
        `;

        connection.query(sql, [matricule], (err, results) => {
          if (!err && results.length > 0) {
            const activities = results.map(row => {
              const initials = `${row.prenom?.charAt(0) || ''}${row.nom?.charAt(0) || ''}`.toUpperCase();
              return {
                utilisateur: {
                  initials,
                  fullName: `${row.prenom || ''} ${row.nom || ''}`.trim(),
                  role: row.role
                },
                action: row.action,
                date: row.date,
                statut: row.statut,
                statutClass: getStatutClass(row.statut)
              };
            });
            resolve(activities);
          } else {
            resolve([]);
          }
        });
      });
    };

    // Execute all queries
    const [average, absences, activities] = await Promise.all([
      getAverage(),
      getAbsences(),
      getActivities()
    ]);

    data.note = average;
    data.absence = absences;
    data.activites = activities;

    res.render('dashboard_eleve', { 
      ...data,
      matricule: req.session.matricule,
      role: req.session.role
    });

  } catch (error) {
    console.error('❌ Error in student dashboard:', error);
    res.status(500).send('Erreur lors du chargement du tableau de bord');
  }
};

/**
 * Professor Dashboard
 */
exports.professorDashboard = async (req, res) => {
  try {
    const matricule = req.session.matricule;

    if (!matricule) {
      return res.redirect('/');
    }

    const { query } = require('../models/db');

    // Get professor information
    const professorQuery = `
      SELECT p.*, u.role
      FROM professeur p
      JOIN utilisateur u ON p.matricule = u.matricule
      WHERE p.matricule = ?
    `;
    const professorResult = await query(professorQuery, [matricule]);
    const professor = professorResult[0];

    // Initialize stats object
    const stats = { cours: 0, eleves: 0, devoirs: 0, absences: 0 };

    // Get number of courses (matiere) assigned to the professor
    const coursQuery = 'SELECT COUNT(*) AS total FROM matiere WHERE professeur_matricule = ?';
    const coursResult = await query(coursQuery, [matricule]);
    stats.cours = coursResult[0].total;

    // Get number of students in the professor's classes
    const elevesQuery = `
      SELECT COUNT(DISTINCT e.matricule) AS total
      FROM eleve e
      JOIN matiere m ON e.nom_classe = m.nom_classe
      WHERE m.professeur_matricule = ?
    `;
    const elevesResult = await query(elevesQuery, [matricule]);
    stats.eleves = elevesResult[0].total || 0;

    // Get number of assignments/devoirs given by the professor
    const devoirsQuery = 'SELECT COUNT(*) AS total FROM devoir WHERE matricule = ?';
    const devoirsResult = await query(devoirsQuery, [matricule]);
    stats.devoirs = devoirsResult[0].total;

    // Get number of absence days in the professor's classes
    const absencesQuery = `
      SELECT COUNT(DISTINCT a.date_absence) AS jours
      FROM absence a
      JOIN matiere m ON a.matiere_id = m.matiere_id
      WHERE m.professeur_matricule = ?
    `;
    const absencesResult = await query(absencesQuery, [matricule]);
    stats.absences = absencesResult[0].jours;

    // Create activites array with professor information
    const activites = [];
    if (professor) {
      // Extract first name and last name
      const nameParts = professor.nom.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';
      const initials = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || 'PR';

      // Add a sample activity entry for the template
      activites.push({
        utilisateur: {
          fullName: professor.nom,
          initials: initials
        },
        type: 'Connexion',
        description: 'Connexion au tableau de bord',
        date: new Date().toLocaleDateString('fr-FR'),
        statut: 'Actif'
      });
    }

    res.render('dashboard_prof', {
      matricule: req.session.matricule,
      role: req.session.role,
      activites: activites,
      professor: professor,
      stats: stats
    });

  } catch (error) {
    console.error('❌ Error in professor dashboard:', error);
    res.status(500).send('Erreur lors du chargement du tableau de bord');
  }
};

/**
 * Utility function to get status CSS class
 */
function getStatutClass(statut) {
  switch (statut?.toLowerCase()) {
    case 'succès':
    case 'validé': 
      return 'bg-success';
    case 'lu': 
      return 'bg-info text-dark';
    case 'en cours': 
      return 'bg-warning text-dark';
    default: 
      return 'bg-secondary';
  }
}
