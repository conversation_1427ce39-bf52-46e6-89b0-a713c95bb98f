/**
 * User Creation Utility for NS School Manager
 * Professional script to create users with proper configuration
 */

const bcrypt = require('bcrypt');
const config = require('./config/config');
const { connection } = require('./models/db');

// Validate configuration
config.validate();

/**
 * Create a new user
 * @param {string} matricule - User matricule
 * @param {string} motDePasse - User password
 * @param {string} role - User role (admin, eleve, professeur)
 * @param {object} additionalData - Additional user data based on role
 */
async function createUser(matricule, motDePasse, role, additionalData = {}) {
  try {
    // Validate inputs
    if (!matricule || !motDePasse || !role) {
      throw new Error('Matricule, mot de passe et rôle sont requis');
    }

    const validRoles = ['admin', 'eleve', 'professeur'];
    if (!validRoles.includes(role)) {
      throw new Error('Rôle invalide. Doit être admin, eleve ou professeur');
    }

    // Check if user already exists
    const existingUser = await new Promise((resolve, reject) => {
      connection.query(
        'SELECT matricule FROM utilisateur WHERE matricule = ?',
        [matricule],
        (err, results) => {
          if (err) reject(err);
          else resolve(results);
        }
      );
    });

    if (existingUser.length > 0) {
      throw new Error(`L'utilisateur avec le matricule ${matricule} existe déjà`);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(motDePasse, config.security.bcryptRounds);

    // Insert user into database
    await new Promise((resolve, reject) => {
      connection.query(
        'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)',
        [matricule, hashedPassword, role],
        (err, results) => {
          if (err) reject(err);
          else resolve(results);
        }
      );
    });

    // Insert role-specific data if provided
    if (Object.keys(additionalData).length > 0) {
      let roleTable = '';
      let fields = [];
      let values = [matricule];

      switch (role) {
        case 'eleve':
          roleTable = 'eleve';
          fields = ['matricule', 'nom', 'prenom', 'sexe', 'date_naissance', 'nom_classe'];
          values.push(
            additionalData.nom || '',
            additionalData.prenom || '',
            additionalData.sexe || '',
            additionalData.date_naissance || null,
            additionalData.nom_classe || ''
          );
          break;
        case 'professeur':
          roleTable = 'professeur';
          fields = ['matricule', 'nom', 'prenom', 'sexe', 'specialiste', 'email', 'telephone'];
          values.push(
            additionalData.nom || '',
            additionalData.prenom || '',
            additionalData.sexe || '',
            additionalData.specialiste || '',
            additionalData.email || '',
            additionalData.telephone || ''
          );
          break;
        case 'admin':
          roleTable = 'admin';
          fields = ['matricule', 'nom', 'prenom', 'email', 'sexe', 'telephone'];
          values.push(
            additionalData.nom || '',
            additionalData.prenom || '',
            additionalData.email || '',
            additionalData.sexe || '',
            additionalData.telephone || ''
          );
          break;
      }

      if (roleTable) {
        const placeholders = fields.map(() => '?').join(', ');
        const sql = `INSERT INTO ${roleTable} (${fields.join(', ')}) VALUES (${placeholders})`;

        await new Promise((resolve, reject) => {
          connection.query(sql, values, (err, results) => {
            if (err) reject(err);
            else resolve(results);
          });
        });
      }
    }

    console.log(`✅ Utilisateur ${role} créé avec succès !`);
    console.log(`   Matricule: ${matricule}`);
    console.log(`   Rôle: ${role}`);

    return { success: true, matricule, role };

  } catch (error) {
    console.error('❌ Erreur lors de la création de l\'utilisateur:', error.message);
    throw error;
  }
}

// Example usage - you can modify these values
async function main() {
  try {
    // Create a student
    await createUser('ELV001', 'student123', 'eleve', {
      nom: 'Dupont',
      prenom: 'Jean',
      sexe: 'homme',
      nom_classe: '6eme A'
    });

    // Create a teacher
    await createUser('PROF001', 'teacher123', 'professeur', {
      nom: 'Martin',
      prenom: 'Marie',
      sexe: 'femme',
      email: '<EMAIL>',
      specialiste: 'Mathématiques'
    });

    // Create an admin
    await createUser('ADM001', 'admin123', 'admin', {
      nom: 'Admin',
      prenom: 'Super',
      sexe: 'homme',
      email: '<EMAIL>'
    });

  } catch (error) {
    console.error('❌ Erreur dans le script principal:', error.message);
  } finally {
    // Close database connection
    connection.end();
    console.log('📦 Connexion fermée');
  }
}

// Run the script if called directly
if (require.main === module) {
  main();
}

module.exports = { createUser };
