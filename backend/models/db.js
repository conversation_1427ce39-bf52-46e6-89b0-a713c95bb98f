/**
 * Database connection module for NS School Manager
 * Provides a centralized, professional database connection with proper error handling
 */

const mysql = require('mysql2/promise');
const config = require('../config/config');

class DatabaseConnection {
  constructor() {
    this.pool = null;
    this.isConnected = false;
  }

  /**
   * Initialize database connection pool
   */
  async initialize() {
    try {
      this.pool = mysql.createPool({
        host: config.database.host,
        port: config.database.port,
        user: config.database.user,
        password: config.database.password,
        database: config.database.name,
        charset: config.database.charset,
        timezone: config.database.timezone,
        acquireTimeout: config.database.acquireTimeout,
        timeout: config.database.timeout,
        reconnect: config.database.reconnect,
        connectionLimit: 10,
        queueLimit: 0,
        // Enable multiple statements for setup scripts
        multipleStatements: true
      });

      // Test the connection
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();

      this.isConnected = true;
      console.log(`✅ Database connected successfully to ${config.database.name}`);

      return this.pool;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * Get database connection pool
   */
  getPool() {
    if (!this.pool) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.pool;
  }

  /**
   * Execute a query with proper error handling
   */
  async query(sql, params = []) {
    try {
      const [rows] = await this.pool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error('❌ Database query error:', error.message);
      console.error('SQL:', sql);
      console.error('Params:', params);
      throw error;
    }
  }

  /**
   * Execute multiple queries in a transaction
   */
  async transaction(queries) {
    const connection = await this.pool.getConnection();

    try {
      await connection.beginTransaction();

      const results = [];
      for (const { sql, params } of queries) {
        const [rows] = await connection.execute(sql, params || []);
        results.push(rows);
      }

      await connection.commit();
      return results;
    } catch (error) {
      await connection.rollback();
      console.error('❌ Transaction failed:', error.message);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Close database connection
   */
  async close() {
    if (this.pool) {
      await this.pool.end();
      this.isConnected = false;
      console.log('📦 Database connection closed');
    }
  }

  /**
   * Check if database is connected
   */
  isHealthy() {
    return this.isConnected && this.pool;
  }
}

// Create singleton instance
const dbInstance = new DatabaseConnection();

/**
 * Legacy adapter for callback-style code
 * This provides a mysql2 callback-style interface using the promise-based pool
 */
class LegacyAdapter {
  constructor(pool) {
    this.pool = pool;
  }

  // Callback-style query method
  query(sql, params, callback) {
    // Handle different parameter patterns
    if (typeof params === 'function') {
      callback = params;
      params = [];
    }

    dbInstance.query(sql, params)
      .then(results => {
        if (callback) callback(null, results);
        return results;
      })
      .catch(error => {
        if (callback) callback(error);
        console.error('Query error:', error);
      });
  }
}

// Initialize the database on module load
dbInstance.initialize().catch(err => {
  console.error('Failed to initialize database on module load:', err);
});

// Create legacy adapter instance
const legacyAdapter = new LegacyAdapter(dbInstance);

// Export both new and legacy interfaces
module.exports = {
  // Modern promise-based interface
  db: dbInstance,

  // Helper methods
  initialize: () => dbInstance.initialize(),
  query: (sql, params) => dbInstance.query(sql, params),
  transaction: (queries) => dbInstance.transaction(queries),
  close: () => dbInstance.close(),
  getPool: () => dbInstance.getPool(),

  // Legacy callback-based interface for existing code
  connection: legacyAdapter
};
