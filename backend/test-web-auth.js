/**
 * Simple test script to verify web authentication works correctly
 * Run with: node test-web-auth.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

async function testWebAuth() {
  console.log('🌐 Testing Web Authentication...\n');

  try {
    // Test 1: Web login without API flag (should get redirect)
    console.log('1. Testing web student login...');
    const webLoginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      matricule: 'ELV001',
      password: 'eleve123',
      role: 'eleve'
      // No api: true flag - this should be treated as web request
    });

    if (webLoginResponse.data.success && webLoginResponse.data.redirect) {
      console.log('✅ Web login successful');
      console.log('   Redirect URL:', webLoginResponse.data.redirect);
      console.log('   Auth Method:', webLoginResponse.data.authMethod);
      
      if (webLoginResponse.data.redirect !== 'undefined' && webLoginResponse.data.redirect !== '/undefined') {
        console.log('✅ Redirect URL is valid');
      } else {
        console.log('❌ Redirect URL is undefined - BUG FOUND!');
      }
    } else {
      console.log('❌ Web login failed');
      console.log('Response:', webLoginResponse.data);
    }

    // Test 2: Web admin login
    console.log('\n2. Testing web admin login...');
    const webAdminResponse = await axios.post(`${BASE_URL}/api/auth/login_ad`, {
      matricule: 'ADM001',
      password: 'admin123'
      // No api: true flag - this should be treated as web request
    });

    if (webAdminResponse.data.success && webAdminResponse.data.redirect) {
      console.log('✅ Web admin login successful');
      console.log('   Redirect URL:', webAdminResponse.data.redirect);
      console.log('   Auth Method:', webAdminResponse.data.authMethod);
      
      if (webAdminResponse.data.redirect !== 'undefined' && webAdminResponse.data.redirect !== '/undefined') {
        console.log('✅ Admin redirect URL is valid');
      } else {
        console.log('❌ Admin redirect URL is undefined - BUG FOUND!');
      }
    } else {
      console.log('❌ Web admin login failed');
      console.log('Response:', webAdminResponse.data);
    }

    // Test 3: Verify mobile still works
    console.log('\n3. Testing mobile login still works...');
    const mobileResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      matricule: 'ELV001',
      password: 'eleve123',
      role: 'eleve',
      api: true // This should be treated as mobile request
    });

    if (mobileResponse.data.success && mobileResponse.data.tokens) {
      console.log('✅ Mobile login still works');
      console.log('   Auth Method:', mobileResponse.data.authMethod);
      console.log('   Has Tokens:', !!mobileResponse.data.tokens);
    } else {
      console.log('❌ Mobile login broken');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testWebAuth();
