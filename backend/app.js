/**
 * NS School Manager - Main Application
 * Professional Express.js application with proper configuration management
 */

const express = require('express');
const session = require('express-session');
const cors = require('cors');
const path = require('path');
const config = require('./config/config');
const { db } = require('./models/db');
const logger = require('./utils/logger');
const {
  globalErrorHandler,
  notFoundHandler,
  requestLogger,
  gracefulShutdown
} = require('./middlewares/errorHandler');

// Validate configuration before starting
config.validate();

const app = express();

// Trust proxy for production deployments
if (config.app.env === 'production') {
  app.set('trust proxy', 1);
}

// CORS configuration - Allow all origins and methods for development
const corsOptions = {
  origin: true, // Allow all origins
  credentials: true, // Allow cookies for session-based auth
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'], // Allow all common methods
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'], // Allow common headers
  exposedHeaders: ['Authorization'] // Expose JWT token in response headers
};

app.use(cors(corsOptions));

// Request logging middleware
app.use(requestLogger);

// Session configuration
app.use(session(config.session));

// Middlewares for parsing
app.use(express.urlencoded({ extended: true }));
app.use(express.json({ limit: '10mb' }));

// Initialize database connection
async function initializeDatabase() {
  try {
    await db.initialize();
    console.log('🚀 Database initialization completed');
  } catch (error) {
    console.error('❌ Failed to initialize database:', error.message);
    process.exit(1);
  }
}

// Static file serving
app.use('/uploads/cours', express.static(path.join(__dirname, 'uploads/cours')));
app.use('/uploads/devoirs', express.static(path.join(__dirname, 'uploads/devoirs')));
app.use('/uploads/soumissions', express.static(path.join(__dirname, 'uploads/soumissions')));
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.static(path.join(__dirname, '../frontend/public')));

// View engine configuration
app.set('views', path.join(__dirname, '../frontend/views'));
app.set('view engine', 'ejs');

// Import routes
const authRoutes = require('./routes/auth-unified');  // Unified auth routes
const dashboardRoutes = require('./routes/dashboard');

// Authentication routes - unified and organized
app.use('/api/auth', authRoutes);

// Dashboard routes
app.use('/', dashboardRoutes);

// Course routes
app.use('/professeur/cours', require('./routes/prof/cours'));

// Professor routes
const absenceProfRoutes = require('./routes/prof/absence');
app.use('/prof/absence', absenceProfRoutes);

const noteProfRoutes = require('./routes/prof/note');
app.use('/prof/note', noteProfRoutes);

const messageprofRoutes = require('./routes/prof/message');
app.use('/prof/message', messageprofRoutes);

const devoirProfRoutes = require('./routes/prof/devoir');
app.use('/prof/devoir', devoirProfRoutes);

const emploiTempsProfRoutes = require('./routes/prof/emploi_temps');
app.use('/prof/emploi-temps', emploiTempsProfRoutes);

// Student routes
const coursEleveRoute = require('./routes/eleve/cours');
app.use('/eleve/cours', coursEleveRoute);

const eleveNotesRoutes = require('./routes/eleve/notes');
app.use('/eleve', eleveNotesRoutes);

const emploiEleveRoutes = require('./routes/eleve/emploi');
app.use('/eleve', emploiEleveRoutes);

const absenceEleveRoutes = require('./routes/eleve/absence');
app.use('/eleve', absenceEleveRoutes);

const annonceEleveRoutes = require('./routes/eleve/annonce');
app.use('/eleve', annonceEleveRoutes);

const eleveDevoirRoutes = require('./routes/eleve/devoir');
app.use('/eleve/devoirs', eleveDevoirRoutes);

app.use('/eleve/bulletin', require('./routes/eleve/bulletin'));

// Admin routes
const eleveRoutes = require('./routes/eleve');
app.use('/admin/eleve', eleveRoutes);

const apiEleveRoutes = require('./routes/api/eleve');
app.use('/api/eleve', apiEleveRoutes);

const apiProfRoute = require('./routes/api/prof');
const profPageRoute = require('./routes/prof');
app.use('/api/prof', apiProfRoute);
app.use('/admin/prof', profPageRoute);

const apiClassRoute = require('./routes/api/classe');
const classPageRoute = require('./routes/classe');
app.use('/api/classe', apiClassRoute);
app.use('/admin/classe', classPageRoute);

const bulletinRoutes = require('./routes/api/bulletin');
app.use('/admin/bulletin', bulletinRoutes);

const utilisateurRoutes = require('./routes/api/utilisateur');
app.use('/', utilisateurRoutes);

// Profile routes for all user types
const profileRoutes = require('./routes/profile');
app.use('/', profileRoutes);

// Admin registration routes
const adminRegistrationRoutes = require('./routes/admin/registration');
app.use('/admin/registration', adminRegistrationRoutes);

// Download API routes
const downloadRoutes = require('./routes/api/download');
app.use('/api/download', downloadRoutes);

// Additional missing routes from old app
const apiCourRoute = require('./routes/api/cour');
const courPageRoute = require('./routes/cour');
app.use('/api/cour', apiCourRoute);
app.use('/admin/cour', courPageRoute);

const emploiTempsAPI = require('./routes/api/emploi_temps');
const emploiTempsPage = require('./routes/emploi_temps');
app.use('/api/emploi_temps', emploiTempsAPI);
app.use('/admin/emploi_temps', emploiTempsPage);

// Salles API routes
const sallesAPI = require('./routes/api/salles');
app.use('/api/salles', sallesAPI);

// Parametre routes
const parametreAPI = require('./routes/api/parametre');
const parametrePage = require('./routes/parametre');
app.use('/api/parametre', parametreAPI);
app.use('/admin/parametre', parametrePage);

const messageApiRoutes = require('./routes/api/message');
app.use('/api/message', messageApiRoutes);

// Message page route - register this AFTER API routes to avoid conflicts
const messageRoutes = require('./routes/message');
app.use('/admin', messageRoutes);

// Login pages
app.get('/', (req, res) => res.render('login'));
app.get('/ad', (req, res) => res.render('login_ad'));

// Legacy logout routes - handle session destruction directly for GET requests
app.get('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      logger.error('Error destroying session', err);
    }
    res.redirect('/');
  });
});

app.get('/log_out', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      logger.error('Error destroying session', err);
    }
    res.redirect('/ad');
  });
});

// 404 handler - must come before global error handler
app.use(notFoundHandler);

// Global error handler - must be last
app.use(globalErrorHandler);

// Graceful shutdown handlers
let server;

process.on('SIGTERM', () => gracefulShutdown(server, 'SIGTERM'));
process.on('SIGINT', () => gracefulShutdown(server, 'SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception', err);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Rejection', err);
  if (server) {
    server.close(() => {
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
});

// Start server
async function startServer() {
  try {
    // Initialize database first
    await initializeDatabase();
    
    // Start the server
    server = app.listen(config.app.port, () => {
      logger.info(`${config.app.name} server started`, {
        port: config.app.port,
        env: config.app.env,
        database: `${config.database.name}@${config.database.host}:${config.database.port}`
      });
      console.log(`🚀 ${config.app.name} server running on ${config.app.baseUrl}`);
      console.log(`📊 Environment: ${config.app.env}`);
      console.log(`🗄️  Database: ${config.database.name}@${config.database.host}:${config.database.port}`);
    });

    // Handle server errors
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }

      switch (error.code) {
        case 'EACCES':
          logger.error(`Port ${config.app.port} requires elevated privileges`);
          process.exit(1);
        case 'EADDRINUSE':
          logger.error(`Port ${config.app.port} is already in use`);
          process.exit(1);
        default:
          throw error;
      }
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Start the application
startServer();

module.exports = app;
