/**
 * Database Seeding System for NS School Manager
 * Creates sample data for testing and development
 */

const bcrypt = require('bcrypt');
const config = require('../config/config');
const { query } = require('../models/db');
const logger = require('../utils/logger');

class DatabaseSeeder {
  constructor() {
    this.saltRounds = config.security.bcryptRounds;
  }

  /**
   * Hash password for seeding
   */
  async hashPassword(password) {
    return await bcrypt.hash(password, this.saltRounds);
  }

  /**
   * Execute SQL query using centralized database connection
   */
  async query(sql, params = []) {
    return await query(sql, params);
  }

  /**
   * Clear all data from tables
   */
  async clearData() {
    try {
      logger.info('🧹 Clearing existing data...');
      
      // Disable foreign key checks
      await this.query('SET FOREIGN_KEY_CHECKS = 0');
      
      // Clear tables in reverse dependency order
      const tables = [
        'soumission_devoir',
        'devoir',
        'note',
        'absence',
        'emploi_temps',
        'cours',
        'matiere',
        'enseignant_classe',
        'eleve',
        'professeur',
        'admin',
        'salle',
        'classe',
        'utilisateur',
        'activite',
        'message',
        'parametre'
      ];

      for (const table of tables) {
        try {
          await this.query(`DELETE FROM ${table}`);
          logger.info(`✅ Cleared table: ${table}`);
        } catch (error) {
          logger.warn(`⚠️  Could not clear table ${table}: ${error.message}`);
        }
      }

      // Re-enable foreign key checks
      await this.query('SET FOREIGN_KEY_CHECKS = 1');
      
      logger.info('🧹 Data clearing completed');
    } catch (error) {
      logger.error('❌ Error clearing data', error);
      throw error;
    }
  }

  /**
   * Seed users (admin, teachers, students)
   */
  async seedUsers() {
    try {
      logger.info('👥 Seeding users...');

      // Admin users
      const adminPassword = await this.hashPassword('admin123');
      const admins = [
        { matricule: 'ADM001', password: adminPassword, role: 'admin' },
        { matricule: 'ADM002', password: adminPassword, role: 'admin' }
      ];

      // Teacher users
      const teacherPassword = await this.hashPassword('prof123');
      const teachers = [
        { matricule: 'PROF001', password: teacherPassword, role: 'professeur' },
        { matricule: 'PROF002', password: teacherPassword, role: 'professeur' },
        { matricule: 'PROF003', password: teacherPassword, role: 'professeur' },
        { matricule: 'PROF004', password: teacherPassword, role: 'professeur' }
      ];

      // Student users
      const studentPassword = await this.hashPassword('eleve123');
      const students = [
        { matricule: 'ELV001', password: studentPassword, role: 'eleve' },
        { matricule: 'ELV002', password: studentPassword, role: 'eleve' },
        { matricule: 'ELV003', password: studentPassword, role: 'eleve' },
        { matricule: 'ELV004', password: studentPassword, role: 'eleve' },
        { matricule: 'ELV005', password: studentPassword, role: 'eleve' },
        { matricule: 'ELV006', password: studentPassword, role: 'eleve' }
      ];

      // Insert all users
      const allUsers = [...admins, ...teachers, ...students];
      for (const user of allUsers) {
        await this.query(
          'INSERT INTO utilisateur (matricule, password, role) VALUES (?, ?, ?)',
          [user.matricule, user.password, user.role]
        );
      }

      logger.info(`✅ Created ${allUsers.length} users`);
    } catch (error) {
      logger.error('❌ Error seeding users', error);
      throw error;
    }
  }

  /**
   * Seed admin details
   */
  async seedAdmins() {
    try {
      logger.info('👨‍💼 Seeding admin details...');

      const admins = [
        {
          matricule: 'ADM001',
          nom: 'Administrateur',
          prenom: 'Principal',
          email: '<EMAIL>',
          sexe: 'homme',
          telephone: '+237 655 000 001'
        },
        {
          matricule: 'ADM002',
          nom: 'Adjoint',
          prenom: 'Administrateur',
          email: '<EMAIL>',
          sexe: 'femme',
          telephone: '+237 655 000 002'
        }
      ];

      for (const admin of admins) {
        await this.query(
          'INSERT INTO admin (matricule, nom, prenom, email, sexe, telephone) VALUES (?, ?, ?, ?, ?, ?)',
          [admin.matricule, admin.nom, admin.prenom, admin.email, admin.sexe, admin.telephone]
        );
      }

      logger.info(`✅ Created ${admins.length} admin profiles`);
    } catch (error) {
      logger.error('❌ Error seeding admins', error);
      throw error;
    }
  }

  /**
   * Seed classes
   */
  async seedClasses() {
    try {
      logger.info('🏫 Seeding classes...');

      const classes = [
        { nom_classe: '6eme A', niveau: '6eme', annee_scolaire: '2024-2025' },
        { nom_classe: '6eme B', niveau: '6eme', annee_scolaire: '2024-2025' },
        { nom_classe: '5eme A', niveau: '5eme', annee_scolaire: '2024-2025' },
        { nom_classe: '4eme A', niveau: '4eme', annee_scolaire: '2024-2025' },
        { nom_classe: '3eme A', niveau: '3eme', annee_scolaire: '2024-2025' }
      ];

      for (const classe of classes) {
        await this.query(
          'INSERT INTO classe (nom_classe, niveau, annee_scolaire) VALUES (?, ?, ?)',
          [classe.nom_classe, classe.niveau, classe.annee_scolaire]
        );
      }

      logger.info(`✅ Created ${classes.length} classes`);
    } catch (error) {
      logger.error('❌ Error seeding classes', error);
      throw error;
    }
  }

  /**
   * Seed teachers
   */
  async seedTeachers() {
    try {
      logger.info('👨‍🏫 Seeding teachers...');

      const teachers = [
        {
          matricule: 'PROF001',
          nom: 'Dupont',
          prenom: 'Jean',
          sexe: 'homme',
          specialiste: 'Mathématiques',
          email: '<EMAIL>',
          telephone: '+237 655 001 001'
        },
        {
          matricule: 'PROF002',
          nom: 'Martin',
          prenom: 'Marie',
          sexe: 'femme',
          specialiste: 'Français',
          email: '<EMAIL>',
          telephone: '+237 655 001 002'
        },
        {
          matricule: 'PROF003',
          nom: 'Kouam',
          prenom: 'Paul',
          sexe: 'homme',
          specialiste: 'Sciences Physiques',
          email: '<EMAIL>',
          telephone: '+237 655 001 003'
        },
        {
          matricule: 'PROF004',
          nom: 'Ngono',
          prenom: 'Sylvie',
          sexe: 'femme',
          specialiste: 'Histoire-Géographie',
          email: '<EMAIL>',
          telephone: '+237 655 001 004'
        }
      ];

      for (const teacher of teachers) {
        await this.query(
          'INSERT INTO professeur (matricule, nom, prenom, sexe, specialiste, email, telephone) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [teacher.matricule, teacher.nom, teacher.prenom, teacher.sexe, teacher.specialiste, teacher.email, teacher.telephone]
        );
      }

      logger.info(`✅ Created ${teachers.length} teacher profiles`);

    // Seed enseignant_classe assignments
    logger.info('👨‍🏫 Seeding enseignant_classe assignments...');
    const enseignantClasse = [
      { professeur_matricule: 'PROF001', nom_classe: '6eme A' },
      { professeur_matricule: 'PROF001', nom_classe: '6eme B' },
      { professeur_matricule: 'PROF002', nom_classe: '6eme A' },
      { professeur_matricule: 'PROF002', nom_classe: '5eme A' },
      { professeur_matricule: 'PROF003', nom_classe: '4eme A' },
      { professeur_matricule: 'PROF003', nom_classe: '3eme A' },
      { professeur_matricule: 'PROF004', nom_classe: '6eme A' },
      { professeur_matricule: 'PROF004', nom_classe: '5eme A' }
    ];
    for (const ec of enseignantClasse) {
      await this.query(
        'INSERT INTO enseignant_classe (professeur_matricule, nom_classe) VALUES (?, ?)',
        [ec.professeur_matricule, ec.nom_classe]
      );
    }
    logger.info(`✅ Created ${enseignantClasse.length} enseignant-classe assignments`);
    } catch (error) {
      logger.error('❌ Error seeding teachers', error);
      throw error;
    }
  }

  /**
   * Seed students
   */
  async seedStudents() {
    try {
      logger.info('👨‍🎓 Seeding students...');

      const students = [
        {
          matricule: 'ELV001',
          nom: 'Mballa',
          prenom: 'Pierre',
          date_naissance: '2010-03-15',
          sexe: 'homme',
          email: '<EMAIL>',
          telephone: '+237 655 002 001',
          nom_classe: '6eme A'
        },
        {
          matricule: 'ELV002',
          nom: 'Fouda',
          prenom: 'Aminata',
          date_naissance: '2010-07-22',
          sexe: 'femme',
          email: '<EMAIL>',
          telephone: '+237 655 002 002',
          nom_classe: '6eme A'
        },
        {
          matricule: 'ELV003',
          nom: 'Biya',
          prenom: 'Claude',
          date_naissance: '2009-11-08',
          sexe: 'homme',
          email: '<EMAIL>',
          telephone: '+237 655 002 003',
          nom_classe: '5eme A'
        },
        {
          matricule: 'ELV004',
          nom: 'Essomba',
          prenom: 'Grace',
          date_naissance: '2008-05-14',
          sexe: 'femme',
          email: '<EMAIL>',
          telephone: '+237 655 002 004',
          nom_classe: '4eme A'
        },
        {
          matricule: 'ELV005',
          nom: 'Atangana',
          prenom: 'David',
          date_naissance: '2007-09-30',
          sexe: 'homme',
          email: '<EMAIL>',
          telephone: '+237 655 002 005',
          nom_classe: '3eme A'
        },
        {
          matricule: 'ELV006',
          nom: 'Nkomo',
          prenom: 'Fatima',
          date_naissance: '2010-12-03',
          sexe: 'femme',
          email: '<EMAIL>',
          telephone: '+237 655 002 006',
          nom_classe: '6eme B'
        }
      ];

      for (const student of students) {
        await this.query(
          'INSERT INTO eleve (matricule, nom, prenom, date_naissance, sexe, email, telephone, nom_classe) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
          [student.matricule, student.nom, student.prenom, student.date_naissance, student.sexe, student.email, student.telephone, student.nom_classe]
        );
      }

      logger.info(`✅ Created ${students.length} student profiles`);
    } catch (error) {
      logger.error('❌ Error seeding students', error);
      throw error;
    }
  }

  /**
   * Seed subjects (matiere)
   */
  async seedSubjects() {
    try {
      logger.info('📚 Seeding subjects...');

      const subjects = [
        { nom: 'Mathématiques', professeur_matricule: 'PROF001', nom_classe: '6eme A' },
        { nom: 'Mathématiques', professeur_matricule: 'PROF001', nom_classe: '6eme B' },
        { nom: 'Français', professeur_matricule: 'PROF002', nom_classe: '6eme A' },
        { nom: 'Français', professeur_matricule: 'PROF002', nom_classe: '5eme A' },
        { nom: 'Sciences Physiques', professeur_matricule: 'PROF003', nom_classe: '4eme A' },
        { nom: 'Sciences Physiques', professeur_matricule: 'PROF003', nom_classe: '3eme A' },
        { nom: 'Histoire-Géographie', professeur_matricule: 'PROF004', nom_classe: '6eme A' },
        { nom: 'Histoire-Géographie', professeur_matricule: 'PROF004', nom_classe: '5eme A' }
      ];

      for (const subject of subjects) {
        await this.query(
          'INSERT INTO matiere (nom, professeur_matricule, nom_classe) VALUES (?, ?, ?)',
          [subject.nom, subject.professeur_matricule, subject.nom_classe]
        );
      }

      logger.info(`✅ Created ${subjects.length} subject assignments`);
    } catch (error) {
      logger.error('❌ Error seeding subjects', error);
      throw error;
    }
  }

  /**
   * Seed sample courses
   */
  async seedCourses() {
    try {
      logger.info('📖 Seeding sample courses...');

      // First, get the actual matiere_id values that were created
      const matieres = await this.query('SELECT matiere_id, nom, nom_classe FROM matiere ORDER BY matiere_id');

      if (matieres.length === 0) {
        logger.warn('⚠️ No subjects found, skipping course creation');
        return;
      }

      // Find specific matiere_ids for our courses
      const mathMatiereId = matieres.find(m => m.nom === 'Mathématiques' && m.nom_classe === '6eme A')?.matiere_id;
      const francaisMatiereId = matieres.find(m => m.nom === 'Français' && m.nom_classe === '6eme A')?.matiere_id;

      const courses = [];

      if (mathMatiereId) {
        courses.push(
          {
            titre: 'Introduction aux nombres entiers',
            fichier: 'math-6eme-nombres-entiers.pdf',
            professeur_matricule: 'PROF001',
            matiere_id: mathMatiereId,
            nom_classe: '6eme A'
          },
          {
            titre: 'Les fractions simples',
            fichier: 'math-6eme-fractions.pdf',
            professeur_matricule: 'PROF001',
            matiere_id: mathMatiereId,
            nom_classe: '6eme A'
          }
        );
      }

      if (francaisMatiereId) {
        courses.push(
          {
            titre: 'Grammaire: Le groupe nominal',
            fichier: 'francais-6eme-groupe-nominal.pdf',
            professeur_matricule: 'PROF002',
            matiere_id: francaisMatiereId,
            nom_classe: '6eme A'
          },
          {
            titre: 'La conjugaison au présent',
            fichier: 'francais-6eme-present.pdf',
            professeur_matricule: 'PROF002',
            matiere_id: francaisMatiereId,
            nom_classe: '6eme A'
          }
        );
      }

      for (const course of courses) {
        await this.query(
          'INSERT INTO cours (titre, fichier, professeur_matricule, matiere_id, nom_classe, date_ajout) VALUES (?, ?, ?, ?, ?, NOW())',
          [course.titre, course.fichier, course.professeur_matricule, course.matiere_id, course.nom_classe]
        );
      }

      logger.info(`✅ Created ${courses.length} sample courses`);
    } catch (error) {
      logger.error('❌ Error seeding courses', error);
      throw error;
    }
  }

  /**
   * Seed rooms (salles)
   */
  async seedSalles() {
    try {
      logger.info('🏢 Seeding rooms (salles)...');

      const salles = [
        { nom_salle: 'Salle A1', type_salle: 'normale', capacite: 35, etage: 'RDC', batiment: 'Bâtiment A' },
        { nom_salle: 'Salle A2', type_salle: 'normale', capacite: 35, etage: 'RDC', batiment: 'Bâtiment A' },
        { nom_salle: 'Salle A3', type_salle: 'normale', capacite: 35, etage: '1er', batiment: 'Bâtiment A' },
        { nom_salle: 'Salle B1', type_salle: 'normale', capacite: 30, etage: 'RDC', batiment: 'Bâtiment B' },
        { nom_salle: 'Salle B2', type_salle: 'normale', capacite: 30, etage: 'RDC', batiment: 'Bâtiment B' },
        { nom_salle: 'Salle B3', type_salle: 'normale', capacite: 30, etage: '1er', batiment: 'Bâtiment B' },
        { nom_salle: 'Laboratoire Sciences', type_salle: 'laboratoire', capacite: 25, etage: '1er', batiment: 'Bâtiment A', equipements: 'Microscopes, Éprouvettes, Brûleurs Bunsen' },
        { nom_salle: 'Laboratoire Physique', type_salle: 'laboratoire', capacite: 25, etage: '1er', batiment: 'Bâtiment A', equipements: 'Oscilloscopes, Générateurs, Multimètres' },
        { nom_salle: 'Salle Informatique', type_salle: 'informatique', capacite: 20, etage: '2ème', batiment: 'Bâtiment B', equipements: '20 Ordinateurs, Projecteur, Tableau interactif' },
        { nom_salle: 'Gymnase', type_salle: 'sport', capacite: 50, etage: 'RDC', batiment: 'Bâtiment Sport', equipements: 'Terrains multisports, Vestiaires' },
        { nom_salle: 'Bibliothèque', type_salle: 'bibliotheque', capacite: 40, etage: '1er', batiment: 'Bâtiment C', equipements: 'Livres, Ordinateurs, Espaces de lecture' },
        { nom_salle: 'Salle de Musique', type_salle: 'autre', capacite: 25, etage: '2ème', batiment: 'Bâtiment C', equipements: 'Piano, Instruments, Système audio' },
        { nom_salle: 'Salle d\'Art', type_salle: 'autre', capacite: 20, etage: '2ème', batiment: 'Bâtiment C', equipements: 'Chevalets, Matériel de dessin, Évier' }
      ];

      for (const salle of salles) {
        await this.query(`
          INSERT INTO salle (nom_salle, type_salle, capacite, etage, batiment, equipements)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [salle.nom_salle, salle.type_salle, salle.capacite, salle.etage, salle.batiment, salle.equipements || null]);
      }

      logger.info(`✅ ${salles.length} rooms seeded successfully`);

    } catch (error) {
      logger.error('❌ Error seeding rooms:', error);
      throw error;
    }
  }

  /**
   * Seed comprehensive timetable data
   */
  async seedTimetables() {
    try {
      logger.info('📅 Seeding comprehensive timetable data...');

      // Get all matiere_id values that were created
      const matieres = await this.query('SELECT matiere_id, nom, nom_classe, professeur_matricule FROM matiere ORDER BY matiere_id');

      if (matieres.length === 0) {
        logger.warn('⚠️ No subjects found, skipping timetable creation');
        return;
      }

      // Get all salle_id values that were created
      const salles = await this.query('SELECT salle_id, nom_salle FROM salle ORDER BY salle_id');
      const salleMap = {};
      salles.forEach(salle => {
        salleMap[salle.nom_salle] = salle.salle_id;
      });

      // Define realistic timetable slots for each class
      const timetableSlots = [
        // 6eme A - Classe complète avec tous les créneaux
        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Lundi', debut: '08:00', fin: '09:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Français', jour: 'Lundi', debut: '09:00', fin: '10:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Histoire-Géographie', jour: 'Lundi', debut: '10:30', fin: '11:30', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Lundi', debut: '14:00', fin: '15:00', salle: 'Salle A1' },

        { classe: '6eme A', matiere: 'Français', jour: 'Mardi', debut: '08:00', fin: '09:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Mardi', debut: '09:00', fin: '10:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Histoire-Géographie', jour: 'Mardi', debut: '10:30', fin: '11:30', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Français', jour: 'Mardi', debut: '14:00', fin: '15:00', salle: 'Salle A1' },

        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Mercredi', debut: '08:00', fin: '09:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Français', jour: 'Mercredi', debut: '09:00', fin: '10:00', salle: 'Salle A1' },

        { classe: '6eme A', matiere: 'Histoire-Géographie', jour: 'Jeudi', debut: '08:00', fin: '09:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Jeudi', debut: '09:00', fin: '10:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Français', jour: 'Jeudi', debut: '10:30', fin: '11:30', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Jeudi', debut: '14:00', fin: '15:00', salle: 'Salle A1' },

        { classe: '6eme A', matiere: 'Français', jour: 'Vendredi', debut: '08:00', fin: '09:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Histoire-Géographie', jour: 'Vendredi', debut: '09:00', fin: '10:00', salle: 'Salle A1' },
        { classe: '6eme A', matiere: 'Mathématiques', jour: 'Vendredi', debut: '10:30', fin: '11:30', salle: 'Salle A1' },

        // 6eme B - Planning différent pour éviter les conflits de professeurs
        { classe: '6eme B', matiere: 'Mathématiques', jour: 'Lundi', debut: '10:30', fin: '11:30', salle: 'Salle B1' },
        { classe: '6eme B', matiere: 'Mathématiques', jour: 'Mardi', debut: '14:00', fin: '15:00', salle: 'Salle B1' },
        { classe: '6eme B', matiere: 'Mathématiques', jour: 'Mercredi', debut: '10:30', fin: '11:30', salle: 'Salle B1' },
        { classe: '6eme B', matiere: 'Mathématiques', jour: 'Jeudi', debut: '14:00', fin: '15:00', salle: 'Salle B1' },
        { classe: '6eme B', matiere: 'Mathématiques', jour: 'Vendredi', debut: '14:00', fin: '15:00', salle: 'Salle B1' },

        // 5eme A
        { classe: '5eme A', matiere: 'Français', jour: 'Lundi', debut: '10:30', fin: '11:30', salle: 'Salle C1' },
        { classe: '5eme A', matiere: 'Histoire-Géographie', jour: 'Lundi', debut: '14:00', fin: '15:00', salle: 'Salle C1' },
        { classe: '5eme A', matiere: 'Français', jour: 'Mardi', debut: '10:30', fin: '11:30', salle: 'Salle C1' },
        { classe: '5eme A', matiere: 'Histoire-Géographie', jour: 'Mercredi', debut: '14:00', fin: '15:00', salle: 'Salle C1' },
        { classe: '5eme A', matiere: 'Français', jour: 'Jeudi', debut: '08:00', fin: '09:00', salle: 'Salle C1' },
        { classe: '5eme A', matiere: 'Histoire-Géographie', jour: 'Vendredi', debut: '14:00', fin: '15:00', salle: 'Salle C1' },

        // 4eme A
        { classe: '4eme A', matiere: 'Sciences Physiques', jour: 'Lundi', debut: '08:00', fin: '09:00', salle: 'Laboratoire' },
        { classe: '4eme A', matiere: 'Sciences Physiques', jour: 'Mardi', debut: '08:00', fin: '09:00', salle: 'Laboratoire' },
        { classe: '4eme A', matiere: 'Sciences Physiques', jour: 'Mercredi', debut: '08:00', fin: '09:00', salle: 'Laboratoire' },
        { classe: '4eme A', matiere: 'Sciences Physiques', jour: 'Jeudi', debut: '10:30', fin: '11:30', salle: 'Laboratoire' },

        // 3eme A
        { classe: '3eme A', matiere: 'Sciences Physiques', jour: 'Lundi', debut: '09:00', fin: '10:00', salle: 'Laboratoire' },
        { classe: '3eme A', matiere: 'Sciences Physiques', jour: 'Mardi', debut: '09:00', fin: '10:00', salle: 'Laboratoire' },
        { classe: '3eme A', matiere: 'Sciences Physiques', jour: 'Mercredi', debut: '09:00', fin: '10:00', salle: 'Laboratoire' },
        { classe: '3eme A', matiere: 'Sciences Physiques', jour: 'Vendredi', debut: '08:00', fin: '09:00', salle: 'Laboratoire' }
      ];

      let createdSlots = 0;

      for (const slot of timetableSlots) {
        // Find the corresponding matiere_id
        const matiere = matieres.find(m =>
          m.nom === slot.matiere && m.nom_classe === slot.classe
        );

        if (matiere) {
          try {
            const salleId = salleMap[slot.salle];
            await this.query(
              'INSERT INTO emploi_temps (nom_classe, matiere_id, jour_semaine, heure_debut, heure_fin, salle, salle_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
              [slot.classe, matiere.matiere_id, slot.jour, slot.debut, slot.fin, slot.salle, salleId || null]
            );
            createdSlots++;
          } catch (error) {
            logger.warn(`⚠️ Could not create timetable slot for ${slot.classe} - ${slot.matiere} on ${slot.jour}: ${error.message}`);
          }
        } else {
          logger.warn(`⚠️ Subject not found: ${slot.matiere} for class ${slot.classe}`);
        }
      }

      logger.info(`✅ Created ${createdSlots} timetable slots`);
    } catch (error) {
      logger.error('❌ Error seeding timetables', error);
      throw error;
    }
  }

  /**
   * Seed system parameters
   */
  async seedParameters() {
    try {
      logger.info('⚙️ Seeding system parameters...');

      await this.query(`
        INSERT INTO parametre (
          id, annee_scolaire, nb_trimestres, theme, messagerie_active,
          nom_ecole, email_ecole, telephone_ecole, adresse_ecole, logo_ecole
        ) VALUES (
          1, '2024-2025', 3, 'clair', 1,
          'NS School Manager', '<EMAIL>', '+237 655 000 000',
          'Douala, Cameroun', ''
        ) ON DUPLICATE KEY UPDATE
          annee_scolaire = VALUES(annee_scolaire),
          nb_trimestres = VALUES(nb_trimestres),
          theme = VALUES(theme),
          messagerie_active = VALUES(messagerie_active),
          nom_ecole = VALUES(nom_ecole),
          email_ecole = VALUES(email_ecole),
          telephone_ecole = VALUES(telephone_ecole),
          adresse_ecole = VALUES(adresse_ecole)
      `);

      logger.info('✅ System parameters configured');
    } catch (error) {
      logger.error('❌ Error seeding parameters', error);
      throw error;
    }
  }

  /**
   * Run all seeds
   */
  async seedAll(clearFirst = true) {
    try {
      logger.info('🌱 Starting database seeding...');

      if (clearFirst) {
        await this.clearData();
      }

      await this.seedUsers();
      await this.seedAdmins();
      await this.seedClasses();
      await this.seedTeachers();
      await this.seedStudents();
      await this.seedSubjects();
      await this.seedSalles(); // Add room seeding before timetables
      await this.seedTimetables(); // Add timetable seeding
      await this.seedCourses();
      await this.seedParameters();

      logger.info('🎉 Database seeding completed successfully!');

      // Display login credentials
      this.displayCredentials();

    } catch (error) {
      logger.error('❌ Database seeding failed', error);
      throw error;
    }
  }

  /**
   * Display login credentials for testing
   */
  displayCredentials() {
    console.log('\n' + '='.repeat(60));
    console.log('🔑 LOGIN CREDENTIALS FOR TESTING');
    console.log('='.repeat(60));
    
    console.log('\n👨‍💼 ADMIN ACCOUNTS:');
    console.log('  Matricule: ADM001 | Password: admin123');
    console.log('  Matricule: ADM002 | Password: admin123');
    
    console.log('\n👨‍🏫 TEACHER ACCOUNTS:');
    console.log('  Matricule: PROF001 | Password: prof123 | Subject: Mathématiques');
    console.log('  Matricule: PROF002 | Password: prof123 | Subject: Français');
    console.log('  Matricule: PROF003 | Password: prof123 | Subject: Sciences Physiques');
    console.log('  Matricule: PROF004 | Password: prof123 | Subject: Histoire-Géographie');
    
    console.log('\n👨‍🎓 STUDENT ACCOUNTS:');
    console.log('  Matricule: ELV001 | Password: eleve123 | Class: 6eme A');
    console.log('  Matricule: ELV002 | Password: eleve123 | Class: 6eme A');
    console.log('  Matricule: ELV003 | Password: eleve123 | Class: 5eme A');
    console.log('  Matricule: ELV004 | Password: eleve123 | Class: 4eme A');
    console.log('  Matricule: ELV005 | Password: eleve123 | Class: 3eme A');
    console.log('  Matricule: ELV006 | Password: eleve123 | Class: 6eme B');
    
    console.log('\n🌐 ACCESS URLS:');
    console.log('  Admin Login: http://localhost:8080/ad');
    console.log('  Student/Teacher Login: http://localhost:8080/');
    
    console.log('\n' + '='.repeat(60));
  }
}

// Export the seeder class
module.exports = DatabaseSeeder;

// Run seeding if called directly
if (require.main === module) {
  const seeder = new DatabaseSeeder();
  
  seeder.seedAll()
    .then(() => {
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error.message);
      process.exit(1);
    });
}
