#!/usr/bin/env node

/**
 * Database Seeding Runner for NS School Manager
 * Simple script to run database seeds
 */

const DatabaseSeeder = require('./seeds/seedData');
const logger = require('./utils/logger');

async function runSeeds() {
  try {
    console.log('🌱 NS School Manager - Database Seeding');
    console.log('=====================================\n');

    const seeder = new DatabaseSeeder();
    
    // Check command line arguments
    const args = process.argv.slice(2);
    const clearFirst = !args.includes('--no-clear');
    
    if (clearFirst) {
      console.log('⚠️  This will clear all existing data and create fresh seed data.');
      console.log('   Use --no-clear flag to keep existing data.\n');
    }

    // Always clear the data for this script, then seed.
    await seeder.seedAll(true);
    
    console.log('\n✅ Database seeding completed successfully!');
    console.log('🚀 You can now start the application with: npm run dev');
    
  } catch (error) {
    console.error('\n❌ Database seeding failed:', error.message);
    logger.error('Database seeding failed', error);
    process.exit(1);
  } finally {
    // Close database connection
    process.exit(0);
  }
}

// Run the seeding
runSeeds();
