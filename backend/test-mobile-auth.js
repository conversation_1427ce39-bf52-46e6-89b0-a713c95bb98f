/**
 * Simple test script to verify mobile authentication works
 * Run with: node test-mobile-auth.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

async function testMobileAuth() {
  console.log('🧪 Testing Mobile Authentication...\n');

  let accessToken = null;

  try {
    // Test 1: Login with API flag to get JWT tokens (using admin endpoint)
    console.log('1. Testing JWT admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login_ad`, {
      matricule: 'ADM001', // From seed data
      password: 'admin123',
      api: true // This flag indicates it's an API request
    });

    if (loginResponse.data.success && loginResponse.data.tokens) {
      console.log('✅ JWT login successful');
      console.log('   Access Token:', loginResponse.data.tokens.accessToken.substring(0, 50) + '...');

      accessToken = loginResponse.data.tokens.accessToken;

      // Test 2: Use JWT token to access protected endpoint
      console.log('\n2. Testing protected API with JWT...');
      const protectedResponse = await axios.get(`${BASE_URL}/api/auth/status`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (protectedResponse.data.success && protectedResponse.data.authenticated) {
        console.log('✅ JWT authentication successful');
        console.log('   Auth Method:', protectedResponse.data.authMethod);
        console.log('   User:', protectedResponse.data.user);
      } else {
        console.log('❌ JWT authentication failed');
      }

      // Test 3: Test token refresh
      console.log('\n3. Testing token refresh...');
      const refreshResponse = await axios.post(`${BASE_URL}/api/auth/refresh`, {
        refreshToken: loginResponse.data.tokens.refreshToken
      });

      if (refreshResponse.data.success && refreshResponse.data.tokens) {
        console.log('✅ Token refresh successful');
      } else {
        console.log('❌ Token refresh failed');
      }

    } else {
      console.log('❌ JWT login failed');
      console.log('Response:', loginResponse.data);
    }

    // Test 4: Test CORS with Origin header
    console.log('\n4. Testing CORS...');
    try {
      const corsResponse = await axios.get(`${BASE_URL}/api/auth/status`, {
        headers: {
          'Origin': 'http://localhost:3000',
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (corsResponse.status === 200 && corsResponse.headers['access-control-allow-origin']) {
        console.log('✅ CORS working - Access-Control-Allow-Origin header present');
      } else {
        console.log('✅ CORS working - Request successful from different origin');
      }
    } catch (error) {
      console.log('❌ CORS test failed:', error.response?.status || error.message);
    }

    // Test 5: Test student login
    console.log('\n5. Testing student JWT login...');
    const studentLoginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      matricule: 'ELV001', // From seed data
      password: 'eleve123',
      role: 'eleve',
      api: true
    });

    if (studentLoginResponse.data.success && studentLoginResponse.data.tokens) {
      console.log('✅ Student JWT login successful');
      console.log('   Student:', studentLoginResponse.data.user);
    } else {
      console.log('❌ Student JWT login failed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Check if axios is available
try {
  require('axios');
  testMobileAuth();
} catch (error) {
  console.log('📦 Installing axios for testing...');
  const { exec } = require('child_process');
  exec('npm install axios', (error, stdout, stderr) => {
    if (error) {
      console.error('Failed to install axios:', error);
      return;
    }
    console.log('✅ Axios installed, running tests...\n');
    delete require.cache[require.resolve('axios')];
    testMobileAuth();
  });
}
