# API Documentation - Emploi du Temps

## Vue d'ensemble

L'API Emploi du Temps fournit des endpoints pour gérer les emplois du temps des classes dans NS School Manager. Elle inclut des fonctionnalités CRUD complètes, la détection de conflits, et des fonctionnalités avancées d'import/export.

**Base URL:** `/api/emploi_temps`

## Authentification

Toutes les routes nécessitent une authentification appropriée selon le rôle de l'utilisateur.

## Endpoints Principaux

### 1. Récupérer les Classes

```http
GET /api/emploi_temps/classes
```

**Description:** Récupère la liste de toutes les classes disponibles.

**Réponse:**
```json
{
  "success": true,
  "data": [
    {
      "nom_classe": "6eme A",
      "niveau": "6eme",
      "annee_scolaire": "2024-2025"
    }
  ],
  "count": 1,
  "message": "Classes récupérées avec succès"
}
```

### 2. <PERSON><PERSON><PERSON><PERSON><PERSON> les Matières

```http
GET /api/emploi_temps/matieres?nom_classe=6eme A
```

**Paramètres de requête:**
- `nom_classe` (optionnel): Filtre par classe

**Réponse:**
```json
{
  "success": true,
  "data": [
    {
      "matiere_id": 1,
      "nom": "Mathématiques",
      "nom_classe": "6eme A",
      "professeur": "Jean Dupont",
      "matricule_professeur": "PROF001"
    }
  ],
  "count": 1,
  "message": "Matières récupérées avec succès"
}
```

### 3. Récupérer l'Emploi du Temps d'une Classe

```http
GET /api/emploi_temps/planning/:nom_classe
```

**Paramètres d'URL:**
- `nom_classe`: Nom de la classe

**Réponse:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "jour_semaine": "Lundi",
      "heure_debut": "08:00",
      "heure_fin": "09:00",
      "matiere": "Mathématiques",
      "matiere_id": 1,
      "salle": "Salle A1",
      "professeur": "Jean Dupont",
      "matricule_professeur": "PROF001"
    }
  ],
  "message": "Emploi du temps récupéré avec succès pour la classe 6eme A"
}
```

### 4. Ajouter un Créneau

```http
POST /api/emploi_temps
```

**Corps de la requête:**
```json
{
  "nom_classe": "6eme A",
  "matiere_id": 1,
  "jour_semaine": "Lundi",
  "heure_debut": "08:00",
  "heure_fin": "09:00",
  "salle": "Salle A1"
}
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "success": true,
    "message": "Créneau ajouté avec succès"
  },
  "message": "Créneau ajouté avec succès à l'emploi du temps"
}
```

### 5. Modifier un Créneau

```http
PUT /api/emploi_temps/:id
```

**Paramètres d'URL:**
- `id`: ID du créneau à modifier

**Corps de la requête:** (même format que pour l'ajout)

### 6. Supprimer un Créneau

```http
DELETE /api/emploi_temps/:id
```

**Paramètres d'URL:**
- `id`: ID du créneau à supprimer

### 7. Vérifier les Conflits

```http
POST /api/emploi_temps/verifier-conflits?id_creneau=1
```

**Paramètres de requête:**
- `id_creneau` (optionnel): ID du créneau à exclure de la vérification

**Corps de la requête:** (même format que pour l'ajout)

**Réponse:**
```json
{
  "success": true,
  "data": {
    "aConflits": false,
    "conflits": []
  },
  "message": "Aucun conflit détecté"
}
```

## Fonctionnalités Avancées

### 8. Export CSV

```http
GET /api/emploi_temps/export/csv/:nom_classe
```

**Description:** Exporte l'emploi du temps au format CSV.

**Réponse:** Fichier CSV en téléchargement

### 9. Export JSON

```http
GET /api/emploi_temps/export/json/:nom_classe
```

**Description:** Exporte l'emploi du temps au format JSON.

**Réponse:** Fichier JSON en téléchargement

### 10. Import JSON

```http
POST /api/emploi_temps/import/:nom_classe
```

**Description:** Importe un emploi du temps depuis des données JSON.

**Corps de la requête:**
```json
{
  "classe": "6eme A",
  "dateExport": "2024-01-15T10:00:00.000Z",
  "nombreCreneaux": 2,
  "creneaux": [
    {
      "jour_semaine": "Lundi",
      "heure_debut": "08:00",
      "heure_fin": "09:00",
      "matiere": "Mathématiques",
      "salle": "Salle A1"
    }
  ]
}
```

### 11. Vérifier Disponibilité Professeur

```http
POST /api/emploi_temps/verifier-disponibilite-professeur
```

**Corps de la requête:**
```json
{
  "matricule_professeur": "PROF001",
  "jour_semaine": "Lundi",
  "heure_debut": "08:00",
  "heure_fin": "09:00",
  "exclude_id": 1
}
```

### 12. Vérifier Disponibilité Salle

```http
POST /api/emploi_temps/verifier-disponibilite-salle
```

**Corps de la requête:**
```json
{
  "salle": "Salle A1",
  "jour_semaine": "Lundi",
  "heure_debut": "08:00",
  "heure_fin": "09:00",
  "exclude_id": 1
}
```

### 13. Suggestions d'Optimisation

```http
GET /api/emploi_temps/suggestions/:nom_classe
```

**Réponse:**
```json
{
  "success": true,
  "data": [
    {
      "type": "trou_horaire",
      "jour": "Lundi",
      "message": "Trou de 60 minutes entre 10:00 et 11:00",
      "priorite": "moyenne"
    }
  ],
  "count": 1,
  "message": "1 suggestion(s) générée(s) pour la classe 6eme A"
}
```

### 14. Copier Emploi du Temps

```http
POST /api/emploi_temps/copier
```

**Corps de la requête:**
```json
{
  "classe_source": "6eme A",
  "classe_destination": "6eme B"
}
```

### 15. Statistiques

```http
GET /api/emploi_temps/statistiques
```

**Réponse:**
```json
{
  "success": true,
  "data": {
    "totalCreneaux": 50,
    "totalClasses": 5,
    "totalMatieres": 8,
    "totalProfesseurs": 4,
    "repartitionParJour": [
      {
        "jour_semaine": "Lundi",
        "nombre": 12
      }
    ]
  },
  "message": "Statistiques récupérées avec succès"
}
```

## Codes d'Erreur

- `400 Bad Request`: Données manquantes ou invalides
- `404 Not Found`: Ressource non trouvée
- `409 Conflict`: Conflit détecté (chevauchement d'horaires)
- `500 Internal Server Error`: Erreur serveur

## Format des Réponses

Toutes les réponses suivent le format standardisé:

```json
{
  "success": boolean,
  "data": object|array,
  "message": string,
  "timestamp": string,
  "path": string
}
```

## Validation des Données

### Créneaux Horaires

- `nom_classe`: Requis, doit exister dans la table `classe`
- `matiere_id`: Requis, doit exister dans la table `matiere`
- `jour_semaine`: Requis, valeurs acceptées: Lundi, Mardi, Mercredi, Jeudi, Vendredi, Samedi
- `heure_debut`: Requis, format HH:MM (24h)
- `heure_fin`: Requis, format HH:MM (24h), doit être après `heure_debut`
- `salle`: Requis, chaîne non vide

### Détection de Conflits

Le système détecte automatiquement:
- Conflits de classe (même classe, même horaire)
- Conflits de professeur (même professeur, même horaire)
- Conflits de salle (même salle, même horaire)

## Exemples d'Utilisation

### JavaScript/Frontend

```javascript
// Récupérer l'emploi du temps d'une classe
const response = await fetch('/api/emploi_temps/planning/6eme A');
const data = await response.json();

// Ajouter un créneau
const nouveauCreneau = {
  nom_classe: '6eme A',
  matiere_id: 1,
  jour_semaine: 'Lundi',
  heure_debut: '08:00',
  heure_fin: '09:00',
  salle: 'Salle A1'
};

const response = await fetch('/api/emploi_temps', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(nouveauCreneau)
});
```
