import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  AccessibilityInfo,
  useColorScheme,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function AdminLoginScreen() {
  const router = useRouter();
  const { signInAdmin, loading } = useAuth();
  const [matricule, setMatricule] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  React.useEffect(() => {
    AccessibilityInfo.announceForAccessibility?.('Connexion Administrateur. Renseignez vos identifiants.');
  }, []);

  const canSubmit = useMemo(
    () => matricule.trim().length > 0 && password.trim().length > 0 && !loading,
    [matricule, password, loading]
  );

  const onSubmit = async () => {
    setError(null);
    if (!canSubmit) {
      setError('Veuillez remplir tous les champs.');
      return;
    }
    const res = await signInAdmin({ matricule: matricule.trim(), password });
    if (res.ok) {
      router.replace('/app');
    } else {
      setError(res.message || 'Échec de la connexion administrateur');
      Alert.alert('Erreur', res.message || 'Échec de la connexion administrateur');
    }
  };

  return (
    <View style={styles.root}>
      <LinearGradient
        colors={isDark ? ['#0a0f1f', '#121a2f'] : ['#e9f1ff', '#ffffff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />
      <View style={[styles.dot, { top: 70, left: -20, backgroundColor: isDark ? '#1b2a5a' : '#cfe0ff' }]} />
      <View style={[styles.dot, { bottom: -30, right: -10, backgroundColor: isDark ? '#10243a' : '#ebf3ff', width: 200, height: 200 }]} />

      <SafeAreaView style={styles.safe}>
        <View style={styles.container}>
          <BlurView intensity={isDark ? 40 : 20} tint={isDark ? 'dark' : 'light'} style={styles.card}>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Connexion Administrateur</Text>

            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Matricule</Text>
              <TextInput
                value={matricule}
                onChangeText={setMatricule}
                placeholder="Entrez votre matricule"
                placeholderTextColor={isDark ? '#94A3B8' : '#94A3B8'}
                autoCapitalize="none"
                autoCorrect={false}
                style={[
                  styles.input,
                  {
                    backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.03)',
                    borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)',
                    color: isDark ? '#E2E8F0' : '#0F172A',
                  },
                ]}
                editable={!loading}
                accessibilityLabel="Champ matricule"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Mot de passe</Text>
              <TextInput
                value={password}
                onChangeText={setPassword}
                placeholder="Entrez votre mot de passe"
                placeholderTextColor={isDark ? '#94A3B8' : '#94A3B8'}
                secureTextEntry
                style={[
                  styles.input,
                  {
                    backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.03)',
                    borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)',
                    color: isDark ? '#E2E8F0' : '#0F172A',
                  },
                ]}
                editable={!loading}
                accessibilityLabel="Champ mot de passe"
              />
            </View>

            {error ? <Text style={[styles.error, { color: '#F87171' }]}>{error}</Text> : null}

            <View style={styles.actions}>
              <TouchableOpacity
                style={[styles.button, styles.primaryBtn]}
                onPress={onSubmit}
                disabled={!canSubmit}
                accessibilityRole="button"
                accessibilityLabel="Se connecter"
                activeOpacity={0.85}
              >
                <LinearGradient
                  colors={['#2563EB', '#1D4ED8']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={[styles.buttonBg, !canSubmit && { opacity: 0.6 }]}
                />
                {loading ? <ActivityIndicator color="#fff" /> : <Text style={styles.primaryText}>Se connecter</Text>}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => router.replace('/auth/welcome')}
                accessibilityRole="button"
                accessibilityLabel="Retour"
                style={[styles.button, styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
              >
                <Text style={[styles.secondaryText, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Retour</Text>
              </TouchableOpacity>
            </View>

            <Text style={[styles.helper, { color: isDark ? '#94A3B8' : '#64748B' }]}>
              Astuce: utilisez vos identifiants fournis par l’établissement.
            </Text>
          </BlurView>
        </View>
      </SafeAreaView>
    </View>
  );
}

function useAuth() {
  // placeholder re-import to keep types the same as original file import location
  return require('../../src/contexts/AuthContext').useAuth();
}

const styles = StyleSheet.create({
  root: { flex: 1 },
  safe: { flex: 1 },
  container: { flex: 1, paddingHorizontal: 20, justifyContent: 'center' },
  dot: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 999,
    opacity: 0.6,
    filter: 'blur(12px)' as any,
  },
  card: {
    borderRadius: 24,
    paddingVertical: 28,
    paddingHorizontal: 20,
    alignItems: 'stretch',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.18)',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 10 },
    elevation: 6,
    gap: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    letterSpacing: 0.2,
    textAlign: 'center',
    marginBottom: 4,
  },
  formGroup: { marginTop: 6 },
  label: { fontSize: 13, fontWeight: '700', marginBottom: 6 },
  input: {
    borderWidth: 1,
    borderRadius: 14,
    paddingHorizontal: 14,
    paddingVertical: 12,
    fontSize: 15.5,
  },
  actions: { width: '100%', marginTop: 8, gap: 12 },
  button: { overflow: 'hidden', width: '100%', borderRadius: 14, alignItems: 'center', justifyContent: 'center' },
  buttonBg: { ...StyleSheet.absoluteFillObject, borderRadius: 14 },
  primaryBtn: { paddingVertical: 14 },
  primaryText: { color: '#fff', fontSize: 16, fontWeight: '800', letterSpacing: 0.2 },
  secondaryBtn: { paddingVertical: 13, borderWidth: 1, backgroundColor: 'transparent' },
  secondaryText: { fontSize: 15.5, fontWeight: '800', letterSpacing: 0.2 },
  error: { marginTop: 4, textAlign: 'center' },
  helper: { marginTop: 8, fontSize: 12.5, textAlign: 'center' },
});