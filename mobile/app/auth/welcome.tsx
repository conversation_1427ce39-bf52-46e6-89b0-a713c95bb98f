import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  AccessibilityInfo,
  useColorScheme,
  Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function WelcomeScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  React.useEffect(() => {
    AccessibilityInfo.announceForAccessibility?.('Bienvenue. Choisissez une option de connexion.');
  }, []);

  const handleAdminLogin = () => {
    router.push('/auth/login-ad');
  };

  const handleUserLogin = () => {
    router.push('/auth/login-user');
  };

  return (
    <View style={styles.root}>
      <LinearGradient
        colors={isDark ? ['#0a0f1f', '#121a2f'] : ['#e9f1ff', '#ffffff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Accent decorative dots */}
      <View style={[styles.dot, { top: 80, left: -20, backgroundColor: isDark ? '#1b2a5a' : '#cfe0ff' }]} />
      <View style={[styles.dot, { top: 160, right: -10, backgroundColor: isDark ? '#16325c' : '#d9ecff', width: 140, height: 140 }]} />
      <View style={[styles.dot, { bottom: -30, left: 40, backgroundColor: isDark ? '#10243a' : '#ebf3ff', width: 220, height: 220 }]} />

      <SafeAreaView style={styles.safe}>
        <View style={styles.container}>
          <BlurView intensity={isDark ? 40 : 20} tint={isDark ? 'dark' : 'light'} style={styles.card}>
            <View style={styles.logoWrap} accessible accessibilityRole="image" accessibilityLabel="NS School Manager logo">
              <View style={[styles.logoCircle, { backgroundColor: isDark ? 'rgba(255,255,255,0.06)' : 'rgba(0,0,0,0.04)' }]}>
                <Image
                  source={require('../../assets/images/icon.png')}
                  resizeMode="contain"
                  style={styles.logo}
                />
              </View>
            </View>

            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
              Bienvenue
            </Text>
            <Text style={[styles.subtitle, { color: isDark ? '#CBD5E1' : '#475569' }]}>
              Veuillez choisir une option de connexion pour continuer
            </Text>

            <View style={styles.actions}>
              <TouchableOpacity
                style={[styles.button, styles.primaryBtn]}
                onPress={handleAdminLogin}
                accessibilityRole="button"
                accessibilityLabel="Connexion Administrateur"
                accessibilityHint="Ouvrir l'écran de connexion administrateur"
                activeOpacity={0.85}
              >
                <LinearGradient
                  colors={['#2563EB', '#1D4ED8']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.buttonBg}
                />
                <Text style={styles.primaryText}>Connexion Administrateur</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
                onPress={handleUserLogin}
                accessibilityRole="button"
                accessibilityLabel="Connexion Élève / Enseignant"
                accessibilityHint="Ouvrir l'écran de connexion pour élèves et enseignants"
                activeOpacity={0.9}
              >
                <Text style={[styles.secondaryText, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>
                  Connexion Élève / Enseignant
                </Text>
              </TouchableOpacity>
            </View>

            <Text
              style={[styles.helper, { color: isDark ? '#94A3B8' : '#64748B' }]}
              accessibilityRole="text"
              accessibilityLabel="Astuce"
            >
              Astuce: vous pouvez changer de rôle plus tard depuis les paramètres.
            </Text>
          </BlurView>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  safe: {
    flex: 1,
  },
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'center',
  },
  dot: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 999,
    opacity: 0.6,
    filter: 'blur(12px)' as any, // ignored on native, safe on web
  },
  card: {
    borderRadius: 24,
    paddingVertical: 28,
    paddingHorizontal: 20,
    alignItems: 'center',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.18)',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 10 },
    elevation: 6,
    gap: 14,
  },
  logoWrap: {
    marginBottom: 4,
  },
  logoCircle: {
    width: 96,
    height: 96,
    borderRadius: 64,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.22)',
  },
  logo: {
    width: 64,
    height: 64,
  },
  title: {
    fontSize: 28,
    fontWeight: '800',
    letterSpacing: 0.2,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 15.5,
    lineHeight: 22,
    marginTop: 2,
    marginBottom: 4,
    paddingHorizontal: 6,
  },
  actions: {
    width: '100%',
    marginTop: 8,
    gap: 12,
  },
  button: {
    overflow: 'hidden',
    width: '100%',
    alignSelf: 'center',
    borderRadius: 14,
  },
  buttonBg: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 14,
  },
  primaryBtn: {
    paddingVertical: 16,
    paddingHorizontal: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryText: {
    color: '#FFFFFF',
    fontSize: 16.5,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  secondaryBtn: {
    backgroundColor: 'transparent',
    paddingVertical: 15,
    paddingHorizontal: 18,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryText: {
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  helper: {
    marginTop: 10,
    fontSize: 12.5,
    textAlign: 'center',
  },
});