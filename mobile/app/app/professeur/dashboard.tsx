import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../src/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { dashboardService } from '../../../src/services/dashboardService';
import type { TimetableEntry, DevoirSummary } from '../../../src/types/dashboard';

export default function ProfDashboard() {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const [timetable, setTimetable] = useState<TimetableEntry[]>([]);
  const [devoirs, setDevoirs] = useState<DevoirSummary[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const isDark = useColorScheme() === 'dark';

  const loadData = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const [tt, dev] = await Promise.all([
        dashboardService.getProfTodayEmploiTemps().catch(() => []),
        dashboardService.getProfPendingDevoir().catch(() => []),
      ]);
      setTimetable(tt);
      setDevoirs(dev);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement du tableau de bord');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadData();
    } finally {
      setRefreshing(false);
    }
  }, [loadData]);

  const handleRetry = () => loadData();

  const handleSignOut = async () => {
    await signOut();
    router.replace('/auth/welcome');
  };

  const nextClass = useMemo(() => {
    if (!timetable.length) return null;
    const now = new Date();
    const toMinutes = (t: string) => {
      const [h, m] = (t || '00:00').split(':').map(Number);
      return h * 60 + (m || 0);
    };
    const nowMin = now.getHours() * 60 + now.getMinutes();
    const upcoming = [...timetable].sort((a, b) => toMinutes(a.start) - toMinutes(b.start)).find(e => toMinutes(e.start) >= nowMin);
    return upcoming || timetable[0];
  }, [timetable]);

  const renderEntry = ({ item }: { item: TimetableEntry }) => (
    <View style={[styles.item, { borderColor: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.06)' }]}>
      <Text style={[styles.itemTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>{item.subject || 'Cours'}</Text>
      <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
        {item.start || 'N/A'} - {item.end || 'N/A'} {item.room ? `• Salle ${item.room}` : ''}
      </Text>
    </View>
  );

  return (
    <View style={styles.root}>
      <LinearGradient
        colors={isDark ? ['#0a0f1f', '#121a2f'] : ['#e9f1ff', '#ffffff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />
      <View style={[styles.dot, { top: 70, left: -20, backgroundColor: isDark ? '#1b2a5a' : '#cfe0ff' }]} />
      <View style={[styles.dot, { bottom: -30, right: -10, backgroundColor: isDark ? '#10243a' : '#ebf3ff', width: 200, height: 200 }]} />

      <SafeAreaView style={styles.safe}>
        <View style={styles.container}>
          <View style={styles.headerRow}>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Professeur Dashboard</Text>
            <TouchableOpacity
              onPress={handleSignOut}
              style={[styles.signoutBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
              accessibilityRole="button"
              accessibilityLabel="Se déconnecter"
            >
              <Text style={[styles.signoutText, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Se déconnecter</Text>
            </TouchableOpacity>
          </View>

          <BlurView intensity={isDark ? 40 : 20} tint={isDark ? 'dark' : 'light'} style={styles.card}>
            {loading ? (
              <View style={styles.centerBox}>
                <ActivityIndicator />
                <Text style={[styles.info, { color: isDark ? '#CBD5E1' : '#334155' }]}>Chargement...</Text>
              </View>
            ) : error ? (
              <View style={styles.centerBox}>
                <Text style={[styles.errorText, { color: '#F87171' }]}>{error}</Text>
                <TouchableOpacity
                  style={styles.retryBtn}
                  onPress={handleRetry}
                  accessibilityRole="button"
                  accessibilityLabel="Réessayer"
                >
                  <LinearGradient
                    colors={['#2563EB', '#1D4ED8']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.retryBtnBg}
                  />
                  <Text style={styles.retryText}>Réessayer</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                ListHeaderComponent={
                  <View>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                      Matricule: {user?.matricule ?? 'unknown'}
                    </Text>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                      Role: {user?.role ?? 'unknown'}
                    </Text>

                    <View style={[styles.statCardHalf, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                      <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>
                        Prochain cours aujourd'hui
                      </Text>
                      <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                        {nextClass
                          ? `${nextClass.subject || 'Cours'} • ${nextClass.start || 'N/A'} - ${nextClass.end || 'N/A'}${nextClass.room ? ` • Salle ${nextClass.room}` : ''}`
                          : 'N/A'}
                      </Text>
                    </View>

                    <View style={styles.cardsRow}>
                      <View style={[styles.statCardHalf, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                        <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>Devoirs en attente</Text>
                        <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{devoirs.length}</Text>
                      </View>
                      <View style={[styles.statCardHalf, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                        <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>Créneaux aujourd'hui</Text>
                        <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{timetable.length}</Text>
                      </View>
                    </View>

                    <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Prochains créneaux</Text>
                  </View>
                }
                data={timetable.slice(0, 3)}
                keyExtractor={(item) => String(item.id)}
                renderItem={renderEntry}
                ItemSeparatorComponent={() => <View style={[styles.separator, { backgroundColor: isDark ? 'rgba(148,163,184,0.12)' : '#e5e7eb' }]} />}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
                contentContainerStyle={timetable.length === 0 ? { flexGrow: 1 } : undefined}
                ListEmptyComponent={
                  <View style={styles.centerBox}>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>Aucun créneau trouvé pour aujourd'hui</Text>
                  </View>
                }
              />
            )}
          </BlurView>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  root: { flex: 1 },
  safe: { flex: 1 },
  container: { flex: 1, paddingHorizontal: 16, paddingTop: 8 },
  dot: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 999,
    opacity: 0.6,
    filter: 'blur(12px)' as any,
  },
  headerRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 },
  title: { fontSize: 22, fontWeight: '800', letterSpacing: 0.2 },
  signoutBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  signoutText: { fontSize: 13, fontWeight: '800', letterSpacing: 0.2 },
  card: {
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.18)',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 10 },
    elevation: 6,
    gap: 12,
  },
  centerBox: { alignItems: 'center', justifyContent: 'center', paddingVertical: 24 },
  info: { fontSize: 14 },
  errorText: { textAlign: 'center', marginBottom: 12 },
  retryBtn: { overflow: 'hidden', borderRadius: 12, alignItems: 'center', justifyContent: 'center' },
  retryBtnBg: { ...StyleSheet.absoluteFillObject, borderRadius: 12, height: 40, width: 140 },
  retryText: { color: '#fff', fontWeight: '800', letterSpacing: 0.2, paddingVertical: 10, paddingHorizontal: 16 },
  cardsRow: { flexDirection: 'row', gap: 12, marginTop: 12 },
  statCard: {
    padding: 14,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderColor: 'rgba(148,163,184,0.18)',
    marginTop: 12,
  },
  statCardHalf: {
    flex: 1,
    padding: 14,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
  },
  cardLabel: { fontSize: 12, fontWeight: '700' },
  cardValue: { fontSize: 16, fontWeight: '800', marginTop: 4 },
  sectionTitle: { fontSize: 18, fontWeight: '800', marginTop: 16, marginBottom: 8, letterSpacing: 0.2 },
  item: { paddingVertical: 10, borderBottomWidth: 1 },
  itemTitle: { fontSize: 14, fontWeight: '800' },
  itemMeta: { fontSize: 12, marginTop: 2 },
  separator: { height: 1 },
});