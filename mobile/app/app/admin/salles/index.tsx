import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, FlatList, RefreshControl, ActivityIndicator, TouchableOpacity, useColorScheme, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import type { Salle } from '../../../../src/types/salles';
import { sallesService } from '../../../../src/services/sallesService';

export default function SallesListScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [data, setData] = useState<Salle[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const load = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const res = await sallesService.getSalles();
      const list = Array.isArray(res) ? res : res?.data || [];
      setData(list);
    } catch (e: any) {
      setError(e?.message || 'Erreur de chargement des salles');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    load();
  }, [load]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await load();
    } finally {
      setRefreshing(false);
    }
  }, [load]);

  const renderItem = ({ item }: { item: Salle }) => (
    <TouchableOpacity
      onPress={() => router.push({ pathname: '/app/admin/salles/[id]', params: { id: String(item.id) } as any })}
      style={[styles.item, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}
      accessibilityRole="button"
      accessibilityLabel={`Voir la salle ${item.nom_salle}`}
    >
      <Text style={[styles.itemTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{item.nom_salle}</Text>
      <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
        {item.type_salle} • Capacité: {item.capacite} • {item.disponible ? 'Disponible' : 'Indisponible'}
      </Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text>
        <TouchableOpacity onPress={load} style={styles.retryBtn}>
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <View style={styles.headerRow}>
        <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Salles</Text>
        <TouchableOpacity
          onPress={() => router.push({ pathname: '/app/admin/salles/create' } as any)}
          style={[styles.createBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          accessibilityRole="button"
          accessibilityLabel="Ajouter une salle"
        >
          <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Ajouter</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={Array.isArray(data) ? data.filter(Boolean) : []}
        keyExtractor={(item, index) => {
          const key = (item as any)?.id ?? `${(item as any)?.nom_salle || 'salle'}-${(item as any)?.batiment || 'bat'}-${(item as any)?.capacite || '0'}`;
          return String(key ?? index);
        }}
        renderItem={renderItem}
        contentContainerStyle={(Array.isArray(data) ? data.length : 0) === 0 ? { flexGrow: 1 } : undefined}
        ListEmptyComponent={
          <View style={styles.center}>
            <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Aucune salle trouvée</Text>
          </View>
        }
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  headerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  createBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },
  item: { padding: 14, borderRadius: 16, borderWidth: 1, marginBottom: 10 },
  itemTitle: { fontSize: 16, fontWeight: '800' },
  itemMeta: { fontSize: 12, marginTop: 4 },
});