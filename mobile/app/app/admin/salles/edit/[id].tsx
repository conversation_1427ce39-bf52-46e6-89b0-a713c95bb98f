import React, { useEffect, useState } from 'react';
import { View, Text, TextInput, Switch, TouchableOpacity, StyleSheet, ActivityIndicator, useColorScheme, ScrollView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import type { Salle, UpdateSalleDto, SalleType } from '../../../../../src/types/salles';
import { sallesService } from '../../../../../src/services/sallesService';
import Select from '../../../../../src/components/Select';
import { useSalleFormOptions } from '../../../../../src/hooks/useSalleFormOptions';

export default function SalleEditScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const { options: formOptions, loading: optionsLoading, error: optionsError } = useSalleFormOptions();
  const isDark = colorScheme === 'dark';

  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [form, setForm] = useState<UpdateSalleDto>({});
  const [showCustomCapacity, setShowCustomCapacity] = useState(false);
  const [showCustomBuilding, setShowCustomBuilding] = useState(false);

  useEffect(() => {
    const load = async () => {
      if (!id) return;
      setError(null);
      setLoading(true);
      try {
        const data: Salle = await sallesService.getSalleById(id);
        setForm({
          nom_salle: data.nom_salle,
          type_salle: data.type_salle,
          capacite: data.capacite,
          etage: data.etage,
          batiment: data.batiment,
          equipements: data.equipements,
          disponible: data.disponible,
        });
      } catch (e: any) {
        setError(e?.message || 'Erreur lors du chargement');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [id]);

  const update = (patch: Partial<UpdateSalleDto>) => setForm((f) => ({ ...f, ...patch }));

  const handleSubmit = async () => {
    if (!id) return;
    setError(null);
    if (!form.nom_salle || !form.type_salle || !form.batiment) {
      setError('Veuillez remplir les champs obligatoires (nom, type, bâtiment).');
      return;
    }
    setSubmitting(true);
    try {
      await sallesService.updateSalle(id, form);
      router.replace({ pathname: '/app/admin/salles/[id]', params: { id: String(id) } } as any);
    } catch (e: any) {
      setError(e?.message || 'Échec de la mise à jour');
    } finally {
      setSubmitting(false);
    }
  };

  // Show loading state while fetching form options or salle data
  if (loading || optionsLoading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color={isDark ? '#3B82F6' : '#2563EB'} />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>
          {loading ? 'Chargement de la salle...' : 'Chargement des options...'}
        </Text>
      </View>
    );
  }

  // Show error state if options failed to load
  if (optionsError || !formOptions) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#EF4444', textAlign: 'center', marginBottom: 16 }}>
          {optionsError || 'Erreur lors du chargement des options'}
        </Text>
        <TouchableOpacity
          style={[styles.submitBtn, { backgroundColor: isDark ? '#3B82F6' : '#2563EB' }]}
          onPress={() => router.replace(`/app/admin/salles/edit/${id}`)}
        >
          <Text style={styles.submitText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }
 
  return (
    <ScrollView contentContainerStyle={{ padding: 16 }}>
      <View style={styles.headerWrap}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flexShrink: 1 }]} numberOfLines={1}>
            Modifier la salle
          </Text>
        </View>
        <View style={styles.actionsRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Annuler"
            style={[styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Annuler</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={submitting}
            style={[styles.primaryBtn, { opacity: submitting ? 0.7 : 1 }]}
            accessibilityRole="button"
            accessibilityLabel="Enregistrer la salle"
          >
            <Text style={{ color: '#fff', fontWeight: '800' }}>{submitting ? 'Enregistrement...' : 'Enregistrer'}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {error ? <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text> : null}

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nom</Text>
      <TextInput
        value={form.nom_salle || ''}
        onChangeText={(t) => update({ nom_salle: t })}
        placeholder="Ex: Salle A-101"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Select
        label="Type de salle *"
        options={formOptions.roomTypes}
        value={form.type_salle as string}
        onValueChange={(value) => update({ type_salle: value as SalleType })}
        placeholder="Sélectionner un type"
        style={{ marginBottom: 16 }}
      />

      <Select
        label="Capacité *"
        options={formOptions.capacities}
        value={showCustomCapacity ? 'custom' : String(form.capacite || 0)}
        onValueChange={(value) => {
          if (value === 'custom') {
            setShowCustomCapacity(true);
          } else {
            setShowCustomCapacity(false);
            update({ capacite: Number(value) });
          }
        }}
        placeholder="Sélectionner la capacité"
        style={{ marginBottom: showCustomCapacity ? 8 : 16 }}
      />

      {showCustomCapacity && (
        <View style={{ marginBottom: 16 }}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Capacité personnalisée</Text>
          <TextInput
            keyboardType="numeric"
            value={String(form.capacite || 0)}
            onChangeText={(t) => update({ capacite: Number(t) || 0 })}
            placeholder="Ex: 45"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
          />
        </View>
      )}

      <Select
        label="Étage *"
        options={formOptions.floors}
        value={form.etage || ''}
        onValueChange={(value) => update({ etage: value })}
        placeholder="Sélectionner un étage"
        style={{ marginBottom: 16 }}
      />

      <Select
        label="Bâtiment *"
        options={formOptions.buildings}
        value={showCustomBuilding ? 'custom' : form.batiment || ''}
        onValueChange={(value) => {
          if (value === 'custom') {
            setShowCustomBuilding(true);
          } else {
            setShowCustomBuilding(false);
            update({ batiment: value });
          }
        }}
        placeholder="Sélectionner un bâtiment"
        style={{ marginBottom: showCustomBuilding ? 8 : 16 }}
      />

      {showCustomBuilding && (
        <View style={{ marginBottom: 16 }}>
          <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Bâtiment personnalisé</Text>
          <TextInput
            value={form.batiment || ''}
            onChangeText={(t) => update({ batiment: t })}
            placeholder="Ex: Bâtiment D"
            placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
            style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
          />
        </View>
      )}

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Équipements</Text>
      <TextInput
        value={form.equipements || ''}
        onChangeText={(t) => update({ equipements: t })}
        placeholder="Ex: Projecteur, tableau"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <View style={styles.row}>
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', fontWeight: '700' }}>Disponible</Text>
        <Switch value={!!form.disponible} onValueChange={(v) => update({ disponible: v })} />
      </View>

      <TouchableOpacity
        onPress={handleSubmit}
        disabled={submitting}
        style={[styles.submitBtn, { opacity: submitting ? 0.7 : 1 }]}
        accessibilityRole="button"
        accessibilityLabel="Enregistrer la salle"
      >
        {submitting ? <ActivityIndicator color="#fff" /> : <Text style={styles.submitText}>Enregistrer</Text>}
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },

  // Responsive header (match detail/create)
  headerWrap: { gap: 8, marginBottom: 12 },
  headerTopRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  actionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },

  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },

  label: { fontSize: 12, fontWeight: '700', marginTop: 8, marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
  row: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginTop: 12 },
  submitBtn: { marginTop: 16, backgroundColor: '#2563EB', paddingVertical: 12, borderRadius: 12, alignItems: 'center' },
  submitText: { color: '#fff', fontWeight: '800' },
});