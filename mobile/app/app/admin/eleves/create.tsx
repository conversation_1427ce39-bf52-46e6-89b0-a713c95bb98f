import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, useColorScheme, ScrollView } from 'react-native';
import { useRouter } from 'expo-router';
import { ApiServices } from '../../../../src/services/apiService';
import Select from '../../../../src/components/Select';
import DateInput from '../../../../src/components/DateInput';
import type { Classe, CreateEleveRequest } from '../../../../src/types/api';

type Sexe = 'homme' | 'femme';

export default function AdminEleveCreateScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [form, setForm] = useState<CreateEleveRequest>({
    matricule: '',
    nom: '',
    prenom: '',
    sexe: '' as any,
    date_naissance: '',
    classe: '',
  });
  const [classes, setClasses] = useState<Classe[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const update = (patch: Partial<CreateEleveRequest>) => setForm((f) => ({ ...f, ...patch }));

  useEffect(() => {
    const loadOptions = async () => {
      try {
        const cls = await ApiServices.academic().getClasses();
        setClasses(cls);
      } catch (e: any) {
        setError(e?.message || 'Erreur lors du chargement des classes');
      } finally {
        setLoadingOptions(false);
      }
    };
    loadOptions();
  }, []);

  const sexeOptions = useMemo(() => ([
    { label: 'Homme', value: 'homme' },
    { label: 'Femme', value: 'femme' },
  ]), []);

  const classeOptions = useMemo(() => {
    return classes.map((c) => ({ label: c.nom_classe, value: c.nom_classe }));
  }, [classes]);

  const handleSubmit = async () => {
    setError(null);
    if (!form.matricule || !form.nom || !form.prenom || !form.sexe || !form.classe) {
      setError('Veuillez remplir tous les champs obligatoires.');
      return;
    }
    setSubmitting(true);
    try {
      await ApiServices.user().createEleve(form);
      router.replace('/app/admin/eleves');
    } catch (e: any) {
      setError(e?.message || 'Échec de création');
    } finally {
      setSubmitting(false);
    }
  };

  if (loadingOptions) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color={isDark ? '#3B82F6' : '#2563EB'} />
        <Text style={{ marginTop: 16, color: isDark ? '#CBD5E1' : '#64748B' }}>
          Chargement des options...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={{ padding: 16 }}>
      <View style={styles.headerWrap}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>
          <Text
            style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flexShrink: 1 }]}
            numberOfLines={1}
          >
            Ajouter un élève
          </Text>
        </View>
        <View style={styles.actionsRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Annuler"
            style={[styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Annuler</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={submitting}
            style={[styles.primaryBtn, { opacity: submitting ? 0.7 : 1 }]}
            accessibilityRole="button"
            accessibilityLabel="Créer l'élève"
          >
            <Text style={{ color: '#fff', fontWeight: '800' }}>{submitting ? 'Création...' : 'Créer'}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {error ? <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text> : null}

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Matricule *</Text>
      <TextInput
        value={form.matricule}
        onChangeText={(t) => update({ matricule: t })}
        placeholder="Ex: E12345"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nom *</Text>
      <TextInput
        value={form.nom}
        onChangeText={(t) => update({ nom: t })}
        placeholder="Nom"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Prénom *</Text>
      <TextInput
        value={form.prenom}
        onChangeText={(t) => update({ prenom: t })}
        placeholder="Prénom"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Select
        label="Sexe *"
        options={sexeOptions}
        value={form.sexe as unknown as string}
        onValueChange={(value) => update({ sexe: value as any as Sexe })}
        placeholder="Sélectionner le sexe"
        style={{ marginBottom: 16 }}
      />

      <DateInput
        label="Date Naissance"
        value={form.date_naissance}
        onChange={(v) => update({ date_naissance: v })}
        placeholder="YYYY-MM-DD"
        inputStyle={[styles.input, { borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
        accessibilityLabel="Choisir la date de naissance"
      />

      <Select
        label="Classe *"
        options={classeOptions}
        value={form.classe}
        onValueChange={(value) => update({ classe: value })}
        placeholder="Sélectionner une classe"
        style={{ marginBottom: 16 }}
      />

      {/* Mot de passe optionnel non typé dans CreateEleveRequest: champ omis pour respecter les types.
          Si nécessaire côté backend, il peut être géré via un autre endpoint ou extension de types. */}

      <TouchableOpacity
        onPress={handleSubmit}
        disabled={submitting}
        style={[styles.submitBtn, { opacity: submitting ? 0.7 : 1 }]}
        accessibilityRole="button"
        accessibilityLabel="Créer l'élève"
      >
        {submitting ? <ActivityIndicator color="#fff" /> : <Text style={styles.submitText}>Créer</Text>}
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 },

  headerWrap: { gap: 8, marginBottom: 12 },
  headerTopRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  actionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },

  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },

  label: { fontSize: 12, fontWeight: '700', marginTop: 8, marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
  submitBtn: { marginTop: 16, backgroundColor: '#2563EB', paddingVertical: 12, borderRadius: 12, alignItems: 'center' },
  submitText: { color: '#fff', fontWeight: '800' },
});