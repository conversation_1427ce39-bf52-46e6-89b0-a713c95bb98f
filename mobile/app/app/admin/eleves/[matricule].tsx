import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, ActivityIndicator, TouchableOpacity, StyleSheet, useColorScheme, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ApiServices } from '../../../../src/services/apiService';
import type { Eleve } from '../../../../src/types/api';

export default function AdminEleveDetailScreen() {
  const { matricule } = useLocalSearchParams<{ matricule: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [eleve, setEleve] = useState<Eleve | null>(null);
  const [loading, setLoading] = useState(true);
  const [pendingDelete, setPendingDelete] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const load = useCallback(async () => {
    if (!matricule) return;
    setError(null);
    setLoading(true);
    try {
      // No dedicated get-by-id in ApiServices; fetch all and pick one (backend has GET /api/eleve returning array)
      const list = await ApiServices.user().getEleves();
      const found = list.find(e => String(e.matricule) === String(matricule)) || null;
      if (!found) throw new Error('Élève introuvable');
      setEleve(found);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement');
    } finally {
      setLoading(false);
    }
  }, [matricule]);

  useEffect(() => {
    load();
  }, [load]);

  const handleDelete = () => {
    if (!matricule) return;
    Alert.alert('Supprimer', "Voulez-vous vraiment supprimer cet élève ?", [
      { text: 'Annuler', style: 'cancel' },
      {
        text: 'Supprimer',
        style: 'destructive',
        onPress: async () => {
          try {
            setPendingDelete(true);
            await ApiServices.user().deleteEleve(String(matricule));
            router.replace('/app/admin/eleves');
          } catch (e: any) {
            Alert.alert('Erreur', e?.message || 'Échec de suppression');
          } finally {
            setPendingDelete(false);
          }
        },
      },
    ]);
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement...</Text>
      </View>
    );
  }

  if (error || !eleve) {
    return (
      <View style={styles.center}>
        <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error ?? 'Élève introuvable'}</Text>
        <TouchableOpacity onPress={load} style={styles.retryBtn}>
          <Text style={{ color: '#fff', fontWeight: '800' }}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ padding: 16 }}>
      <View style={styles.headerWrap}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>

          <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flexShrink: 1 }]} numberOfLines={1}>
            {eleve.nom} {eleve.prenom}
          </Text>
        </View>

        <View style={styles.actionsRow}>
          <TouchableOpacity
            onPress={() => router.push({ pathname: '/app/admin/eleves/edit/[matricule]', params: { matricule: String(eleve.matricule) } } as any)}
            style={[styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            accessibilityRole="button"
            accessibilityLabel="Modifier l'élève"
          >
            <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Modifier</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleDelete}
            disabled={pendingDelete}
            style={[styles.deleteBtn, { opacity: pendingDelete ? 0.7 : 1 }]}
            accessibilityRole="button"
            accessibilityLabel="Supprimer l'élève"
          >
            <Text style={{ color: '#fff', fontWeight: '800' }}>{pendingDelete ? 'Suppression...' : 'Supprimer'}</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={[styles.card, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
        <Row label="Matricule" value={String(eleve.matricule)} isDark={isDark} />
        <Row label="Sexe" value={eleve.sexe || '—'} isDark={isDark} />
        <Row label="Date Naissance" value={eleve.date_naissance || '—'} isDark={isDark} />
        <Row label="Classe" value={eleve.nom_classe || '—'} isDark={isDark} />
        <Row label="Email" value={(eleve as any).email || '—'} isDark={isDark} />
        <Row label="Téléphone" value={(eleve as any).telephone || '—'} isDark={isDark} />
      </View>
    </ScrollView>
    </SafeAreaView>
  );
}

function Row({ label, value, isDark }: { label: string; value: string; isDark: boolean }) {
  return (
    <View style={rowStyles.row}>
      <Text style={[rowStyles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>{label}</Text>
      <Text style={[rowStyles.value, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{value}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, alignItems: 'center', justifyContent: 'center', padding: 16 },
  retryBtn: { backgroundColor: '#2563EB', paddingHorizontal: 16, paddingVertical: 10, borderRadius: 10 },

  headerWrap: { gap: 8, marginBottom: 12 },
  headerTopRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  actionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },

  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 },
  deleteBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#ef4444' },

  card: { padding: 14, borderRadius: 16, borderWidth: 1, marginTop: 8, gap: 10 },
});

const rowStyles = StyleSheet.create({
  row: { flexDirection: 'row', justifyContent: 'space-between' },
  label: { fontSize: 12, fontWeight: '700' },
  value: { fontSize: 14, fontWeight: '800' },
});