import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, useColorScheme, FlatList, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { ApiServices } from '../../../../src/services/apiService';
import type { Professeur } from '../../../../src/types/api';

export default function AdminProfsListScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [list, setList] = useState<Professeur[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [q, setQ] = useState('');

  const load = async () => {
    setError(null);
    setLoading(true);
    try {
      const data = await ApiServices.user().getProfesseurs();
      setList(data);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement des professeurs');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    load();
  }, []);

  const filtered = useMemo(() => {
    const t = q.trim().toLowerCase();
    if (!t) return list;
    return list.filter(p => {
      return (
        String(p.matricule ?? '').toLowerCase().includes(t) ||
        String(p.nom ?? '').toLowerCase().includes(t) ||
        String(p.prenom ?? '').toLowerCase().includes(t) ||
        String((p as any).specialiste ?? '').toLowerCase().includes(t)
      );
    });
  }, [q, list]);

  const confirmDelete = (matricule: string) => {
    Alert.alert('Confirmation', 'Supprimer ce professeur ?', [
      { text: 'Annuler', style: 'cancel' },
      {
        text: 'Supprimer',
        style: 'destructive',
        onPress: async () => {
          try {
            await ApiServices.user().deleteProfesseur(matricule);
            await load();
          } catch (e: any) {
            Alert.alert('Erreur', e?.message || 'Échec de la suppression');
          }
        },
      },
    ]);
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color={isDark ? '#3B82F6' : '#2563EB'} />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement des professeurs...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, padding: 16, gap: 12 }}>
      <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
        <TouchableOpacity
          onPress={() => router.back()}
          accessibilityRole="button"
          accessibilityLabel="Retour"
          style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
        >
          <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
        </TouchableOpacity>
        <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flex: 1, textAlign: 'center' }]}>Professeurs</Text>
        <TouchableOpacity
          onPress={() => router.push({ pathname: '/app/admin/profs/create' } as any)}
          style={[styles.primaryBtn]}
          accessibilityRole="button"
          accessibilityLabel="Ajouter un professeur"
        >
          <Text style={{ color: '#fff', fontWeight: '800' }}>Ajouter</Text>
        </TouchableOpacity>
      </View>

      {error ? <Text style={{ color: '#ef4444' }}>{error}</Text> : null}

      <TextInput
        value={q}
        onChangeText={setQ}
        placeholder="Recherche: matricule, nom, prénom, spécialité"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[
          styles.input,
          { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' },
        ]}
      />

      <FlatList
        data={filtered}
        keyExtractor={(item) => String(item.matricule)}
        ItemSeparatorComponent={() => <View style={{ height: 8 }} />}
        renderItem={({ item }) => (
          <TouchableOpacity
            onPress={() => router.push({ pathname: '/app/admin/profs/[matricule]', params: { matricule: String(item.matricule) } } as any)}
            style={[
              styles.row,
              { borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)', backgroundColor: isDark ? '#0B1220' : '#fff' },
            ]}
            accessibilityRole="button"
            accessibilityLabel={`Voir professeur ${item.nom} ${item.prenom}`}
          >
            <View style={{ flex: 1 }}>
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }} numberOfLines={1}>
                {item.nom} {item.prenom}
              </Text>
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }} numberOfLines={1}>
                Matricule: {item.matricule} • Spécialité: {(item as any).specialiste ?? '—'}
              </Text>
              <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }} numberOfLines={1}>
                {item.email} • {item.telephone}
              </Text>
            </View>
            <View style={{ gap: 8, flexDirection: 'row' }}>
              <TouchableOpacity
                onPress={() => router.push({ pathname: '/app/admin/profs/edit/[matricule]', params: { matricule: String(item.matricule) } } as any)}
                style={[styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
                accessibilityRole="button"
                accessibilityLabel={`Modifier ${item.matricule}`}
              >
                <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Modifier</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => confirmDelete(String(item.matricule))}
                style={[styles.dangerBtn]}
                accessibilityRole="button"
                accessibilityLabel={`Supprimer ${item.matricule}`}
              >
                <Text style={{ color: '#fff', fontWeight: '800' }}>Suppr.</Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        )}
        ListEmptyComponent={() => (
          <View style={{ padding: 24, alignItems: 'center' }}>
            <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Aucun professeur trouvé</Text>
          </View>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 },
  title: { fontSize: 20, fontWeight: '800' },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
  row: { borderWidth: 1, borderRadius: 12, padding: 12, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 },
  dangerBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#DC2626' },
  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
});