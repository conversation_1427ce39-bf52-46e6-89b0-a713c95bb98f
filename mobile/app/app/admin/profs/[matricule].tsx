import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator, useColorScheme, ScrollView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ApiServices } from '../../../../src/services/apiService';
import type { Professeur } from '../../../../src/types/api';

export default function AdminProfDetailScreen() {
  const { matricule } = useLocalSearchParams<{ matricule: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [prof, setProf] = useState<Professeur | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const load = async () => {
    if (!matricule) return;
    setError(null);
    setLoading(true);
    try {
      const list = await ApiServices.user().getProfesseurs();
      const found = list.find(p => String(p.matricule) === String(matricule)) || null;
      if (!found) throw new Error('Professeur introuvable');
      setProf(found);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    load();
  }, [matricule]);

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color={isDark ? '#3B82F6' : '#2563EB'} />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement…</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.center, { padding: 16 }]}>
        <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text>
        <TouchableOpacity
          onPress={() => router.back()}
          style={[{ paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 }, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
        >
          <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Retour</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!prof) {
    return (
      <View style={styles.center}>
        <Text style={{ color: isDark ? '#94A3B8' : '#64748B' }}>Professeur introuvable</Text>
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={{ padding: 16, gap: 12 }}>
      <View style={styles.headerWrap}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>
          <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flexShrink: 1 }]} numberOfLines={1}>
            {prof.nom} {prof.prenom}
          </Text>
        </View>
        <View style={styles.actionsRow}>
          <TouchableOpacity
            onPress={() => router.push({ pathname: '/app/admin/profs/edit/[matricule]', params: { matricule: String(prof.matricule) } } as any)}
            accessibilityRole="button"
            accessibilityLabel="Modifier"
            style={[styles.primaryBtn]}
          >
            <Text style={{ color: '#fff', fontWeight: '800' }}>Modifier</Text>
          </TouchableOpacity>
        </View>
      </View>

      <View style={[styles.card, { borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)', backgroundColor: isDark ? '#0B1220' : '#fff' }]}>
        <Row label="Matricule" value={String(prof.matricule ?? '—')} isDark={isDark} />
        <Row label="Nom" value={prof.nom ?? '—'} isDark={isDark} />
        <Row label="Prénom" value={prof.prenom ?? '—'} isDark={isDark} />
        <Row label="Sexe" value={String(prof.sexe ?? '—')} isDark={isDark} />
        <Row label="Spécialité" value={String((prof as any).specialiste ?? '—')} isDark={isDark} />
        <Row label="Email" value={prof.email ?? '—'} isDark={isDark} />
        <Row label="Téléphone" value={prof.telephone ?? '—'} isDark={isDark} />
      </View>
    </ScrollView>
  );
}

function Row({ label, value, isDark }: { label: string; value: string; isDark: boolean }) {
  return (
    <View style={{ paddingVertical: 8 }}>
      <Text style={{ color: isDark ? '#94A3B8' : '#64748B', fontSize: 12, marginBottom: 2 }}>{label}</Text>
      <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '700' }}>{value || '—'}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  headerWrap: { gap: 8, marginBottom: 12 },
  headerTopRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  actionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },

  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },

  card: { borderWidth: 1, borderRadius: 12, padding: 12 },
});