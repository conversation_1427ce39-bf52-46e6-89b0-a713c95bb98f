import React, { useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, useColorScheme, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { ApiServices } from '../../../../src/services/apiService';
import Select from '../../../../src/components/Select';
import type { CreateProfesseurRequest } from '../../../../src/types/api';

type Sexe = 'homme' | 'femme';

export default function AdminProfCreateScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [form, setForm] = useState<CreateProfesseurRequest & { password?: string }>({
    matricule: '',
    nom: '',
    prenom: '',
    sexe: '' as any,
    specialiste: '',
    email: '',
    telephone: '',
    password: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Track current search text for specialty combobox
  const [queryText, setQueryText] = useState<string>('');

  const [specialites, setSpecialites] = useState<string[]>([]);
  const [loadingOptions, setLoadingOptions] = useState<boolean>(true);

  const update = (patch: Partial<typeof form>) => setForm((f) => ({ ...f, ...patch }));

  useEffect(() => {
    const loadSpecialites = async () => {
      try {
        const specs = await ApiServices.user().getProfSpecialites();
        setSpecialites(specs);
      } catch (e: any) {
        // do not block the form; just show an error message
        setError(e?.message || 'Erreur lors du chargement des spécialités');
      } finally {
        setLoadingOptions(false);
      }
    };
    loadSpecialites();
  }, []);

  const sexeOptions = useMemo(() => ([
    { label: 'Homme', value: 'homme' },
    { label: 'Femme', value: 'femme' },
  ]), []);

  const specialiteOptions = useMemo(() => {
    return specialites.map(s => ({ label: s, value: s }));
  }, [specialites]);

  // Compute enhanced options based on queryText with a synthetic "Create …" option if no exact match
  const filteredSpecialiteOptions = useMemo(() => {
    const q = queryText.trim().toLowerCase();
    const filtered = specialiteOptions.filter(o => o.label.toLowerCase().includes(q));
    const hasMatch = q.length > 0 && filtered.some(o => o.label.toLowerCase().trim() === q);
    if (hasMatch || q.length === 0) return filtered;
    return [
      ...filtered,
      { label: `Create “${queryText.trim()}”`, value: `__create__:${queryText.trim()}` },
    ];
  }, [specialiteOptions, queryText]);

  const handleSubmit = async () => {
    setError(null);
    if (!form.matricule || !form.nom || !form.prenom || !form.sexe || !form.specialiste || !form.email || !form.telephone) {
      setError('Veuillez remplir tous les champs obligatoires.');
      return;
    }
    setSubmitting(true);
    try {
      const payload: CreateProfesseurRequest & { password?: string } = {
        matricule: form.matricule,
        nom: form.nom,
        prenom: form.prenom,
        sexe: form.sexe as any,
        specialiste: form.specialiste,
        email: form.email,
        telephone: form.telephone,
      };
      if (form.password && form.password.trim().length > 0) {
        payload.password = form.password.trim();
      }
      await ApiServices.user().createProfesseur(payload);
      router.replace('/app/admin/profs');
    } catch (e: any) {
      setError(e?.message || 'Échec de la création du professeur');
    } finally {
      setSubmitting(false);
    }
  };

  if (loadingOptions) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color={isDark ? '#3B82F6' : '#2563EB'} />
        <Text style={{ marginTop: 16, color: isDark ? '#CBD5E1' : '#64748B' }}>Chargement des options...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ScrollView contentContainerStyle={{ padding: 16 }}>
        <View style={styles.headerWrap}>
          <View style={styles.headerTopRow}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Retour"
              style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
            </TouchableOpacity>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flexShrink: 1 }]} numberOfLines={1}>
              Ajouter un professeur
            </Text>
          </View>
          <View style={styles.actionsRow}>
            <TouchableOpacity
              onPress={() => router.back()}
              accessibilityRole="button"
              accessibilityLabel="Annuler"
              style={[styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
            >
              <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Annuler</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleSubmit}
              disabled={submitting}
              style={[styles.primaryBtn, { opacity: submitting ? 0.7 : 1 }]}
              accessibilityRole="button"
              accessibilityLabel="Créer le professeur"
            >
              <Text style={{ color: '#fff', fontWeight: '800' }}>{submitting ? 'Création...' : 'Créer'}</Text>
            </TouchableOpacity>
          </View>
        </View>

      {error ? <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text> : null}

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Matricule *</Text>
      <TextInput
        value={form.matricule}
        onChangeText={(t) => update({ matricule: t })}
        placeholder="Ex: P12345"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
        autoCapitalize="characters"
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nom *</Text>
      <TextInput
        value={form.nom}
        onChangeText={(t) => update({ nom: t })}
        placeholder="Nom"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Prénom *</Text>
      <TextInput
        value={form.prenom}
        onChangeText={(t) => update({ prenom: t })}
        placeholder="Prénom"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Select
        label="Sexe *"
        options={sexeOptions}
        value={form.sexe as unknown as string}
        onValueChange={(value) => update({ sexe: value as any as Sexe })}
        placeholder="Sélectionner le sexe"
        style={{ marginBottom: 16 }}
      />

      {/* Search input controlling the specialty filter */}
      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Rechercher une spécialité</Text>
      <TextInput
        value={queryText}
        onChangeText={setQueryText}
        placeholder="Rechercher..."
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)', marginBottom: 8 }]}
      />

      <Select
        label="Spécialité *"
        options={filteredSpecialiteOptions}
        value={form.specialiste}
        onValueChange={(value) => {
          if (typeof value === 'string' && value.startsWith('__create__:')) {
            const customText = value.replace('__create__:', '').trim();
            update({ specialiste: customText });
            // Optional UX polish: clear query so full list shows again
            setQueryText('');
          } else {
            update({ specialiste: value });
          }
        }}
        placeholder="Sélectionner une spécialité"
        style={{ marginBottom: 16 }}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Email *</Text>
      <TextInput
        value={form.email}
        onChangeText={(t) => update({ email: t })}
        placeholder="<EMAIL>"
        keyboardType="email-address"
        autoCapitalize="none"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Téléphone *</Text>
      <TextInput
        value={form.telephone}
        onChangeText={(t) => update({ telephone: t })}
        placeholder="Téléphone"
        keyboardType="phone-pad"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Mot de passe (optionnel)</Text>
      <TextInput
        value={form.password}
        onChangeText={(t) => update({ password: t })}
        placeholder='Laisser vide pour "prof123" par défaut'
        secureTextEntry
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <TouchableOpacity
        onPress={handleSubmit}
        disabled={submitting}
        style={[styles.submitBtn, { opacity: submitting ? 0.7 : 1 }]}
        accessibilityRole="button"
        accessibilityLabel="Créer le professeur"
      >
        {submitting ? <ActivityIndicator color="#fff" /> : <Text style={styles.submitText}>Créer</Text>}
      </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 },
  headerWrap: { gap: 8, marginBottom: 12 },
  headerTopRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  actionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },

  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },

  label: { fontSize: 12, fontWeight: '700', marginTop: 8, marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
  submitBtn: { marginTop: 16, backgroundColor: '#2563EB', paddingVertical: 12, borderRadius: 12, alignItems: 'center' },
  submitText: { color: '#fff', fontWeight: '800' },
});
