import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, useColorScheme, ScrollView, Alert } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ApiServices } from '../../../../../src/services/apiService';
import Select from '../../../../../src/components/Select';
import type { Professeur } from '../../../../../src/types/api';

type Sexe = 'homme' | 'femme';

export default function AdminProfEditScreen() {
  const { matricule } = useLocalSearchParams<{ matricule: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [form, setForm] = useState<Partial<Professeur> & { password?: string }>({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const update = (patch: Partial<typeof form>) => setForm((f) => ({ ...f, ...patch }));

  const sexeOptions = useMemo(() => ([
    { label: 'Homme', value: 'homme' },
    { label: 'Femme', value: 'femme' },
  ]), []);

  const load = useCallback(async () => {
    if (!matricule) return;
    setError(null);
    setLoading(true);
    try {
      const list = await ApiServices.user().getProfesseurs();
      const found = list.find(p => String(p.matricule) === String(matricule));
      if (!found) throw new Error('Professeur introuvable');
      setForm({
        matricule: found.matricule,
        nom: found.nom,
        prenom: found.prenom,
        sexe: found.sexe as any,
        specialiste: (found as any).specialiste,
        email: found.email,
        telephone: found.telephone,
      });
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement');
    } finally {
      setLoading(false);
    }
  }, [matricule]);

  useEffect(() => {
    load();
  }, [load]);

  const handleSubmit = async () => {
    if (!matricule) return;
    setError(null);

    if (!form.nom || !form.prenom || !form.sexe || !(form as any).specialiste || !form.email || !form.telephone) {
      setError('Veuillez remplir les champs obligatoires.');
      return;
    }

    setSubmitting(true);
    try {
      const payload: Partial<Professeur> & { password?: string } = {
        nom: form.nom,
        prenom: form.prenom,
        sexe: form.sexe as any,
        // backend uses "specialiste"
        specialiste: (form as any).specialiste,
        email: form.email,
        telephone: form.telephone,
      } as any;

      if (form.password && form.password.trim().length > 0) {
        payload.password = form.password.trim();
      }

      await ApiServices.user().updateProfesseur(String(matricule), payload);
      router.replace({ pathname: '/app/admin/profs/[matricule]', params: { matricule: String(matricule) } } as any);
    } catch (e: any) {
      setError(e?.message || 'Échec de la mise à jour');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.center}>
        <ActivityIndicator size="large" color={isDark ? '#3B82F6' : '#2563EB'} />
        <Text style={{ color: isDark ? '#CBD5E1' : '#334155', marginTop: 8 }}>Chargement…</Text>
      </View>
    );
  }

  return (
    <ScrollView contentContainerStyle={{ padding: 16 }}>
      <View style={styles.headerWrap}>
        <View style={styles.headerTopRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Retour"
            style={[styles.backBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ fontWeight: '800', color: isDark ? '#E2E8F0' : '#0F172A' }}>← Retour</Text>
          </TouchableOpacity>
          <Text
            style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A', flexShrink: 1 }]}
            numberOfLines={1}
          >
            Modifier le professeur
          </Text>
        </View>
        <View style={styles.actionsRow}>
          <TouchableOpacity
            onPress={() => router.back()}
            accessibilityRole="button"
            accessibilityLabel="Annuler"
            style={[styles.secondaryBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
          >
            <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A', fontWeight: '800' }}>Annuler</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleSubmit}
            disabled={submitting}
            style={[styles.primaryBtn, { opacity: submitting ? 0.7 : 1 }]}
            accessibilityRole="button"
            accessibilityLabel="Enregistrer"
          >
            <Text style={{ color: '#fff', fontWeight: '800' }}>{submitting ? 'Enregistrement...' : 'Enregistrer'}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {error ? <Text style={{ color: '#ef4444', marginBottom: 12 }}>{error}</Text> : null}

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Matricule</Text>
      <TextInput
        value={String(form.matricule ?? '')}
        editable={false}
        style={[styles.input, { color: isDark ? '#94A3B8' : '#64748B', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)', backgroundColor: isDark ? '#0B1220' : '#F8FAFC' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nom *</Text>
      <TextInput
        value={form.nom ?? ''}
        onChangeText={(t) => update({ nom: t })}
        placeholder="Nom"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Prénom *</Text>
      <TextInput
        value={form.prenom ?? ''}
        onChangeText={(t) => update({ prenom: t })}
        placeholder="Prénom"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Select
        label="Sexe *"
        options={sexeOptions}
        value={(form.sexe as any as string) ?? ''}
        onValueChange={(value) => update({ sexe: value as any as Sexe })}
        placeholder="Sélectionner le sexe"
        style={{ marginBottom: 16 }}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Spécialité *</Text>
      <TextInput
        value={(form as any).specialiste ?? ''}
        onChangeText={(t) => update({ specialiste: t } as any)}
        placeholder="Spécialité"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Email *</Text>
      <TextInput
        value={form.email ?? ''}
        onChangeText={(t) => update({ email: t })}
        placeholder="<EMAIL>"
        keyboardType="email-address"
        autoCapitalize="none"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Téléphone *</Text>
      <TextInput
        value={form.telephone ?? ''}
        onChangeText={(t) => update({ telephone: t })}
        placeholder="Téléphone"
        keyboardType="phone-pad"
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>Nouveau mot de passe (optionnel)</Text>
      <TextInput
        value={form.password ?? ''}
        onChangeText={(t) => update({ password: t })}
        placeholder="Laisser vide pour ne pas modifier"
        secureTextEntry
        placeholderTextColor={isDark ? '#64748B' : '#94A3B8'}
        style={[styles.input, { color: isDark ? '#E2E8F0' : '#0F172A', borderColor: isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)' }]}
      />

      <TouchableOpacity
        onPress={handleSubmit}
        disabled={submitting}
        style={[styles.submitBtn, { opacity: submitting ? 0.7 : 1 }]}
        accessibilityRole="button"
        accessibilityLabel="Enregistrer"
      >
        {submitting ? <ActivityIndicator color="#fff" /> : <Text style={styles.submitText}>Enregistrer</Text>}
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  center: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16 },

  headerWrap: { gap: 8, marginBottom: 12 },
  headerTopRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 12 },
  title: { fontSize: 20, fontWeight: '800' },
  actionsRow: { flexDirection: 'row', flexWrap: 'wrap', gap: 8 },

  backBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent', minWidth: 80, alignItems: 'center' },
  secondaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12 },
  primaryBtn: { paddingVertical: 8, paddingHorizontal: 12, borderRadius: 12, backgroundColor: '#2563EB' },

  label: { fontSize: 12, fontWeight: '700', marginTop: 8, marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
  submitBtn: { marginTop: 16, backgroundColor: '#2563EB', paddingVertical: 12, borderRadius: 12, alignItems: 'center' },
  submitText: { color: '#fff', fontWeight: '800' },
});