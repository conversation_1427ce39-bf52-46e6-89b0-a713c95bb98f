import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../src/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { dashboardService } from '../../../src/services/dashboardService';
import type { AdminSummary, MessageSummary } from '../../../src/types/dashboard';

export default function AdminDashboard() {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const [summary, setSummary] = useState<AdminSummary | null>(null);
  const [messages, setMessages] = useState<MessageSummary[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const loadData = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const [sum, recents] = await Promise.all([
        dashboardService.getAdminSummary().catch(() => ({ classes: 0, profs: 0, eleves: 0, messages: 0 })),
        dashboardService.getRecentMessages(5).catch(() => []),
      ]);
      setSummary(sum);
      setMessages(recents);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement du tableau de bord');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadData();
    } finally {
      setRefreshing(false);
    }
  }, [loadData]);

  const handleRetry = () => loadData();

  const handleSignOut = async () => {
    await signOut();
    router.replace('/auth/welcome');
  };

  const renderMessage = ({ item }: { item: MessageSummary }) => (
    <View style={[styles.messageItem, { borderColor: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.06)' }]}>
      <Text style={[styles.messageTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>{item.title || 'Message'}</Text>
      <Text style={[styles.messageMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
        {item.from ? `De: ${item.from}` : 'De: N/A'} • {item.createdAt ? new Date(item.createdAt).toLocaleString() : 'Date: N/A'}
      </Text>
    </View>
  );

  return (
    <View style={styles.root}>
      <LinearGradient
        colors={isDark ? ['#0a0f1f', '#121a2f'] : ['#e9f1ff', '#ffffff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />
      <View style={[styles.dot, { top: 70, left: -20, backgroundColor: isDark ? '#1b2a5a' : '#cfe0ff' }]} />
      <View style={[styles.dot, { bottom: -30, right: -10, backgroundColor: isDark ? '#10243a' : '#ebf3ff', width: 200, height: 200 }]} />

      <SafeAreaView style={styles.safe}>
        <View style={styles.container}>
          <View style={styles.headerRow}>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Admin Dashboard</Text>
            <TouchableOpacity
              onPress={handleSignOut}
              style={[styles.signoutBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
              accessibilityRole="button"
              accessibilityLabel="Se déconnecter"
            >
              <Text style={[styles.signoutText, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Se déconnecter</Text>
            </TouchableOpacity>
          </View>

          <BlurView intensity={isDark ? 40 : 20} tint={isDark ? 'dark' : 'light'} style={styles.card}>
            {loading ? (
              <View style={styles.centerBox}>
                <ActivityIndicator />
                <Text style={[styles.info, { color: isDark ? '#CBD5E1' : '#334155' }]}>Chargement...</Text>
              </View>
            ) : error ? (
              <View style={styles.centerBox}>
                <Text style={[styles.errorText, { color: '#F87171' }]}>{error}</Text>
                <TouchableOpacity
                  style={styles.retryBtn}
                  onPress={handleRetry}
                  accessibilityRole="button"
                  accessibilityLabel="Réessayer"
                >
                  <LinearGradient
                    colors={['#2563EB', '#1D4ED8']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.retryBtnBg}
                  />
                  <Text style={styles.retryText}>Réessayer</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                ListHeaderComponent={
                  <View>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                      Matricule: {user?.matricule ?? 'unknown'}
                    </Text>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                      Role: {user?.role ?? 'unknown'}
                    </Text>

                    <View style={styles.cardsRow}>
                      <View style={[styles.statCard, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                        <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>Classes</Text>
                        <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{summary?.classes ?? 0}</Text>
                      </View>
                      <View style={[styles.statCard, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                        <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>Profs</Text>
                        <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{summary?.profs ?? 0}</Text>
                      </View>
                    </View>
                    <View style={styles.cardsRow}>
                      <View style={[styles.statCard, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                        <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>Élèves</Text>
                        <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{summary?.eleves ?? 0}</Text>
                      </View>
                      <View style={[styles.statCard, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}>
                        <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>Messages</Text>
                        <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>{summary?.messages ?? 0}</Text>
                      </View>
                    </View>
 
                    {/* Admin actions */}
                    <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Fonctions admin</Text>
                    <View style={styles.actionsRow}>
                      <TouchableOpacity
                        onPress={() => router.push({ pathname: '/app/admin/salles' })}
                        style={[styles.actionTile, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}
                        accessibilityRole="button"
                        accessibilityLabel="Aller à la gestion des salles"
                      >
                        <Text style={[styles.actionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Salles</Text>
                        <Text style={[styles.actionSubtitle, { color: isDark ? '#94A3B8' : '#64748B' }]}>Gérer les salles</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => router.push({ pathname: '/app/admin/eleves' } as any)}
                        style={[styles.actionTile, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}
                        accessibilityRole="button"
                        accessibilityLabel="Aller à la gestion des élèves"
                      >
                        <Text style={[styles.actionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Élèves</Text>
                        <Text style={[styles.actionSubtitle, { color: isDark ? '#94A3B8' : '#64748B' }]}>Gérer les élèves</Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        onPress={() => router.push({ pathname: '/app/admin/profs' } as any)}
                        style={[styles.actionTile, { borderColor: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)', backgroundColor: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)' }]}
                        accessibilityRole="button"
                        accessibilityLabel="Aller à la gestion des professeurs"
                      >
                        <Text style={[styles.actionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Professeurs</Text>
                        <Text style={[styles.actionSubtitle, { color: isDark ? '#94A3B8' : '#64748B' }]}>Gérer les professeurs</Text>
                      </TouchableOpacity>
                    </View>
 
                    <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Derniers messages</Text>
                 </View>
               }
               data={messages.slice(0, 5)}
                keyExtractor={(item) => String(item.id)}
                renderItem={renderMessage}
                ItemSeparatorComponent={() => <View style={[styles.separator, { backgroundColor: isDark ? 'rgba(148,163,184,0.12)' : '#e5e7eb' }]} />}
                refreshControl={
                  <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
                contentContainerStyle={messages.length === 0 ? { flexGrow: 1 } : undefined}
                ListEmptyComponent={
                  <View style={styles.centerBox}>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>Aucun message récent</Text>
                  </View>
                }
              />
            )}
          </BlurView>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  root: { flex: 1 },
  safe: { flex: 1 },
  container: { flex: 1, paddingHorizontal: 16, paddingTop: 8 },
  dot: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 999,
    opacity: 0.6,
    filter: 'blur(12px)' as any,
  },
  headerRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 },
  title: { fontSize: 22, fontWeight: '800', letterSpacing: 0.2 },
  signoutBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  signoutText: { fontSize: 13, fontWeight: '800', letterSpacing: 0.2 },
  card: {
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.18)',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 10 },
    elevation: 6,
    gap: 12,
  },
  centerBox: { alignItems: 'center', justifyContent: 'center', paddingVertical: 24 },
  info: { fontSize: 14 },
  errorText: { textAlign: 'center', marginBottom: 12 },
  retryBtn: { overflow: 'hidden', borderRadius: 12, alignItems: 'center', justifyContent: 'center' },
  retryBtnBg: { ...StyleSheet.absoluteFillObject, borderRadius: 12, height: 40, width: 140 },
  retryText: { color: '#fff', fontWeight: '800', letterSpacing: 0.2, paddingVertical: 10, paddingHorizontal: 16 },
  cardsRow: { flexDirection: 'row', gap: 12, marginTop: 12 },
  statCard: {
    flex: 1,
    padding: 14,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
  },
  cardLabel: { fontSize: 12, fontWeight: '700' },
  cardValue: { fontSize: 22, fontWeight: '800', marginTop: 4 },
  sectionTitle: { fontSize: 18, fontWeight: '800', marginTop: 16, marginBottom: 8, letterSpacing: 0.2 },
  messageItem: { paddingVertical: 10, borderBottomWidth: 1 },
  messageTitle: { fontSize: 14, fontWeight: '800' },
  messageMeta: { fontSize: 12, marginTop: 2 },
  separator: { height: 1 },
  actionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 8,
  },
  actionTile: {
    flexGrow: 0,
    flexShrink: 0,
    flexBasis: '48%', // roughly half width to ensure at most 2 per row with gap
    minWidth: '48%',
    padding: 14,
    borderRadius: 16,
    borderWidth: 1,
  },
  actionTitle: { fontSize: 16, fontWeight: '800' },
  actionSubtitle: { fontSize: 12, marginTop: 4 },
});