import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  View,
  Text,
  Button,
  StyleSheet,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  TouchableOpacity,
  useColorScheme,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../../src/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { dashboardService } from '../../../src/services/dashboardService';
import type { TimetableEntry, NoteSummary, AnnonceSummary } from '../../../src/types/dashboard';

export default function EleveDashboard() {
  const { user, signOut } = useAuth();
  const router = useRouter();

  const [timetable, setTimetable] = useState<TimetableEntry[]>([]);
  const [notes, setNotes] = useState<NoteSummary[]>([]);
  const [annonces, setAnnonces] = useState<AnnonceSummary[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const isDark = useColorScheme() === 'dark';

  const loadData = useCallback(async () => {
    setError(null);
    setLoading(true);
    try {
      const [tt, nt, an] = await Promise.all([
        dashboardService.getEleveTodayEmploiTemps().catch(() => []),
        dashboardService.getEleveLatestNotes(3).catch(() => []),
        dashboardService.getEleveLatestAnnonces(3).catch(() => []),
      ]);
      setTimetable(tt);
      setNotes(nt);
      setAnnonces(an);
    } catch (e: any) {
      setError(e?.message || 'Erreur lors du chargement du tableau de bord');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadData();
    } finally {
      setRefreshing(false);
    }
  }, [loadData]);

  const handleRetry = () => loadData();

  const handleSignOut = async () => {
    await signOut();
    router.replace('/auth/welcome');
  };

  const firstClass = useMemo(() => {
    if (!timetable.length) return null;
    const toMinutes = (t: string) => {
      const [h, m] = (t || '00:00').split(':').map(Number);
      return h * 60 + (m || 0);
    };
    const sorted = [...timetable].sort((a, b) => toMinutes(a.start) - toMinutes(b.start));
    return sorted[0];
  }, [timetable]);

  const renderTimetable = ({ item }: { item: TimetableEntry }) => (
    <View style={[styles.item, { borderColor: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.06)' }]}>
      <Text style={[styles.itemTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>{item.subject || 'Cours'}</Text>
      <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
        {item.start || 'N/A'} - {item.end || 'N/A'} {item.room ? `• Salle ${item.room}` : ''}
      </Text>
    </View>
  );

  const renderNote = ({ item }: { item: NoteSummary }) => (
    <View style={[styles.item, { borderColor: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.06)' }]}>
      <Text style={[styles.itemTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>{item.subject || 'Note'}</Text>
      <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>
        {typeof item.value === 'number' ? item.value : 'N/A'} {item.coef ? `(coef ${item.coef})` : ''} • {item.createdAt ? new Date(item.createdAt).toLocaleDateString() : 'Date: N/A'}
      </Text>
    </View>
  );

  const renderAnnonce = ({ item }: { item: AnnonceSummary }) => (
    <View style={[styles.item, { borderColor: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.06)' }]}>
      <Text style={[styles.itemTitle, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>{item.title || 'Annonce'}</Text>
      <Text style={[styles.itemMeta, { color: isDark ? '#94A3B8' : '#64748B' }]}>{item.createdAt ? new Date(item.createdAt).toLocaleString() : 'Date: N/A'}</Text>
    </View>
  );

  return (
    <View style={styles.root}>
      <LinearGradient
        colors={isDark ? ['#0a0f1f', '#121a2f'] : ['#e9f1ff', '#ffffff']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />
      <View style={[styles.dot, { top: 70, left: -20, backgroundColor: isDark ? '#1b2a5a' : '#cfe0ff' }]} />
      <View style={[styles.dot, { bottom: -30, right: -10, backgroundColor: isDark ? '#10243a' : '#ebf3ff', width: 200, height: 200 }]} />

      <SafeAreaView style={styles.safe}>
        <View style={styles.container}>
          <View style={styles.headerRow}>
            <Text style={[styles.title, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Élève Dashboard</Text>
            <TouchableOpacity
              onPress={handleSignOut}
              style={[styles.signoutBtn, { borderColor: isDark ? 'rgba(148,163,184,0.35)' : 'rgba(15,23,42,0.12)' }]}
              accessibilityRole="button"
              accessibilityLabel="Se déconnecter"
            >
              <Text style={[styles.signoutText, { color: isDark ? '#E2E8F0' : '#0F172A' }]}>Se déconnecter</Text>
            </TouchableOpacity>
          </View>

          <BlurView intensity={isDark ? 40 : 20} tint={isDark ? 'dark' : 'light'} style={styles.card}>
            {loading ? (
              <View style={styles.centerBox}>
                <ActivityIndicator />
                <Text style={[styles.info, { color: isDark ? '#CBD5E1' : '#334155' }]}>Chargement...</Text>
              </View>
            ) : error ? (
              <View style={styles.centerBox}>
                <Text style={[styles.errorText, { color: '#F87171' }]}>{error}</Text>
                <TouchableOpacity
                  style={styles.retryBtn}
                  onPress={handleRetry}
                  accessibilityRole="button"
                  accessibilityLabel="Réessayer"
                >
                  <LinearGradient
                    colors={['#2563EB', '#1D4ED8']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.retryBtnBg}
                  />
                  <Text style={styles.retryText}>Réessayer</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                ListHeaderComponent={
                  <View>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                      Matricule: {user?.matricule ?? 'unknown'}
                    </Text>
                    <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>
                      Role: {user?.role ?? 'unknown'}
                    </Text>

                    <View style={styles.statCard}>
                      <Text style={[styles.cardLabel, { color: isDark ? '#CBD5E1' : '#334155' }]}>
                        Premier cours aujourd'hui
                      </Text>
                      <Text style={[styles.cardValue, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>
                        {firstClass
                          ? `${firstClass.subject || 'Cours'} • ${firstClass.start || 'N/A'} - ${firstClass.end || 'N/A'}${firstClass.room ? ` • Salle ${firstClass.room}` : ''}`
                          : 'N/A'}
                      </Text>
                    </View>

                    <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Emploi du temps (prochains)</Text>
                  </View>
                }
                data={timetable.slice(0, 3)}
                keyExtractor={(item) => String(item.id)}
                renderItem={renderTimetable}
                ItemSeparatorComponent={() => <View style={[styles.separator, { backgroundColor: isDark ? 'rgba(148,163,184,0.12)' : '#e5e7eb' }]} />}
                refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
                contentContainerStyle={{ paddingBottom: 16 }}
                ListFooterComponent={
                  <View>
                    <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Dernières notes</Text>
                    {notes.length === 0 ? (
                      <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>Aucune note</Text>
                    ) : (
                      <FlatList
                        data={notes}
                        keyExtractor={(item) => `note-${item.id}`}
                        renderItem={renderNote}
                        ItemSeparatorComponent={() => <View style={[styles.separator, { backgroundColor: isDark ? 'rgba(148,163,184,0.12)' : '#e5e7eb' }]} />}
                      />
                    )}

                    <Text style={[styles.sectionTitle, { color: isDark ? '#F8FAFC' : '#0F172A' }]}>Dernières annonces</Text>
                    {annonces.length === 0 ? (
                      <Text style={[styles.info, { color: isDark ? '#94A3B8' : '#64748B' }]}>Aucune annonce</Text>
                    ) : (
                      <FlatList
                        data={annonces}
                        keyExtractor={(item) => `ann-${item.id}`}
                        renderItem={renderAnnonce}
                        ItemSeparatorComponent={() => <View style={[styles.separator, { backgroundColor: isDark ? 'rgba(148,163,184,0.12)' : '#e5e7eb' }]} />}
                      />
                    )}
                  </View>
                }
              />
            )}
          </BlurView>
        </View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  root: { flex: 1 },
  safe: { flex: 1 },
  container: { flex: 1, paddingHorizontal: 16, paddingTop: 8 },
  dot: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 999,
    opacity: 0.6,
    filter: 'blur(12px)' as any,
  },
  headerRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 },
  title: { fontSize: 22, fontWeight: '800', letterSpacing: 0.2 },
  signoutBtn: { paddingVertical: 8, paddingHorizontal: 12, borderWidth: 1, borderRadius: 12, backgroundColor: 'transparent' },
  signoutText: { fontSize: 13, fontWeight: '800', letterSpacing: 0.2 },
  card: {
    borderRadius: 24,
    paddingVertical: 16,
    paddingHorizontal: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(148,163,184,0.18)',
    shadowColor: '#000',
    shadowOpacity: 0.18,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 10 },
    elevation: 6,
    gap: 12,
  },
  centerBox: { alignItems: 'center', justifyContent: 'center', paddingVertical: 24 },
  info: { fontSize: 14 },
  errorText: { textAlign: 'center', marginBottom: 12 },
  retryBtn: { overflow: 'hidden', borderRadius: 12, alignItems: 'center', justifyContent: 'center' },
  retryBtnBg: { ...StyleSheet.absoluteFillObject, borderRadius: 12, height: 40, width: 140 },
  retryText: { color: '#fff', fontWeight: '800', letterSpacing: 0.2, paddingVertical: 10, paddingHorizontal: 16 },
  statCard: {
    padding: 14,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center',
    backgroundColor: 'transparent',
    borderColor: 'rgba(148,163,184,0.18)',
    marginTop: 12,
  },
  cardLabel: { fontSize: 12, fontWeight: '700' },
  cardValue: { fontSize: 16, fontWeight: '800', marginTop: 4 },
  sectionTitle: { fontSize: 18, fontWeight: '800', marginTop: 16, marginBottom: 8, letterSpacing: 0.2 },
  item: { paddingVertical: 10, borderBottomWidth: 1 },
  itemTitle: { fontSize: 14, fontWeight: '800' },
  itemMeta: { fontSize: 12, marginTop: 2 },
  separator: { height: 1 },
});