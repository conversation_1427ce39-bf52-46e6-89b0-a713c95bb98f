import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useAuth } from '../../src/contexts/AuthContext';

export default function ProtectedHome() {
  const { user, signOut, loading } = useAuth();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Authenticated</Text>
      <Text style={styles.subtitle}>role: {user?.role ?? 'unknown'}</Text>
      <Text style={styles.subtitle}>matricule: {user?.matricule ?? 'unknown'}</Text>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        disabled={loading}
        onPress={signOut}
        accessibilityRole="button"
        accessibilityLabel="Sign out"
      >
        <Text style={styles.buttonText}>{loading ? 'Signing out...' : 'Sign out'}</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 24, alignItems: 'center', justifyContent: 'center', backgroundColor: '#fff' },
  title: { fontSize: 24, fontWeight: '700', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#444', marginBottom: 16 },
  button: { backgroundColor: '#dc3545', paddingVertical: 12, paddingHorizontal: 16, borderRadius: 8 },
  buttonDisabled: { backgroundColor: '#f199a1' },
  buttonText: { color: '#fff', fontWeight: '700' },
});