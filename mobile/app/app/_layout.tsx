import { Stack, useRouter } from 'expo-router';
import { useEffect } from 'react';
import { useAuth } from '../../src/contexts/AuthContext';
import { View, Text } from 'react-native';

export default function AppRoleRouterLayout() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (loading) return;

    const role = user?.role;

    if (role === 'admin') {
      router.replace('/app/admin/dashboard');
    } else if (role === 'professeur') {
      router.replace('/app/professeur/dashboard');
    } else if (role === 'eleve') {
      router.replace('/app/eleve/dashboard');
    } else {
      // Authenticated but role missing/unknown: stay in /app and show safe placeholder
      // Do not crash; do not redirect to auth here (higher-level guard handles auth state)
    }
  }, [loading, user, router]);

  return (
    <>
      <Stack screenOptions={{ headerShown: true }}>
        <Stack.Screen name="index" options={{ headerShown: false, title: 'App' }} />
        <Stack.Screen name="admin/dashboard" options={{ title: 'Admin Dashboard' }} />
        <Stack.Screen name="professeur/dashboard" options={{ title: 'Professeur Dashboard' }} />
        <Stack.Screen name="eleve/dashboard" options={{ title: 'Eleve Dashboard' }} />
      </Stack>

      {!loading && !user?.role && (
        <View style={{ padding: 16 }}>
          <Text style={{ fontSize: 16 }}>No role assigned. Please contact support.</Text>
        </View>
      )}
    </>
  );
}