import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import { useEffect, useState } from 'react';
import { View, Text, ActivityIndicator } from 'react-native';

import { useColorScheme } from '@/hooks/useColorScheme';
import { initializeApiServices } from '../src/services';
import { AuthProvider } from '../src/contexts/AuthContext';



export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const [servicesInitialized, setServicesInitialized] = useState(false);

  // Initialize API services when app starts
  useEffect(() => {
    try {
      initializeApiServices();
      setServicesInitialized(true);
    } catch (error) {
      console.error('Failed to initialize services:', error);
      // Still set to true to prevent infinite loading
      setServicesInitialized(true);
    }
  }, []);

  if (!loaded || !servicesInitialized) {
    // Show loading screen while fonts and services are loading
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 10 }}>Loading...</Text>
      </View>
    );
  }

  return (
    <AuthProvider>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>

        <Stack screenOptions={{ headerShown: false }}>
          {/* Root index route */}
          <Stack.Screen name="index" options={{ headerShown: false }} />

          {/* Public auth routes */}
          <Stack.Screen name="auth/welcome" options={{ headerShown: false }} />
          <Stack.Screen name="auth/login-ad" options={{ headerShown: true, title: 'Admin Login' }} />
          <Stack.Screen name="auth/login-user" options={{ headerShown: true, title: 'User Login' }} />

          {/* Protected app routes */}
          <Stack.Screen name="app" options={{ headerShown: false }} />

          {/* Existing routes (kept) */}
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>

        <StatusBar style="auto" />
      </ThemeProvider>
    </AuthProvider>
  );
}
