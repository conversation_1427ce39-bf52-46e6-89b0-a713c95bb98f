# NS School Manager Mobile App Environment Configuration
# Copy this file to .env and update the values for your environment

# API Configuration (EXPO_PUBLIC_ prefix required for client-side access)
EXPO_PUBLIC_API_BASE_URL=http://localhost:8080
EXPO_PUBLIC_API_TIMEOUT=30000

# App Configuration
EXPO_PUBLIC_APP_NAME=NS School Manager
EXPO_PUBLIC_APP_VERSION=1.0.0

# Feature Flags
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_ENABLE_LOGGING=true
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=false

# Production Example:
# EXPO_PUBLIC_API_BASE_URL=https://your-production-api.com
# EXPO_PUBLIC_DEBUG_MODE=false
# EXPO_PUBLIC_ENABLE_LOGGING=false
