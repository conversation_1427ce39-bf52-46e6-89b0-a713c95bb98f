/**
 * Logger Utility for NS School Manager Mobile App
 * Professional logging with different levels and formatting
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

interface LogEntry {
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  source?: string;
}

class LoggerService {
  private static instance: LoggerService;
  private logLevel: LogLevel = __DEV__ ? LogLevel.DEBUG : LogLevel.INFO;
  private logs: LogEntry[] = [];
  private maxLogs = 1000; // Keep last 1000 logs in memory

  private constructor() {}

  static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService();
    }
    return LoggerService.instance;
  }

  /**
   * Set minimum log level
   */
  setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  /**
   * Get current log level
   */
  getLogLevel(): LogLevel {
    return this.logLevel;
  }

  /**
   * Debug level logging
   */
  debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  /**
   * Info level logging
   */
  info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  /**
   * Warning level logging
   */
  warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  /**
   * Error level logging
   */
  error(message: string, error?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, error, source);
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    if (level < this.logLevel) {
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      data,
      timestamp: new Date(),
      source,
    };

    // Add to memory logs
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift(); // Remove oldest log
    }

    // Console output
    this.outputToConsole(logEntry);
  }

  /**
   * Output log entry to console with formatting
   */
  private outputToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const levelName = LogLevel[entry.level];
    const source = entry.source ? `[${entry.source}]` : '';
    const prefix = `${timestamp} ${levelName}${source}:`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        if (entry.data) {
          console.log(`🔍 ${prefix}`, entry.message, entry.data);
        } else {
          console.log(`🔍 ${prefix}`, entry.message);
        }
        break;

      case LogLevel.INFO:
        if (entry.data) {
          console.info(`ℹ️ ${prefix}`, entry.message, entry.data);
        } else {
          console.info(`ℹ️ ${prefix}`, entry.message);
        }
        break;

      case LogLevel.WARN:
        if (entry.data) {
          console.warn(`⚠️ ${prefix}`, entry.message, entry.data);
        } else {
          console.warn(`⚠️ ${prefix}`, entry.message);
        }
        break;

      case LogLevel.ERROR:
        if (entry.data) {
          console.error(`❌ ${prefix}`, entry.message, entry.data);
        } else {
          console.error(`❌ ${prefix}`, entry.message);
        }
        break;
    }
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count: number = 100): LogEntry[] {
    return this.logs.slice(-count);
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Get logs by source
   */
  getLogsBySource(source: string): LogEntry[] {
    return this.logs.filter(log => log.source === source);
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
  }

  /**
   * Export logs as JSON string
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Get logs count by level
   */
  getLogStats(): Record<string, number> {
    const stats: Record<string, number> = {
      DEBUG: 0,
      INFO: 0,
      WARN: 0,
      ERROR: 0,
    };

    this.logs.forEach(log => {
      const levelName = LogLevel[log.level];
      stats[levelName]++;
    });

    return stats;
  }

  /**
   * Performance timing utility
   */
  time(label: string): void {
    console.time(`⏱️ ${label}`);
  }

  /**
   * End performance timing
   */
  timeEnd(label: string): void {
    console.timeEnd(`⏱️ ${label}`);
  }

  /**
   * Group logs
   */
  group(label: string): void {
    console.group(`📁 ${label}`);
  }

  /**
   * End log group
   */
  groupEnd(): void {
    console.groupEnd();
  }

  /**
   * Log table data
   */
  table(data: any): void {
    console.table(data);
  }
}

// Export singleton instance
export const Logger = LoggerService.getInstance();

// Export convenience functions
export const log = {
  debug: (message: string, data?: any, source?: string) => Logger.debug(message, data, source),
  info: (message: string, data?: any, source?: string) => Logger.info(message, data, source),
  warn: (message: string, data?: any, source?: string) => Logger.warn(message, data, source),
  error: (message: string, error?: any, source?: string) => Logger.error(message, error, source),
  time: (label: string) => Logger.time(label),
  timeEnd: (label: string) => Logger.timeEnd(label),
  group: (label: string) => Logger.group(label),
  groupEnd: () => Logger.groupEnd(),
  table: (data: any) => Logger.table(data),
};

// Export types
export type { LogEntry };
export { LoggerService };
