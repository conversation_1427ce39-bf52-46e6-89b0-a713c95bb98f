/**
 * Error Handler Utility for NS School Manager Mobile App
 * Centralized error handling and user-friendly error messages
 */

import { Alert } from 'react-native';
import { NetworkError, ApiErrorCode } from '../types/api';
import { ERROR_MESSAGES } from '../config/api';
import { Logger } from './logger';

/**
 * Error handler class for managing different types of errors
 */
export class ErrorHandler {
  /**
   * Handle network errors with user-friendly messages
   */
  static handleNetworkError(error: NetworkError, showAlert: boolean = true): string {
    let userMessage: string;

    switch (error.code) {
      case ApiErrorCode.NETWORK_ERROR:
        userMessage = ERROR_MESSAGES.NETWORK_ERROR;
        break;
      case ApiErrorCode.TIMEOUT_ERROR:
        userMessage = ERROR_MESSAGES.TIMEOUT_ERROR;
        break;
      case ApiErrorCode.AUTHENTICATION_ERROR:
        userMessage = ERROR_MESSAGES.AUTHENTICATION_ERROR;
        break;
      case ApiErrorCode.AUTHORIZATION_ERROR:
        userMessage = ERROR_MESSAGES.AUTHORIZATION_ERROR;
        break;
      case ApiErrorCode.VALIDATION_ERROR:
        userMessage = ERROR_MESSAGES.VALIDATION_ERROR;
        break;
      case ApiErrorCode.NOT_FOUND:
        userMessage = ERROR_MESSAGES.NOT_FOUND;
        break;
      case ApiErrorCode.SERVER_ERROR:
        userMessage = ERROR_MESSAGES.SERVER_ERROR;
        break;
      default:
        userMessage = ERROR_MESSAGES.UNKNOWN_ERROR;
    }

    Logger.error('Network error handled', {
      code: error.code,
      status: error.status,
      message: error.message,
      userMessage,
    });

    if (showAlert) {
      this.showErrorAlert('Erreur', userMessage);
    }

    return userMessage;
  }

  /**
   * Handle general errors
   */
  static handleError(error: any, context?: string, showAlert: boolean = true): string {
    let userMessage: string;

    if (error instanceof Error && 'code' in error) {
      // Handle NetworkError
      return this.handleNetworkError(error as NetworkError, showAlert);
    }

    // Handle other types of errors
    if (error?.response?.data?.message) {
      userMessage = error.response.data.message;
    } else if (error?.message) {
      userMessage = error.message;
    } else {
      userMessage = ERROR_MESSAGES.UNKNOWN_ERROR;
    }

    Logger.error('General error handled', {
      error,
      context,
      userMessage,
    });

    if (showAlert) {
      this.showErrorAlert('Erreur', userMessage);
    }

    return userMessage;
  }

  /**
   * Show error alert to user
   */
  static showErrorAlert(title: string, message: string, onPress?: () => void): void {
    Alert.alert(
      title,
      message,
      [
        {
          text: 'OK',
          onPress,
        },
      ],
      { cancelable: false }
    );
  }

  /**
   * Show confirmation alert
   */
  static showConfirmAlert(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ): void {
    Alert.alert(
      title,
      message,
      [
        {
          text: 'Annuler',
          style: 'cancel',
          onPress: onCancel,
        },
        {
          text: 'Confirmer',
          onPress: onConfirm,
        },
      ],
      { cancelable: false }
    );
  }

  /**
   * Show success alert
   */
  static showSuccessAlert(title: string, message: string, onPress?: () => void): void {
    Alert.alert(
      title,
      message,
      [
        {
          text: 'OK',
          onPress,
        },
      ],
      { cancelable: false }
    );
  }

  /**
   * Handle authentication errors specifically
   */
  static handleAuthError(error: any, onAuthRequired?: () => void): void {
    const userMessage = this.handleError(error, 'Authentication', false);
    
    if (error?.code === ApiErrorCode.AUTHENTICATION_ERROR) {
      Alert.alert(
        'Session expirée',
        'Votre session a expiré. Veuillez vous reconnecter.',
        [
          {
            text: 'Se reconnecter',
            onPress: onAuthRequired,
          },
        ],
        { cancelable: false }
      );
    } else {
      this.showErrorAlert('Erreur d\'authentification', userMessage);
    }
  }

  /**
   * Handle validation errors with field-specific messages
   */
  static handleValidationError(error: any): Record<string, string> {
    const fieldErrors: Record<string, string> = {};

    if (error?.response?.data?.details) {
      const details = error.response.data.details;
      
      if (Array.isArray(details)) {
        details.forEach((detail: any) => {
          if (detail.field && detail.message) {
            fieldErrors[detail.field] = detail.message;
          }
        });
      } else if (typeof details === 'object') {
        Object.keys(details).forEach(field => {
          fieldErrors[field] = details[field];
        });
      }
    }

    Logger.error('Validation error handled', { fieldErrors });
    return fieldErrors;
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(error: any): boolean {
    if (error instanceof Error && 'isRetryable' in error) {
      return (error as NetworkError).isRetryable;
    }

    // Check for specific error codes that are retryable
    const retryableCodes = [
      ApiErrorCode.NETWORK_ERROR,
      ApiErrorCode.TIMEOUT_ERROR,
      ApiErrorCode.SERVER_ERROR,
    ];

    return retryableCodes.includes(error?.code);
  }

  /**
   * Get retry delay based on attempt number
   */
  static getRetryDelay(attempt: number, baseDelay: number = 1000): number {
    return Math.min(baseDelay * Math.pow(2, attempt), 10000); // Max 10 seconds
  }

  /**
   * Format error for logging
   */
  static formatErrorForLogging(error: any, context?: string): object {
    return {
      context,
      message: error?.message || 'Unknown error',
      code: error?.code,
      status: error?.status,
      stack: error?.stack,
      response: error?.response?.data,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Create user-friendly error message from API response
   */
  static createUserMessage(error: any): string {
    // Try to extract user-friendly message from API response
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }

    // Try to extract from error object
    if (error?.message) {
      return error.message;
    }

    // Fallback to generic message
    return ERROR_MESSAGES.UNKNOWN_ERROR;
  }
}

/**
 * Utility functions for common error handling patterns
 */
export const handleApiError = (error: any, context?: string, showAlert: boolean = true): string => {
  return ErrorHandler.handleError(error, context, showAlert);
};

export const handleAuthError = (error: any, onAuthRequired?: () => void): void => {
  ErrorHandler.handleAuthError(error, onAuthRequired);
};

export const showErrorAlert = (title: string, message: string, onPress?: () => void): void => {
  ErrorHandler.showErrorAlert(title, message, onPress);
};

export const showSuccessAlert = (title: string, message: string, onPress?: () => void): void => {
  ErrorHandler.showSuccessAlert(title, message, onPress);
};

export const showConfirmAlert = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
): void => {
  ErrorHandler.showConfirmAlert(title, message, onConfirm, onCancel);
};

// Export the main class
export default ErrorHandler;
