/**
 * Authentication Context for NS School Manager Mobile App
 * Global state management for user authentication with secure token storage
 * and hydration using unified endpoints.
 */

import React, { createContext, useContext, useEffect, useMemo, useState, ReactNode, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from '../services/authService';
import { TokenManager } from '../services/tokenManager';
import { API_ENDPOINTS } from '../config/api';
import { getHttpClient } from '../services/httpClient';
import { Logger } from '../utils/logger';
import type { User, UserRole, LoginResponse, AuthStatusResponse } from '../types/api';

type AuthMethod = 'jwt' | 'session' | null;

export interface AuthState {
  user: { matricule: string; role: 'admin' | 'professeur' | 'eleve' } | null;
  accessToken: string | null;
  refreshToken: string | null;
  authMethod: AuthMethod;
  loading: boolean;
}

interface SignInStudentTeacherArgs {
  matricule: string;
  password: string;
  role: 'eleve' | 'professeur';
  remember?: boolean;
}

interface SignInAdminArgs {
  matricule: string;
  password: string;
  remember?: boolean;
}

interface AuthContextType extends AuthState {
  signInStudentTeacher: (args: SignInStudentTeacherArgs) => Promise<{ ok: boolean; message?: string }>;
  signInAdmin: (args: SignInAdminArgs) => Promise<{ ok: boolean; message?: string }>;
  signOut: () => Promise<void>;
  restoreSession: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
}

const USER_STORAGE_KEY = 'ns_user';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const authService = useMemo(() => AuthService.getInstance(), []);
  const tokenManager = useMemo(() => TokenManager.getInstance(), []);
  const [state, setState] = useState<AuthState>({
    user: null,
    accessToken: null,
    refreshToken: null,
    authMethod: null,
    loading: true,
  });

  const setLoading = (loading: boolean) => setState((s) => ({ ...s, loading }));

  const persistUser = async (user: User | null) => {
    try {
      if (user) {
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
      } else {
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
      }
    } catch (e) {
      Logger.warn('Failed to persist user in AsyncStorage', e);
    }
  };

  const loadPersistedUser = async (): Promise<User | null> => {
    try {
      const raw = await AsyncStorage.getItem(USER_STORAGE_KEY);
      if (!raw) return null;
      return JSON.parse(raw) as User;
    } catch (e) {
      Logger.warn('Failed to load user from AsyncStorage', e);
      return null;
    }
  };

  const updateTokensInState = async () => {
    const tokens = await tokenManager.getAllTokens();
    setState((s) => ({
      ...s,
      accessToken: tokens?.accessToken ?? null,
      refreshToken: tokens?.refreshToken ?? null,
    }));
  };

  const rehydrateViaStatus = useCallback(async (): Promise<{ user: User | null; method: AuthMethod }> => {
    try {
      const http = getHttpClient();
      const status = await http.get<AuthStatusResponse>(API_ENDPOINTS.AUTH.STATUS);
      if (status.authenticated) {
        if (status.user) {
          await persistUser(status.user);
          return { user: status.user, method: status.authMethod ?? 'jwt' };
        }
        // If server says authenticated but no user payload, fallback to local user
        const local = await loadPersistedUser();
        return { user: local, method: status.authMethod ?? 'jwt' };
      }
      return { user: null, method: null };
    } catch (e) {
      Logger.warn('Status rehydration failed', e);
      // Fallback to local only
      const local = await loadPersistedUser();
      return { user: local, method: null };
    }
  }, []);

  const restoreSession = useCallback(async () => {
    setLoading(true);
    try {
      const hasTokens = await tokenManager.hasValidTokens();
      if (!hasTokens) {
        await authService.clearLocalAuth();
        setState((s) => ({ ...s, user: null, accessToken: null, refreshToken: null, authMethod: null, loading: false }));
        return;
      }

      // Update tokens in local state
      await updateTokensInState();

      // Try server status to rehydrate user/role reliably
      const { user, method } = await rehydrateViaStatus();
      await persistUser(user);

      setState((s) => ({
        ...s,
        user: user ? { matricule: user.matricule, role: user.role } : null,
        authMethod: method,
        loading: false,
      }));
    } catch (e) {
      Logger.error('restoreSession failed', e);
      await authService.clearLocalAuth();
      setState((s) => ({ ...s, user: null, accessToken: null, refreshToken: null, authMethod: null, loading: false }));
    }
  }, [authService, rehydrateViaStatus, tokenManager]);

  const signInStudentTeacher: AuthContextType['signInStudentTeacher'] = async ({ matricule, password, role, remember = false }) => {
    setLoading(true);
    try {
      const resp: LoginResponse = await authService.login(matricule, password, role as UserRole, remember);
      if (resp.success && resp.user && resp.tokens) {
        await persistUser(resp.user);
        await updateTokensInState();
        setState((s) => ({
          ...s,
          user: { matricule: resp.user!.matricule, role: resp.user!.role },
          authMethod: resp.authMethod ?? 'jwt',
          loading: false,
        }));
        return { ok: true };
      }
      setLoading(false);
      return { ok: false, message: resp.message || 'Login failed' };
    } catch (e: any) {
      Logger.error('signInStudentTeacher error', e);
      setLoading(false);
      const message = e?.response?.data?.message || e?.message || 'Login failed';
      return { ok: false, message };
    }
  };

  const signInAdmin: AuthContextType['signInAdmin'] = async ({ matricule, password, remember = false }) => {
    setLoading(true);
    try {
      const resp: LoginResponse = await authService.loginAdmin(matricule, password, remember);
      if (resp.success && resp.user && resp.tokens) {
        await persistUser(resp.user);
        await updateTokensInState();
        setState((s) => ({
          ...s,
          user: { matricule: resp.user!.matricule, role: resp.user!.role },
          authMethod: resp.authMethod ?? 'jwt',
          loading: false,
        }));
        return { ok: true };
      }
      setLoading(false);
      return { ok: false, message: resp.message || 'Login failed' };
    } catch (e: any) {
      Logger.error('signInAdmin error', e);
      setLoading(false);
      const message = e?.response?.data?.message || e?.message || 'Login failed';
      return { ok: false, message };
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      await authService.logout();
    } catch (e) {
      Logger.warn('Server logout failed, clearing local anyway', e);
    } finally {
      await persistUser(null);
      setState({
        user: null,
        accessToken: null,
        refreshToken: null,
        authMethod: null,
        loading: false,
      });
    }
  };

  const refreshTokens = async (): Promise<boolean> => {
    try {
      await tokenManager.refreshToken();
      await updateTokensInState();
      return true;
    } catch (e) {
      Logger.error('refreshTokens failed', e);
      return false;
    }
  };

  useEffect(() => {
    // hydrate on mount
    restoreSession();
  }, [restoreSession]);

  const value: AuthContextType = {
    ...state,
    signInStudentTeacher,
    signInAdmin,
    signOut,
    restoreSession,
    refreshTokens,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within AuthProvider');
  return ctx;
};

export default AuthContext;
