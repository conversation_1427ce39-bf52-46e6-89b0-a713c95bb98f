/**
 * Authentication Context for NS School Manager Mobile App
 * Global state management for user authentication with secure token storage
 * and hydration using unified endpoints.
 */

import React, { createContext, useContext, useEffect, useMemo, useState, ReactNode, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from '../services/authService';
import { TokenManager } from '../services/tokenManager';
import { API_ENDPOINTS } from '../config/api';
import { getHttpClient } from '../services/httpClient';
import { Logger } from '../utils/logger';
import type { User, UserRole, LoginResponse, AuthStatusResponse } from '../types/api';

type AuthMethod = 'jwt' | 'session' | null;

export interface AuthState {
  user: { matricule: string; role: 'admin' | 'professeur' | 'eleve' } | null;
  accessToken: string | null;
  refreshToken: string | null;
  authMethod: AuthMethod;
  loading: boolean;
}

interface SignInStudentTeacherArgs {
  matricule: string;
  password: string;
  role: 'eleve' | 'professeur';
  remember?: boolean;
}

interface SignInAdminArgs {
  matricule: string;
  password: string;
  remember?: boolean;
}

interface AuthContextType extends AuthState {
  signInStudentTeacher: (args: SignInStudentTeacherArgs) => Promise<{ ok: boolean; message?: string }>;
  signInAdmin: (args: SignInAdminArgs) => Promise<{ ok: boolean; message?: string }>;
  signOut: () => Promise<void>;
  restoreSession: () => Promise<void>;
  refreshTokens: () => Promise<boolean>;
}

const USER_STORAGE_KEY = 'ns_user';

/**
 * Convert technical error messages to user-friendly login error messages
 */
const getLoginErrorMessage = (error: any): string => {
  // Check if it's a backend response with a message
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  // Check for specific HTTP status codes
  if (error?.response?.status === 401) {
    return 'Identifiants incorrects. Vérifiez votre matricule et mot de passe.';
  }

  if (error?.response?.status === 403) {
    return 'Accès refusé. Vous n\'avez pas les permissions nécessaires.';
  }

  if (error?.response?.status === 404) {
    return 'Service de connexion non disponible. Contactez l\'administrateur.';
  }

  if (error?.response?.status >= 500) {
    return 'Erreur du serveur. Veuillez réessayer plus tard.';
  }

  // Check for network errors
  if (error?.code === 'NETWORK_ERROR' || !error?.response) {
    return 'Problème de connexion. Vérifiez votre connexion internet.';
  }

  if (error?.code === 'TIMEOUT_ERROR' || error?.code === 'ECONNABORTED') {
    return 'La connexion a pris trop de temps. Veuillez réessayer.';
  }

  // Fallback to a generic message
  return 'Erreur de connexion. Veuillez vérifier vos identifiants et réessayer.';
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const authService = useMemo(() => AuthService.getInstance(), []);
  const tokenManager = useMemo(() => TokenManager.getInstance(), []);
  const [state, setState] = useState<AuthState>({
    user: null,
    accessToken: null,
    refreshToken: null,
    authMethod: null,
    loading: true,
  });

  // Handle authentication failures from HTTP client
  const handleAuthFailure = useCallback(async () => {
    Logger.warn('Authentication failure detected, signing out user');
    await persistUser(null);
    setState({
      user: null,
      accessToken: null,
      refreshToken: null,
      authMethod: null,
      loading: false,
    });
  }, []);

  const setLoading = (loading: boolean) => setState((s) => ({ ...s, loading }));

  const persistUser = async (user: User | null) => {
    try {
      if (user) {
        await AsyncStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));
      } else {
        await AsyncStorage.removeItem(USER_STORAGE_KEY);
      }
    } catch (e) {
      Logger.warn('Failed to persist user in AsyncStorage', e);
    }
  };

  const loadPersistedUser = async (): Promise<User | null> => {
    try {
      const raw = await AsyncStorage.getItem(USER_STORAGE_KEY);
      if (!raw) return null;
      return JSON.parse(raw) as User;
    } catch (e) {
      Logger.warn('Failed to load user from AsyncStorage', e);
      return null;
    }
  };

  const updateTokensInState = async () => {
    const tokens = await tokenManager.getAllTokens();
    setState((s) => ({
      ...s,
      accessToken: tokens?.accessToken ?? null,
      refreshToken: tokens?.refreshToken ?? null,
    }));
  };

  const rehydrateViaStatus = useCallback(async (): Promise<{ user: User | null; method: AuthMethod }> => {
    try {
      const http = getHttpClient();
      const status = await http.get<AuthStatusResponse>(API_ENDPOINTS.AUTH.STATUS);
      if (status.authenticated) {
        if (status.user) {
          await persistUser(status.user);
          return { user: status.user, method: status.authMethod ?? 'jwt' };
        }
        // If server says authenticated but no user payload, fallback to local user
        const local = await loadPersistedUser();
        return { user: local, method: status.authMethod ?? 'jwt' };
      }
      return { user: null, method: null };
    } catch (e) {
      Logger.warn('Status rehydration failed', e);
      // Fallback to local only
      const local = await loadPersistedUser();
      return { user: local, method: null };
    }
  }, []);

  const restoreSession = useCallback(async () => {
    setLoading(true);
    try {
      const hasTokens = await tokenManager.hasValidTokens();
      if (!hasTokens) {
        await authService.clearLocalAuth();
        setState((s) => ({ ...s, user: null, accessToken: null, refreshToken: null, authMethod: null, loading: false }));
        return;
      }

      // Update tokens in local state
      await updateTokensInState();

      // Try server status to rehydrate user/role reliably
      const { user, method } = await rehydrateViaStatus();
      await persistUser(user);

      setState((s) => ({
        ...s,
        user: user ? { matricule: user.matricule, role: user.role } : null,
        authMethod: method,
        loading: false,
      }));
    } catch (e) {
      Logger.error('restoreSession failed', e);
      await authService.clearLocalAuth();
      setState((s) => ({ ...s, user: null, accessToken: null, refreshToken: null, authMethod: null, loading: false }));
    }
  }, [authService, rehydrateViaStatus, tokenManager]);

  const signInStudentTeacher: AuthContextType['signInStudentTeacher'] = async ({ matricule, password, role, remember = false }) => {
    setLoading(true);
    try {
      const resp: LoginResponse = await authService.login(matricule, password, role as UserRole, remember);
      if (resp.success && resp.user && resp.tokens) {
        await persistUser(resp.user);
        await updateTokensInState();
        setState((s) => ({
          ...s,
          user: { matricule: resp.user!.matricule, role: resp.user!.role },
          authMethod: resp.authMethod ?? 'jwt',
          loading: false,
        }));
        return { ok: true };
      }
      setLoading(false);
      return { ok: false, message: resp.message || 'Login failed' };
    } catch (e: any) {
      Logger.error('signInStudentTeacher error', e);
      setLoading(false);
      const message = getLoginErrorMessage(e);
      return { ok: false, message };
    }
  };

  const signInAdmin: AuthContextType['signInAdmin'] = async ({ matricule, password, remember = false }) => {
    setLoading(true);
    try {
      const resp: LoginResponse = await authService.loginAdmin(matricule, password, remember);
      if (resp.success && resp.user && resp.tokens) {
        await persistUser(resp.user);
        await updateTokensInState();
        setState((s) => ({
          ...s,
          user: { matricule: resp.user!.matricule, role: resp.user!.role },
          authMethod: resp.authMethod ?? 'jwt',
          loading: false,
        }));
        return { ok: true };
      }
      setLoading(false);
      return { ok: false, message: resp.message || 'Login failed' };
    } catch (e: any) {
      Logger.error('signInAdmin error', e);
      setLoading(false);
      const message = getLoginErrorMessage(e);
      return { ok: false, message };
    }
  };

  const signOut = async () => {
    setLoading(true);
    try {
      await authService.logout();
    } catch (e) {
      Logger.warn('Server logout failed, clearing local anyway', e);
    } finally {
      await persistUser(null);
      setState({
        user: null,
        accessToken: null,
        refreshToken: null,
        authMethod: null,
        loading: false,
      });
    }
  };

  const refreshTokens = async (): Promise<boolean> => {
    try {
      await tokenManager.refreshToken();
      await updateTokensInState();
      return true;
    } catch (e) {
      Logger.error('refreshTokens failed', e);
      return false;
    }
  };

  useEffect(() => {
    // hydrate on mount
    restoreSession();
  }, [restoreSession]);

  // Register for authentication failure notifications
  useEffect(() => {
    try {
      const httpClient = getHttpClient();
      const unsubscribe = httpClient.onAuthFailure(handleAuthFailure);
      return unsubscribe;
    } catch (error) {
      // HTTP client might not be initialized yet
      Logger.debug('HTTP client not available for auth failure registration', error);
    }
  }, [handleAuthFailure]);

  const value: AuthContextType = {
    ...state,
    signInStudentTeacher,
    signInAdmin,
    signOut,
    restoreSession,
    refreshTokens,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error('useAuth must be used within AuthProvider');
  return ctx;
};

export default AuthContext;
