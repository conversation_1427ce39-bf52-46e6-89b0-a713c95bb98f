/**
 * API Configuration for NS School Manager Mobile App
 * Centralized configuration for API endpoints and settings
 */

import { ApiConfig } from '../types/api';

// Modern Expo environment variables - NO fallbacks, fail fast if not configured
const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;
const API_TIMEOUT = process.env.EXPO_PUBLIC_API_TIMEOUT;
const DEBUG_MODE = process.env.EXPO_PUBLIC_DEBUG_MODE === 'true';
const ENABLE_LOGGING = process.env.EXPO_PUBLIC_ENABLE_LOGGING === 'true';

// Validate required environment variables
if (!API_BASE_URL) {
  throw new Error('EXPO_PUBLIC_API_BASE_URL is required in .env file');
}

if (!API_TIMEOUT) {
  throw new Error('EXPO_PUBLIC_API_TIMEOUT is required in .env file');
}

export const API_CONFIG: ApiConfig = {
  baseURL: API_BASE_URL,
  timeout: parseInt(API_TIMEOUT, 10),
  retryAttempts: 3,
  retryDelay: 1000, // 1 second base delay
};

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGIN_ADMIN: '/api/auth/login_ad',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    STATUS: '/api/auth/status',
  },

  // Users
  USERS: {
    ELEVES: '/api/eleve',
    PROFESSEURS: '/api/prof',
    ADMINS: '/api/admin',
  },

  // Academic
  ACADEMIC: {
    CLASSES: '/api/classe',
    MATIERES: '/api/matiere',
    COURS: '/api/cours',
    NOTES: '/eleve/notes/api', // Student notes API endpoint
    DEVOIRS: '/prof/devoir/api', // Professor devoir API endpoint
    SOUMISSIONS: '/api/soumission',
  },

  // Communication
  COMMUNICATION: {
    MESSAGES: '/api/message',
    CONVERSATIONS: '/api/message/conversations',
    SEND_MESSAGE: '/api/message/send',
  },

  // Schedule
  SCHEDULE: {
    EMPLOI_TEMPS_ELEVE: '/api/emploi_temps/eleve', // Student schedule endpoint
    EMPLOI_TEMPS_PROFESSEUR: '/api/emploi_temps/professeur', // Professor schedule endpoint
    EMPLOI_TEMPS: '/api/emploi_temps', // Generic schedule endpoint
    SALLES: '/api/salles', // Fixed: plural form
  },

  // Attendance
  ATTENDANCE: {
    ABSENCES: '/api/absence',
    PRESENCES: '/api/presence',
  },

  // Files
  FILES: {
    UPLOAD: '/api/files/upload',
    DOWNLOAD: '/api/files/download',
  },

  // Gestion des Salles (from backend/routes/api/salles.js)
  SALLE: {
    BASE: '/api/salles',
    ITEM: (id: number | string) => `/api/salles/${id}`,
    // CRUD operations
    CREATE: '/api/salles',                    // POST - Add new room
    UPDATE: (id: number | string) => `/api/salles/${id}`, // PUT - Update room
    DELETE: (id: number | string) => `/api/salles/${id}`, // DELETE - Remove room
    // Note: backend exposes '/api/salles/statistiques' (plural in FR)
    STATS: '/api/salles/statistiques',
    // For select options in timetable or forms
    OPTIONS_SELECT: '/api/salles/options/select',
    // For form field options (room types, floors, buildings, capacities)
    OPTIONS_FORM: '/api/salles/options/form',
    // Availability check utility
    VERIFY: '/api/salles/verifier-disponibilite',
    // Filter by type
    BY_TYPE: (type: string) => `/api/salles/types/${encodeURIComponent(type)}`,
  },
} as const;

// Request timeout configurations for different types of requests
export const TIMEOUT_CONFIG = {
  FAST: 5000,      // 5 seconds for quick requests
  NORMAL: 15000,   // 15 seconds for normal requests
  SLOW: 30000,     // 30 seconds for slow requests
  UPLOAD: 60000,   // 60 seconds for file uploads
  DOWNLOAD: 120000, // 2 minutes for file downloads
};

// Retry configurations
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  BASE_DELAY: 1000,
  MAX_DELAY: 10000,
  BACKOFF_FACTOR: 2,
};

// Cache configurations
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000,      // 5 minutes
  USER_DATA_TTL: 30 * 60 * 1000,   // 30 minutes
  STATIC_DATA_TTL: 60 * 60 * 1000, // 1 hour
};

// Network status check configuration
export const NETWORK_CONFIG = {
  CHECK_INTERVAL: 30000, // 30 seconds
  PING_ENDPOINT: '/api/auth/status',
  OFFLINE_RETRY_DELAY: 5000, // 5 seconds
};

// Error message configurations
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Problème de connexion réseau. Vérifiez votre connexion internet.',
  TIMEOUT_ERROR: 'La requête a pris trop de temps. Veuillez réessayer.',
  AUTHENTICATION_ERROR: 'Session expirée. Veuillez vous reconnecter.',
  AUTHORIZATION_ERROR: 'Vous n\'avez pas les permissions nécessaires.',
  VALIDATION_ERROR: 'Données invalides. Vérifiez vos informations.',
  NOT_FOUND: 'Ressource non trouvée.',
  SERVER_ERROR: 'Erreur du serveur. Veuillez réessayer plus tard.',
  UNKNOWN_ERROR: 'Une erreur inattendue s\'est produite.',
} as const;

// Success message configurations
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Connexion réussie',
  LOGOUT_SUCCESS: 'Déconnexion réussie',
  DATA_SAVED: 'Données sauvegardées avec succès',
  MESSAGE_SENT: 'Message envoyé avec succès',
  FILE_UPLOADED: 'Fichier téléchargé avec succès',
  PASSWORD_CHANGED: 'Mot de passe modifié avec succès',
} as const;

// Development configurations
export const DEV_CONFIG = {
  ENABLE_LOGGING: ENABLE_LOGGING,
  ENABLE_NETWORK_LOGGING: DEBUG_MODE,
  ENABLE_MOCK_DATA: false, // Set to true to use mock data
  MOCK_DELAY: 1000, // Delay for mock responses
};

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_OFFLINE_MODE: true,
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_FILE_UPLOAD: true,
  ENABLE_REAL_TIME_MESSAGES: false, // WebSocket support
  ENABLE_BIOMETRIC_AUTH: false,
  ENABLE_DARK_MODE: true,
} as const;

// App version and build info
export const APP_INFO = {
  VERSION: '1.0.0',
  BUILD_NUMBER: '1',
  API_VERSION: 'v1',
  MIN_API_VERSION: 'v1',
} as const;

// Export utility functions
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.baseURL}${endpoint}`;
};

export const isProduction = (): boolean => {
  return !DEBUG_MODE;
};

export const isDev = (): boolean => {
  return DEBUG_MODE;
};

// Export all configurations as a single object for easy access
export const CONFIG = {
  API: API_CONFIG,
  ENDPOINTS: API_ENDPOINTS,
  TIMEOUT: TIMEOUT_CONFIG,
  RETRY: RETRY_CONFIG,
  CACHE: CACHE_CONFIG,
  NETWORK: NETWORK_CONFIG,
  ERRORS: ERROR_MESSAGES,
  SUCCESS: SUCCESS_MESSAGES,
  DEV: DEV_CONFIG,
  FEATURES: FEATURE_FLAGS,
  APP: APP_INFO,
} as const;
