/**
 * Dashboard types for role-specific summaries and listings
 */

export interface AdminSummary {
  classes: number;
  profs: number;
  eleves: number;
  messages: number;
}

export interface MessageSummary {
  id: string | number;
  title: string;
  from?: string;
  createdAt?: string;
}

export interface TimetableEntry {
  id: string | number;
  subject: string;
  start: string;
  end: string;
  room?: string;
}

export interface DevoirSummary {
  id: string | number;
  title: string;
  dueDate?: string;
  status?: string;
}

export interface NoteSummary {
  id: string | number;
  subject: string;
  value: number;
  coef?: number;
  createdAt?: string;
}

export interface AnnonceSummary {
  id: string | number;
  title: string;
  createdAt?: string;
}