/**
 * API Types and Interfaces for NS School Manager Mobile App
 * Comprehensive type definitions for all API interactions
 */

// ============================================================================
// Base API Types
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp?: string;
}

export interface ApiError {
  success: false;
  message: string;
  errorCode?: string;
  details?: any;
  timestamp?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// ============================================================================
// User Types
// ============================================================================

export type UserRole = 'admin' | 'professeur' | 'eleve';

export interface User {
  matricule: string;
  role: UserRole;
  nom?: string;
  prenom?: string;
  email?: string;
  telephone?: string;
  sexe?: 'homme' | 'femme';
}

export interface Admin extends User {
  role: 'admin';
}

export interface Professeur extends User {
  role: 'professeur';
  specialiste?: string;
}

export interface Eleve extends User {
  role: 'eleve';
  date_naissance?: string;
  nom_classe?: string;
}

// ============================================================================
// Authentication Types
// ============================================================================

export interface LoginRequest {
  matricule: string;
  password: string;
  role?: UserRole;
  remember?: boolean;
  api: boolean;
}

export interface AdminLoginRequest {
  matricule: string;
  password: string;
  remember?: boolean;
  api: boolean;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginResponse extends ApiResponse {
  user: User;
  tokens?: AuthTokens;
  authMethod: 'jwt' | 'session';
  redirect?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse extends ApiResponse {
  tokens: AuthTokens;
}

export interface AuthStatusResponse extends ApiResponse {
  authenticated: boolean;
  authMethod?: 'jwt' | 'session';
  user?: User;
}

// ============================================================================
// Academic Types
// ============================================================================

export interface Classe {
  nom_classe: string;
  niveau?: string;
  effectif?: number;
}

export interface Matiere {
  matiere_id: number;
  nom: string;
  professeur_matricule: string;
  nom_classe: string;
}

export interface Cours {
  cours_id: number;
  titre: string;
  fichier?: string;
  professeur_matricule: string;
  matiere_id: number;
  nom_classe: string;
  date_ajout: string;
}

export interface Note {
  note_id: number;
  eleve_matricule: string;
  matiere_id: number;
  note: number;
  coefficient: number;
  trimestre: number;
  type_evaluation: string;
  date_evaluation: string;
}

export interface Absence {
  absence_id: number;
  eleve_matricule: string;
  date_absence: string;
  justifiee: boolean;
  motif?: string;
  professeur_matricule?: string;
}

export interface Devoir {
  devoir_id: number;
  titre: string;
  description?: string;
  fichier?: string;
  date_limite: string;
  professeur_matricule: string;
  matiere_id: number;
  nom_classe: string;
  date_creation: string;
}

export interface Soumission {
  soumission_id: number;
  devoir_id: number;
  eleve_matricule: string;
  fichier?: string;
  commentaire?: string;
  date_soumission: string;
  note?: number;
}

// ============================================================================
// Message Types
// ============================================================================

export interface Message {
  message_id: number;
  sender_matricule: string;
  receiver_matricule?: string;
  nom_classe?: string;
  contenu: string;
  subject?: string;
  type: 'prive' | 'annonce' | 'classe';
  priority?: 'normal' | 'urgent';
  date_envoi: string;
  lu: boolean;
  fichiers?: string[];
}

export interface Conversation {
  participant_matricule: string;
  participant_nom: string;
  participant_prenom: string;
  participant_role: UserRole;
  last_message: string;
  last_message_date: string;
  unread_count: number;
}

// ============================================================================
// Schedule Types
// ============================================================================

export interface EmploiTemps {
  emploi_id: number;
  jour: 'lundi' | 'mardi' | 'mercredi' | 'jeudi' | 'vendredi' | 'samedi';
  heure_debut: string;
  heure_fin: string;
  matiere_id: number;
  nom_classe: string;
  salle?: string;
  professeur_matricule: string;
}

export interface Salle {
  salle_id: number;
  nom_salle: string;
  capacite?: number;
  type_salle?: string;
  equipements?: string;
}

// ============================================================================
// Request/Response Types for Specific Endpoints
// ============================================================================

export interface CreateEleveRequest {
  matricule: string;
  nom: string;
  prenom: string;
  sexe: 'homme' | 'femme';
  date_naissance: string;
  classe: string;
  email?: string;
  telephone?: string;
}

export interface CreateProfesseurRequest {
  matricule: string;
  nom: string;
  prenom: string;
  sexe: 'homme' | 'femme';
  specialiste: string;
  email?: string;
  telephone?: string;
}

export interface SendMessageRequest {
  type: 'prive' | 'annonce' | 'classe';
  receiver_matricule?: string;
  nom_classe?: string;
  contenu: string;
  subject?: string;
  priority?: 'normal' | 'urgent';
  attachments?: File[];
}

// ============================================================================
// HTTP Client Configuration
// ============================================================================

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export interface RequestConfig {
  requiresAuth?: boolean;
  retryOnFailure?: boolean;
  showLoader?: boolean;
  showErrorAlert?: boolean;
}

// ============================================================================
// Error Types
// ============================================================================

export enum ApiErrorCode {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  SERVER_ERROR = 'SERVER_ERROR',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  REFRESH_TOKEN_EXPIRED = 'REFRESH_TOKEN_EXPIRED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface NetworkError extends Error {
  code: ApiErrorCode;
  status?: number;
  response?: any;
  isRetryable: boolean;
}

// ============================================================================
// Storage Types
// ============================================================================

export interface StoredTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

export interface StoredUser {
  user: User;
  lastLogin: string;
}
