export type SalleType = 'normale' | 'laboratoire' | 'informatique' | 'sport' | 'bibliotheque' | 'autre';

export interface Salle {
  id: number | string;
  nom_salle: string;
  type_salle: SalleType;
  capacite: number;
  etage: string;
  batiment: string;
  equipements?: string;
  disponible: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateSalleDto {
  nom_salle: string;
  type_salle: SalleType;
  capacite: number;
  etage: string;
  batiment: string;
  equipements?: string;
  disponible?: boolean;
}

export type UpdateSalleDto = Partial<CreateSalleDto>;

export interface SalleStats {
  total: number;
  byType: Record<SalleType, number>;
  available: number;
}

export interface SelectOption {
  label: string;
  value: string;
}

export interface FormOptions {
  roomTypes: SelectOption[];
  floors: SelectOption[];
  buildings: SelectOption[];
  capacities: SelectOption[];
}