import { useEffect, useState } from 'react';
import { sallesService } from '../services/sallesService';
import type { FormOptions, SelectOption } from '../types/salles';

interface UseSalleFormOptionsReturn {
  options: FormOptions | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Hook to fetch and cache salle form options from the backend
 * This centralizes the form options instead of hardcoding them in components
 */
export const useSalleFormOptions = (): UseSalleFormOptionsReturn => {
  const [options, setOptions] = useState<FormOptions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOptions = async () => {
    try {
      setLoading(true);
      setError(null);
      const formOptions = await sallesService.getFormOptions();
      
      // Add "custom" option to each select for manual input
      const optionsWithCustom: FormOptions = {
        roomTypes: [...formOptions.roomTypes],
        floors: [...formOptions.floors, { label: 'Autre (saisir manuellement)', value: 'custom' }],
        buildings: [...formOptions.buildings, { label: 'Autre (saisir manuellement)', value: 'custom' }],
        capacities: [...formOptions.capacities, { label: 'Autre (saisir manuellement)', value: 'custom' }],
      };
      
      setOptions(optionsWithCustom);
    } catch (err: any) {
      console.error('Error fetching form options:', err);
      setError(err?.message || 'Erreur lors du chargement des options');
      
      // Fallback to minimal default options
      setOptions({
        roomTypes: [
          { label: 'Salle Normale', value: 'normale' },
          { label: 'Laboratoire', value: 'laboratoire' },
          { label: 'Salle Informatique', value: 'informatique' },
          { label: 'Installation Sportive', value: 'sport' },
          { label: 'Bibliothèque', value: 'bibliotheque' },
          { label: 'Autre', value: 'autre' }
        ],
        floors: [
          { label: 'Rez-de-chaussée', value: 'RDC' },
          { label: '1er étage', value: '1er' },
          { label: '2ème étage', value: '2ème' },
          { label: '3ème étage', value: '3ème' },
          { label: 'Autre (saisir manuellement)', value: 'custom' }
        ],
        buildings: [
          { label: 'Bâtiment A', value: 'Bâtiment A' },
          { label: 'Bâtiment B', value: 'Bâtiment B' },
          { label: 'Bâtiment C', value: 'Bâtiment C' },
          { label: 'Autre (saisir manuellement)', value: 'custom' }
        ],
        capacities: [
          { label: '20 personnes', value: '20' },
          { label: '30 personnes', value: '30' },
          { label: '40 personnes', value: '40' },
          { label: 'Autre (saisir manuellement)', value: 'custom' }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOptions();
  }, []);

  return {
    options,
    loading,
    error,
    refetch: fetchOptions,
  };
};
