/**
 * Main API Service for NS School Manager Mobile App
 * Centralized service for all API interactions following DRY principles
 */

import { 
  ApiResponse, 
  PaginatedResponse, 
  User, 
  Eleve, 
  Professeur,
  Classe,
  Matiere,
  Cours,
  Note,
  Message,
  Conversation,
  EmploiTemps,
  CreateEleveRequest,
  CreateProfesseurRequest,
  SendMessageRequest
} from '../types/api';
import { getHttpClient } from './httpClient';
import { API_ENDPOINTS } from '../config/api';
import { Logger } from '../utils/logger';

/**
 * Base API Service class with common functionality
 */
abstract class BaseApiService {
  protected httpClient = getHttpClient();
  protected abstract serviceName: string;

  protected logRequest(method: string, endpoint: string, data?: any): void {
    Logger.debug(`${this.serviceName} API Request`, { method, endpoint, data }, this.serviceName);
  }

  protected logResponse(method: string, endpoint: string, success: boolean): void {
    Logger.debug(`${this.serviceName} API Response`, { method, endpoint, success }, this.serviceName);
  }

  protected logError(method: string, endpoint: string, error: any): void {
    Logger.error(`${this.serviceName} API Error`, { method, endpoint, error }, this.serviceName);
  }
}

/**
 * User Management Service
 */
export class UserService extends BaseApiService {
  protected serviceName = 'UserService';

  /**
   * Get all students
   */
  async getEleves(): Promise<Eleve[]> {
    this.logRequest('GET', API_ENDPOINTS.USERS.ELEVES);
    try {
      const response = await this.httpClient.get<ApiResponse<Eleve[]>>(API_ENDPOINTS.USERS.ELEVES);
      this.logResponse('GET', API_ENDPOINTS.USERS.ELEVES, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', API_ENDPOINTS.USERS.ELEVES, error);
      throw error;
    }
  }

  /**
   * Create new student
   */
  async createEleve(eleveData: CreateEleveRequest): Promise<ApiResponse> {
    this.logRequest('POST', API_ENDPOINTS.USERS.ELEVES, eleveData);
    try {
      const response = await this.httpClient.post<ApiResponse>(API_ENDPOINTS.USERS.ELEVES, eleveData);
      this.logResponse('POST', API_ENDPOINTS.USERS.ELEVES, response.success);
      return response;
    } catch (error) {
      this.logError('POST', API_ENDPOINTS.USERS.ELEVES, error);
      throw error;
    }
  }

  /**
   * Update student
   */
  async updateEleve(matricule: string, updates: Partial<Eleve>): Promise<ApiResponse> {
    const endpoint = `${API_ENDPOINTS.USERS.ELEVES}/${matricule}`;
    this.logRequest('PUT', endpoint, updates);
    try {
      const response = await this.httpClient.put<ApiResponse>(endpoint, updates);
      this.logResponse('PUT', endpoint, response.success);
      return response;
    } catch (error) {
      this.logError('PUT', endpoint, error);
      throw error;
    }
  }

 /**
  * Delete student
  */
 async deleteEleve(matricule: string): Promise<ApiResponse> {
   const endpoint = `${API_ENDPOINTS.USERS.ELEVES}/${matricule}`;
   this.logRequest('DELETE', endpoint);
   try {
     const response = await this.httpClient.delete<ApiResponse>(endpoint);
     this.logResponse('DELETE', endpoint, (response as any)?.success ?? true);
     return response as any;
   } catch (error) {
     this.logError('DELETE', endpoint, error);
     throw error;
   }
 }

 /**
  * Get all teachers
  */
 async getProfesseurs(): Promise<Professeur[]> {
    this.logRequest('GET', API_ENDPOINTS.USERS.PROFESSEURS);
    try {
      const raw = await this.httpClient.get<any>(API_ENDPOINTS.USERS.PROFESSEURS);
      // Normalize like web: response.success ? response.data : response
      if (raw && typeof raw === 'object' && 'data' in raw && Array.isArray(raw.data)) {
        this.logResponse('GET', API_ENDPOINTS.USERS.PROFESSEURS, (raw as any).success ?? true);
        return raw.data as Professeur[];
      }
      if (Array.isArray(raw)) {
        this.logResponse('GET', API_ENDPOINTS.USERS.PROFESSEURS, true);
        return raw as Professeur[];
      }
      this.logResponse('GET', API_ENDPOINTS.USERS.PROFESSEURS, false);
      return [];
    } catch (error) {
      this.logError('GET', API_ENDPOINTS.USERS.PROFESSEURS, error);
      throw error;
    }
  }

  /**
   * Create new teacher
   */
  async createProfesseur(profData: CreateProfesseurRequest): Promise<ApiResponse> {
    this.logRequest('POST', API_ENDPOINTS.USERS.PROFESSEURS, profData);
    try {
      const response = await this.httpClient.post<ApiResponse>(API_ENDPOINTS.USERS.PROFESSEURS, profData);
      this.logResponse('POST', API_ENDPOINTS.USERS.PROFESSEURS, (response as any)?.success ?? true);
      return response;
    } catch (error) {
      this.logError('POST', API_ENDPOINTS.USERS.PROFESSEURS, error);
      throw error;
    }
  }

  /**
   * Get available specialities for professors
   */
  async getProfSpecialites(): Promise<string[]> {
    const endpoint = `${API_ENDPOINTS.USERS.PROFESSEURS}/specialites`;
    this.logRequest('GET', endpoint);
    try {
      const raw = await this.httpClient.get<any>(endpoint);
      // Accept ApiResponse { data: string[] } or raw string[]
      if (raw && typeof raw === 'object' && Array.isArray(raw.data)) {
        this.logResponse('GET', endpoint, (raw as any).success ?? true);
        return raw.data as string[];
      }
      if (Array.isArray(raw)) {
        this.logResponse('GET', endpoint, true);
        return raw as string[];
      }
      // Also accept { specialites: [...] }
      if (raw && typeof raw === 'object' && Array.isArray(raw.specialites)) {
        this.logResponse('GET', endpoint, true);
        return raw.specialites as string[];
      }
      this.logResponse('GET', endpoint, false);
      return [];
    } catch (error) {
      this.logError('GET', endpoint, error);
      throw error;
    }
  }

  /**
   * Update teacher
   */
  async updateProfesseur(matricule: string, updates: Partial<Professeur> & { password?: string }): Promise<ApiResponse> {
    const endpoint = `${API_ENDPOINTS.USERS.PROFESSEURS}/${matricule}`;
    this.logRequest('PUT', endpoint, updates);
    try {
      const response = await this.httpClient.put<ApiResponse>(endpoint, updates);
      this.logResponse('PUT', endpoint, (response as any)?.success ?? true);
      return response;
    } catch (error) {
      this.logError('PUT', endpoint, error);
      throw error;
    }
  }

  /**
   * Delete teacher
   */
  async deleteProfesseur(matricule: string): Promise<ApiResponse> {
    const endpoint = `${API_ENDPOINTS.USERS.PROFESSEURS}/${matricule}`;
    this.logRequest('DELETE', endpoint);
    try {
      const response = await this.httpClient.delete<ApiResponse>(endpoint);
      this.logResponse('DELETE', endpoint, (response as any)?.success ?? true);
      return response as any;
    } catch (error) {
      this.logError('DELETE', endpoint, error);
      throw error;
    }
  }

}

/**
 * Academic Service
 */
export class AcademicService extends BaseApiService {
  protected serviceName = 'AcademicService';

  /**
   * Get all classes
   */
  async getClasses(): Promise<Classe[]> {
    this.logRequest('GET', API_ENDPOINTS.ACADEMIC.CLASSES);
    try {
      const raw = await this.httpClient.get<any>(API_ENDPOINTS.ACADEMIC.CLASSES);

      // Normalize various backend shapes:
      // 1) ApiResponse: { success: boolean, data: Classe[] }
      if (raw && typeof raw === 'object' && 'data' in raw && Array.isArray(raw.data)) {
        this.logResponse('GET', API_ENDPOINTS.ACADEMIC.CLASSES, (raw as any).success ?? true);
        return raw.data as Classe[];
      }

      // 2) Raw array: [ ...classes ]
      if (Array.isArray(raw)) {
        this.logResponse('GET', API_ENDPOINTS.ACADEMIC.CLASSES, true);
        return raw as Classe[];
      }

      // 3) Alternative envelope: { classes: [...] }
      if (raw && typeof raw === 'object' && Array.isArray(raw.classes)) {
        this.logResponse('GET', API_ENDPOINTS.ACADEMIC.CLASSES, true);
        return raw.classes as Classe[];
      }

      // Fallback: log and return empty
      this.logResponse('GET', API_ENDPOINTS.ACADEMIC.CLASSES, false);
      return [];
    } catch (error) {
      this.logError('GET', API_ENDPOINTS.ACADEMIC.CLASSES, error);
      throw error;
    }
  }

  /**
   * Get subjects
   */
  async getMatieres(): Promise<Matiere[]> {
    this.logRequest('GET', API_ENDPOINTS.ACADEMIC.MATIERES);
    try {
      const response = await this.httpClient.get<ApiResponse<Matiere[]>>(API_ENDPOINTS.ACADEMIC.MATIERES);
      this.logResponse('GET', API_ENDPOINTS.ACADEMIC.MATIERES, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', API_ENDPOINTS.ACADEMIC.MATIERES, error);
      throw error;
    }
  }

  /**
   * Get courses
   */
  async getCours(): Promise<Cours[]> {
    this.logRequest('GET', API_ENDPOINTS.ACADEMIC.COURS);
    try {
      const response = await this.httpClient.get<ApiResponse<Cours[]>>(API_ENDPOINTS.ACADEMIC.COURS);
      this.logResponse('GET', API_ENDPOINTS.ACADEMIC.COURS, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', API_ENDPOINTS.ACADEMIC.COURS, error);
      throw error;
    }
  }

  /**
   * Get student grades
   */
  async getNotes(eleveMatricule?: string): Promise<Note[]> {
    const endpoint = eleveMatricule 
      ? `${API_ENDPOINTS.ACADEMIC.NOTES}?eleve=${eleveMatricule}`
      : API_ENDPOINTS.ACADEMIC.NOTES;
    
    this.logRequest('GET', endpoint);
    try {
      const response = await this.httpClient.get<ApiResponse<Note[]>>(endpoint);
      this.logResponse('GET', endpoint, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', endpoint, error);
      throw error;
    }
  }
}

/**
 * Communication Service
 */
export class CommunicationService extends BaseApiService {
  protected serviceName = 'CommunicationService';

  /**
   * Get conversations
   */
  async getConversations(): Promise<Conversation[]> {
    this.logRequest('GET', API_ENDPOINTS.COMMUNICATION.CONVERSATIONS);
    try {
      const response = await this.httpClient.get<ApiResponse<Conversation[]>>(API_ENDPOINTS.COMMUNICATION.CONVERSATIONS);
      this.logResponse('GET', API_ENDPOINTS.COMMUNICATION.CONVERSATIONS, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', API_ENDPOINTS.COMMUNICATION.CONVERSATIONS, error);
      throw error;
    }
  }

  /**
   * Get messages
   */
  async getMessages(conversationWith?: string): Promise<Message[]> {
    const endpoint = conversationWith 
      ? `${API_ENDPOINTS.COMMUNICATION.MESSAGES}?with=${conversationWith}`
      : API_ENDPOINTS.COMMUNICATION.MESSAGES;
    
    this.logRequest('GET', endpoint);
    try {
      const response = await this.httpClient.get<ApiResponse<Message[]>>(endpoint);
      this.logResponse('GET', endpoint, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', endpoint, error);
      throw error;
    }
  }

  /**
   * Send message
   */
  async sendMessage(messageData: SendMessageRequest): Promise<ApiResponse> {
    this.logRequest('POST', API_ENDPOINTS.COMMUNICATION.SEND_MESSAGE, messageData);
    try {
      const response = await this.httpClient.post<ApiResponse>(API_ENDPOINTS.COMMUNICATION.SEND_MESSAGE, messageData);
      this.logResponse('POST', API_ENDPOINTS.COMMUNICATION.SEND_MESSAGE, response.success);
      return response;
    } catch (error) {
      this.logError('POST', API_ENDPOINTS.COMMUNICATION.SEND_MESSAGE, error);
      throw error;
    }
  }
}

/**
 * Schedule Service
 */
export class ScheduleService extends BaseApiService {
  protected serviceName = 'ScheduleService';

  /**
   * Get schedule
   */
  async getEmploiTemps(classe?: string): Promise<EmploiTemps[]> {
    const endpoint = classe 
      ? `${API_ENDPOINTS.SCHEDULE.EMPLOI_TEMPS}?classe=${classe}`
      : API_ENDPOINTS.SCHEDULE.EMPLOI_TEMPS;
    
    this.logRequest('GET', endpoint);
    try {
      const response = await this.httpClient.get<ApiResponse<EmploiTemps[]>>(endpoint);
      this.logResponse('GET', endpoint, response.success);
      return response.data || [];
    } catch (error) {
      this.logError('GET', endpoint, error);
      throw error;
    }
  }
}

// Singleton instances
let userServiceInstance: UserService;
let academicServiceInstance: AcademicService;
let communicationServiceInstance: CommunicationService;
let scheduleServiceInstance: ScheduleService;

export const getUserService = (): UserService => {
  if (!userServiceInstance) {
    userServiceInstance = new UserService();
  }
  return userServiceInstance;
};

export const getAcademicService = (): AcademicService => {
  if (!academicServiceInstance) {
    academicServiceInstance = new AcademicService();
  }
  return academicServiceInstance;
};

export const getCommunicationService = (): CommunicationService => {
  if (!communicationServiceInstance) {
    communicationServiceInstance = new CommunicationService();
  }
  return communicationServiceInstance;
};

export const getScheduleService = (): ScheduleService => {
  if (!scheduleServiceInstance) {
    scheduleServiceInstance = new ScheduleService();
  }
  return scheduleServiceInstance;
};

// Export all services as a single object
export const ApiServices = {
  user: getUserService,
  academic: getAcademicService,
  communication: getCommunicationService,
  schedule: getScheduleService,
};
