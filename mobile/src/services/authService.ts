/**
 * Authentication Service for NS School Manager Mobile App
 * Handles login, logout, token refresh, and user state management
 */

import { 
  LoginRequest, 
  AdminLoginRequest, 
  LoginResponse, 
  RefreshTokenRequest, 
  RefreshTokenResponse, 
  AuthStatusResponse,
  User,
  UserRole,
  ApiResponse
} from '../types/api';
import { getHttpClient } from './httpClient';
import { TokenManager } from './tokenManager';
import { UserStorage } from './userStorage';
import { Logger } from '../utils/logger';

export class AuthService {
  private static instance: AuthService;
  private tokenManager: TokenManager;
  private userStorage: UserStorage;

  private constructor() {
    this.tokenManager = TokenManager.getInstance();
    this.userStorage = UserStorage.getInstance();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * <PERSON>gin user (student or teacher)
   */
  async login(
    matricule: string, 
    password: string, 
    role: UserRole, 
    remember: boolean = false
  ): Promise<LoginResponse> {
    try {
      Logger.debug('Attempting login', { matricule, role, remember });

      const request: LoginRequest = {
        matricule,
        password,
        role,
        remember,
        api: true, // Always true for mobile
      };

      const httpClient = getHttpClient();
      const response = await httpClient.post<LoginResponse>('/api/auth/login', request);

      if (response.success && response.tokens && response.user) {
        // Store tokens and user data
        await Promise.all([
          this.tokenManager.storeTokens(response.tokens),
          this.userStorage.storeUser(response.user),
        ]);

        Logger.info('Login successful', { 
          matricule: response.user.matricule, 
          role: response.user.role 
        });
      }

      return response;
    } catch (error) {
      Logger.error('Login failed', { matricule, role, error });
      throw error;
    }
  }

  /**
   * Login admin user
   */
  async loginAdmin(
    matricule: string, 
    password: string, 
    remember: boolean = false
  ): Promise<LoginResponse> {
    try {
      Logger.debug('Attempting admin login', { matricule, remember });

      const request: AdminLoginRequest = {
        matricule,
        password,
        remember,
        api: true, // Always true for mobile
      };

      const httpClient = getHttpClient();
      const response = await httpClient.post<LoginResponse>('/api/auth/login_ad', request);

      if (response.success && response.tokens && response.user) {
        // Store tokens and user data
        await Promise.all([
          this.tokenManager.storeTokens(response.tokens),
          this.userStorage.storeUser(response.user),
        ]);

        Logger.info('Admin login successful', { 
          matricule: response.user.matricule, 
          role: response.user.role 
        });
      }

      return response;
    } catch (error) {
      Logger.error('Admin login failed', { matricule, error });
      throw error;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      Logger.debug('Logging out user');

      // Try to call logout endpoint (optional, may fail if token is invalid)
      try {
        const httpClient = getHttpClient();
        await httpClient.post<ApiResponse>('/api/auth/logout');
      } catch (error) {
        Logger.warn('Logout endpoint call failed', error);
        // Continue with local logout even if server call fails
      }

      // Clear local storage
      await Promise.all([
        this.tokenManager.clearTokens(),
        this.userStorage.clearUser(),
      ]);

      Logger.info('Logout successful');
    } catch (error) {
      Logger.error('Logout failed', error);
      throw error;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(refreshToken?: string): Promise<RefreshTokenResponse> {
    try {
      const tokenToUse = refreshToken || await this.tokenManager.getRefreshToken();

      if (!tokenToUse) {
        throw new Error('No refresh token available');
      }

      Logger.debug('Refreshing token');

      const request: RefreshTokenRequest = {
        refreshToken: tokenToUse,
      };

      const httpClient = getHttpClient();

      // Make refresh request without automatic token attachment to avoid infinite loop
      // The refresh endpoint doesn't require an access token, just the refresh token in the body
      const response = await httpClient.post<RefreshTokenResponse>('/api/auth/refresh', request);

      if (response.success && response.tokens) {
        await this.tokenManager.storeTokens(response.tokens);
        Logger.debug('Token refresh successful');
      }

      return response;
    } catch (error) {
      Logger.error('Token refresh failed', error);
      throw error;
    }
  }

  /**
   * Check authentication status
   */
  async checkAuthStatus(): Promise<AuthStatusResponse> {
    try {
      const httpClient = getHttpClient();
      const response = await httpClient.get<AuthStatusResponse>('/api/auth/status');
      
      Logger.debug('Auth status checked', { 
        authenticated: response.authenticated,
        authMethod: response.authMethod 
      });

      return response;
    } catch (error) {
      Logger.error('Auth status check failed', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated locally
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const hasTokens = await this.tokenManager.hasValidTokens();
      const hasUser = await this.userStorage.hasUser();
      
      const isAuth = hasTokens && hasUser;
      Logger.debug('Local auth check', { hasTokens, hasUser, isAuthenticated: isAuth });
      
      return isAuth;
    } catch (error) {
      Logger.error('Local auth check failed', error);
      return false;
    }
  }

  /**
   * Get current user from local storage
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      return await this.userStorage.getUser();
    } catch (error) {
      Logger.error('Failed to get current user', error);
      return null;
    }
  }

  /**
   * Validate current session with server
   */
  async validateSession(): Promise<boolean> {
    try {
      // First check local authentication
      if (!(await this.isAuthenticated())) {
        return false;
      }

      // Then validate with server
      const statusResponse = await this.checkAuthStatus();
      
      if (!statusResponse.authenticated) {
        // Server says we're not authenticated, clear local data
        await this.clearLocalAuth();
        return false;
      }

      // Update user data if provided by server
      if (statusResponse.user) {
        await this.userStorage.storeUser(statusResponse.user);
      }

      return true;
    } catch (error) {
      Logger.error('Session validation failed', error);
      // If validation fails, assume session is invalid
      await this.clearLocalAuth();
      return false;
    }
  }

  /**
   * Clear local authentication data
   */
  async clearLocalAuth(): Promise<void> {
    try {
      await Promise.all([
        this.tokenManager.clearTokens(),
        this.userStorage.clearUser(),
      ]);
      Logger.debug('Local auth data cleared');
    } catch (error) {
      Logger.error('Failed to clear local auth data', error);
    }
  }

  /**
   * Get user role
   */
  async getUserRole(): Promise<UserRole | null> {
    try {
      const user = await this.getCurrentUser();
      return user?.role || null;
    } catch (error) {
      Logger.error('Failed to get user role', error);
      return null;
    }
  }

  /**
   * Check if user has specific role
   */
  async hasRole(role: UserRole): Promise<boolean> {
    try {
      const userRole = await this.getUserRole();
      return userRole === role;
    } catch (error) {
      Logger.error('Failed to check user role', error);
      return false;
    }
  }

  /**
   * Check if user is admin
   */
  async isAdmin(): Promise<boolean> {
    return this.hasRole('admin');
  }

  /**
   * Check if user is teacher
   */
  async isTeacher(): Promise<boolean> {
    return this.hasRole('professeur');
  }

  /**
   * Check if user is student
   */
  async isStudent(): Promise<boolean> {
    return this.hasRole('eleve');
  }

  /**
   * Get authentication headers for manual requests
   */
  async getAuthHeaders(): Promise<Record<string, string>> {
    try {
      const token = await this.tokenManager.getAccessToken();
      
      if (!token) {
        return {};
      }

      return {
        'Authorization': `Bearer ${token}`,
      };
    } catch (error) {
      Logger.error('Failed to get auth headers', error);
      return {};
    }
  }

  /**
   * Force token refresh
   */
  async forceTokenRefresh(): Promise<boolean> {
    try {
      await this.refreshToken();
      return true;
    } catch (error) {
      Logger.error('Force token refresh failed', error);
      return false;
    }
  }
}
