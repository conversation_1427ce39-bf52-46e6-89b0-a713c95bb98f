/**
 * HTTP Client for NS School Manager Mobile App
 * Professional axios-based client with interceptors, retry logic, and token management
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiConfig, ApiError, ApiErrorCode, NetworkError, RequestConfig } from '../types/api';
import { TokenManager } from './tokenManager';
import { Logger } from '../utils/logger';

class HttpClient {
  private client: AxiosInstance;
  private tokenManager: TokenManager;
  private config: ApiConfig;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }> = [];

  constructor(config: ApiConfig) {
    this.config = config;
    this.tokenManager = TokenManager.getInstance();
    this.client = this.createAxiosInstance();
    this.setupInterceptors();
  }

  /**
   * Create axios instance with base configuration
   */
  private createAxiosInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor - Add auth token
    this.client.interceptors.request.use(
      async (config) => {
        // Skip token attachment for refresh requests to avoid infinite loop
        const isRefreshRequest = config.url?.includes('/api/auth/refresh');

        if (!isRefreshRequest) {
          const token = await this.tokenManager.getAccessToken();
          if (token && config.headers) {
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        Logger.debug('HTTP Request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          hasAuth: !isRefreshRequest && !!config.headers?.Authorization,
          isRefreshRequest,
        });

        return config;
      },
      (error) => {
        Logger.error('Request interceptor error', error);
        return Promise.reject(this.createNetworkError(error));
      }
    );

    // Response interceptor - Handle token refresh and errors
    this.client.interceptors.response.use(
      (response) => {
        Logger.debug('HTTP Response', {
          status: response.status,
          url: response.config.url,
          success: response.data?.success,
        });
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors with token refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // Queue the request while refresh is in progress
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then((token) => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.client(originalRequest);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const newToken = await this.tokenManager.refreshToken();
            this.processQueue(null, newToken);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            await this.tokenManager.clearTokens();
            throw this.createNetworkError(error, ApiErrorCode.AUTHENTICATION_ERROR);
          } finally {
            this.isRefreshing = false;
          }
        }

        return Promise.reject(this.createNetworkError(error));
      }
    );
  }

  /**
   * Process queued requests after token refresh
   */
  private processQueue(error: any, token: string | null): void {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });
    this.failedQueue = [];
  }

  /**
   * Create standardized network error
   */
  private createNetworkError(error: AxiosError, code?: ApiErrorCode): NetworkError {
    let errorCode = code || ApiErrorCode.UNKNOWN_ERROR;
    let isRetryable = false;

    if (error.code === 'ECONNABORTED') {
      errorCode = ApiErrorCode.TIMEOUT_ERROR;
      isRetryable = true;
    } else if (error.code === 'NETWORK_ERROR' || !error.response) {
      errorCode = ApiErrorCode.NETWORK_ERROR;
      isRetryable = true;
    } else if (error.response) {
      const status = error.response.status;
      switch (status) {
        case 401:
          errorCode = ApiErrorCode.AUTHENTICATION_ERROR;
          break;
        case 403:
          errorCode = ApiErrorCode.AUTHORIZATION_ERROR;
          break;
        case 404:
          errorCode = ApiErrorCode.NOT_FOUND;
          break;
        case 422:
          errorCode = ApiErrorCode.VALIDATION_ERROR;
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          errorCode = ApiErrorCode.SERVER_ERROR;
          isRetryable = true;
          break;
      }
    }

    const networkError = new Error(error.message) as NetworkError;
    networkError.code = errorCode;
    networkError.status = error.response?.status;
    networkError.response = error.response?.data;
    networkError.isRetryable = isRetryable;

    Logger.error('Network Error', {
      code: errorCode,
      status: error.response?.status,
      message: error.message,
      isRetryable,
    });

    return networkError;
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest<T>(
    config: AxiosRequestConfig,
    requestConfig: RequestConfig = {}
  ): Promise<T> {
    const { retryOnFailure = true } = requestConfig;
    let lastError: NetworkError;

    for (let attempt = 0; attempt <= this.config.retryAttempts; attempt++) {
      try {
        const response: AxiosResponse<T> = await this.client(config);
        return response.data;
      } catch (error) {
        lastError = error as NetworkError;

        // Don't retry if not retryable or if it's the last attempt
        if (!retryOnFailure || !lastError.isRetryable || attempt === this.config.retryAttempts) {
          break;
        }

        // Wait before retrying
        await this.delay(this.config.retryDelay * Math.pow(2, attempt));
        Logger.debug(`Retrying request (attempt ${attempt + 1}/${this.config.retryAttempts})`);
      }
    }

    throw lastError!;
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // ============================================================================
  // Public HTTP Methods
  // ============================================================================

  async get<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>({ method: 'GET', url }, config);
  }

  async post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>({ method: 'POST', url, data }, config);
  }

  async put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>({ method: 'PUT', url, data }, config);
  }

  async patch<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>({ method: 'PATCH', url, data }, config);
  }

  async delete<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.makeRequest<T>({ method: 'DELETE', url }, config);
  }

  /**
   * Upload file with progress tracking
   */
  async upload<T>(
    url: string,
    formData: FormData,
    onProgress?: (progress: number) => void,
    config?: RequestConfig
  ): Promise<T> {
    return this.makeRequest<T>(
      {
        method: 'POST',
        url,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            onProgress(progress);
          }
        },
      },
      config
    );
  }

  /**
   * Download file
   */
  async download(url: string, config?: RequestConfig): Promise<Blob> {
    return this.makeRequest<Blob>(
      {
        method: 'GET',
        url,
        responseType: 'blob',
      },
      config
    );
  }

  /**
   * Get axios instance for custom requests
   */
  getAxiosInstance(): AxiosInstance {
    return this.client;
  }
}

// Singleton instance
let httpClientInstance: HttpClient;

export const createHttpClient = (config: ApiConfig): HttpClient => {
  if (!httpClientInstance) {
    httpClientInstance = new HttpClient(config);
  }
  return httpClientInstance;
};

export const getHttpClient = (): HttpClient => {
  if (!httpClientInstance) {
    throw new Error('HttpClient not initialized. Call createHttpClient first.');
  }
  return httpClientInstance;
};

export { HttpClient };
