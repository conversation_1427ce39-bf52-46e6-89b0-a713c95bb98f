/**
 * User Storage Service for NS School Manager Mobile App
 * Handles secure storage and retrieval of user data
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, StoredUser } from '../types/api';
import { Logger } from '../utils/logger';

export class UserStorage {
  private static instance: UserStorage;
  private static readonly USER_KEY = 'ns_user_data';
  private static readonly LAST_LOGIN_KEY = 'ns_last_login';

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): UserStorage {
    if (!UserStorage.instance) {
      UserStorage.instance = new UserStorage();
    }
    return UserStorage.instance;
  }

  /**
   * Store user data
   */
  async storeUser(user: User): Promise<void> {
    try {
      const storedUser: StoredUser = {
        user,
        lastLogin: new Date().toISOString(),
      };

      await AsyncStorage.setItem(UserStorage.USER_KEY, JSON.stringify(storedUser));
      await AsyncStorage.setItem(UserStorage.LAST_LOGIN_KEY, storedUser.lastLogin);

      Logger.debug('User data stored', { 
        matricule: user.matricule, 
        role: user.role 
      });
    } catch (error) {
      Logger.error('Failed to store user data', error);
      throw new Error('Failed to store user data');
    }
  }

  /**
   * Get stored user data
   */
  async getUser(): Promise<User | null> {
    try {
      const storedData = await AsyncStorage.getItem(UserStorage.USER_KEY);
      
      if (!storedData) {
        return null;
      }

      const parsedData: StoredUser = JSON.parse(storedData);
      return parsedData.user;
    } catch (error) {
      Logger.error('Failed to get user data', error);
      return null;
    }
  }

  /**
   * Get stored user with metadata
   */
  async getStoredUser(): Promise<StoredUser | null> {
    try {
      const storedData = await AsyncStorage.getItem(UserStorage.USER_KEY);
      
      if (!storedData) {
        return null;
      }

      return JSON.parse(storedData);
    } catch (error) {
      Logger.error('Failed to get stored user data', error);
      return null;
    }
  }

  /**
   * Update user data
   */
  async updateUser(updates: Partial<User>): Promise<void> {
    try {
      const currentUser = await this.getUser();
      
      if (!currentUser) {
        throw new Error('No user data to update');
      }

      const updatedUser: User = {
        ...currentUser,
        ...updates,
      };

      await this.storeUser(updatedUser);
      Logger.debug('User data updated', { matricule: updatedUser.matricule });
    } catch (error) {
      Logger.error('Failed to update user data', error);
      throw error;
    }
  }

  /**
   * Check if user data exists
   */
  async hasUser(): Promise<boolean> {
    try {
      const user = await this.getUser();
      return user !== null;
    } catch (error) {
      Logger.error('Failed to check user existence', error);
      return false;
    }
  }

  /**
   * Clear user data
   */
  async clearUser(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(UserStorage.USER_KEY),
        AsyncStorage.removeItem(UserStorage.LAST_LOGIN_KEY),
      ]);

      Logger.debug('User data cleared');
    } catch (error) {
      Logger.error('Failed to clear user data', error);
      // Don't throw error as clearing should always succeed
    }
  }

  /**
   * Get last login time
   */
  async getLastLogin(): Promise<Date | null> {
    try {
      const lastLogin = await AsyncStorage.getItem(UserStorage.LAST_LOGIN_KEY);
      
      if (!lastLogin) {
        return null;
      }

      return new Date(lastLogin);
    } catch (error) {
      Logger.error('Failed to get last login time', error);
      return null;
    }
  }

  /**
   * Get user matricule
   */
  async getUserMatricule(): Promise<string | null> {
    try {
      const user = await this.getUser();
      return user?.matricule || null;
    } catch (error) {
      Logger.error('Failed to get user matricule', error);
      return null;
    }
  }

  /**
   * Get user role
   */
  async getUserRole(): Promise<string | null> {
    try {
      const user = await this.getUser();
      return user?.role || null;
    } catch (error) {
      Logger.error('Failed to get user role', error);
      return null;
    }
  }

  /**
   * Get user full name
   */
  async getUserFullName(): Promise<string | null> {
    try {
      const user = await this.getUser();
      
      if (!user) {
        return null;
      }

      if (user.nom && user.prenom) {
        return `${user.prenom} ${user.nom}`;
      }

      return user.nom || user.prenom || user.matricule;
    } catch (error) {
      Logger.error('Failed to get user full name', error);
      return null;
    }
  }

  /**
   * Check if user is admin
   */
  async isAdmin(): Promise<boolean> {
    try {
      const role = await this.getUserRole();
      return role === 'admin';
    } catch (error) {
      Logger.error('Failed to check admin status', error);
      return false;
    }
  }

  /**
   * Check if user is teacher
   */
  async isTeacher(): Promise<boolean> {
    try {
      const role = await this.getUserRole();
      return role === 'professeur';
    } catch (error) {
      Logger.error('Failed to check teacher status', error);
      return false;
    }
  }

  /**
   * Check if user is student
   */
  async isStudent(): Promise<boolean> {
    try {
      const role = await this.getUserRole();
      return role === 'eleve';
    } catch (error) {
      Logger.error('Failed to check student status', error);
      return false;
    }
  }

  /**
   * Export user data (for debugging or backup)
   */
  async exportUserData(): Promise<StoredUser | null> {
    try {
      return await this.getStoredUser();
    } catch (error) {
      Logger.error('Failed to export user data', error);
      return null;
    }
  }

  /**
   * Import user data (for restore or migration)
   */
  async importUserData(userData: StoredUser): Promise<void> {
    try {
      await AsyncStorage.setItem(UserStorage.USER_KEY, JSON.stringify(userData));
      await AsyncStorage.setItem(UserStorage.LAST_LOGIN_KEY, userData.lastLogin);

      Logger.debug('User data imported', { 
        matricule: userData.user.matricule, 
        role: userData.user.role 
      });
    } catch (error) {
      Logger.error('Failed to import user data', error);
      throw error;
    }
  }
}
