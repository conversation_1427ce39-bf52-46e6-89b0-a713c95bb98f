/**
 * Dashboard Service - role specific data fetchers
 * Uses existing HttpClient singleton and API_CONFIG endpoints
 */

import { getHttpClient } from './httpClient';
import { API_ENDPOINTS } from '../config/api';
import { ApiResponse, EmploiTemps, Note, Devoir, Message } from '../types/api';
import {
  AdminSummary,
  MessageSummary,
  TimetableEntry,
  DevoirSummary,
  NoteSummary,
  AnnonceSummary,
} from '../types/dashboard';

// Utility safe getters
const safeNumber = (v: any, def = 0): number => (typeof v === 'number' && !isNaN(v) ? v : parseInt(v, 10)) || def;
const safeString = (v: any, def = 'N/A'): string => (typeof v === 'string' && v.trim() ? v : def);

// Map backend items to dashboard minimal summaries
const mapMessageToSummary = (m: any): MessageSummary => ({
  id: m.message_id ?? m.id ?? m._id ?? Math.random().toString(36).slice(2),
  title: safeString(m.subject || m.contenu || m.title, 'Message'),
  from: m.sender_name || m.sender_matricule || m.from || undefined,
  createdAt: m.date_envoi || m.createdAt || m.created_at || undefined,
});

const mapEmploiToEntry = (e: any): TimetableEntry => ({
  id: e.emploi_id ?? e.id ?? Math.random().toString(36).slice(2),
  subject: safeString(e.matiere || e.titre || e.subject || e.nom_matiere, 'Cours'),
  start: e.heure_debut || e.start || '',
  end: e.heure_fin || e.end || '',
  room: e.salle || e.room || undefined,
});

const mapDevoirToSummary = (d: any): DevoirSummary => ({
  id: d.devoir_id ?? d.id ?? Math.random().toString(36).slice(2),
  title: safeString(d.titre || d.title, 'Devoir'),
  dueDate: d.date_limite || d.dueDate || d.deadline || undefined,
  status: d.status || d.etat || undefined,
});

const mapNoteToSummary = (n: any): NoteSummary => ({
  id: n.note_id ?? n.id ?? Math.random().toString(36).slice(2),
  subject: safeString(n.nom_matiere || n.subject || String(n.matiere_id || ''), 'Note'),
  value: typeof n.note === 'number' ? n.note : Number(n.value ?? 0),
  coef: typeof n.coefficient === 'number' ? n.coefficient : n.coef,
  createdAt: n.date_evaluation || n.createdAt || undefined,
});

const mapAnnonceToSummary = (a: any): AnnonceSummary => ({
  id: a.message_id ?? a.id ?? Math.random().toString(36).slice(2),
  title: safeString(a.subject || a.contenu || a.title, 'Annonce'),
  createdAt: a.date_envoi || a.createdAt || undefined,
});

class DashboardService {
  private getHttp() {
    return getHttpClient();
  }

  // ========== Admin ==========
  async getAdminSummary(): Promise<AdminSummary> {
    // Use known endpoints and minimal requests; if any fail, keep defaults
    let classes = 0;
    let profs = 0;
    let eleves = 0;
    let messages = 0;

    try {
      const res = await this.getHttp().get<ApiResponse<any[]>>(API_ENDPOINTS.ACADEMIC.CLASSES);
      classes = safeNumber(res?.data?.length ?? 0, 0);
    } catch {}

    try {
      const res = await this.getHttp().get<ApiResponse<any[]>>(API_ENDPOINTS.USERS.PROFESSEURS);
      profs = safeNumber(res?.data?.length ?? 0, 0);
    } catch {}

    try {
      const res = await this.getHttp().get<ApiResponse<any[]>>(API_ENDPOINTS.USERS.ELEVES);
      eleves = safeNumber(res?.data?.length ?? 0, 0);
    } catch {}

    try {
      const res = await this.getHttp().get<ApiResponse<any[]>>(API_ENDPOINTS.COMMUNICATION.MESSAGES);
      // Some endpoints return conversations instead of messages, still count array length
      const arr = (res as any)?.messages || res?.data || [];
      messages = safeNumber(Array.isArray(arr) ? arr.length : 0, 0);
    } catch {}

    return { classes, profs, eleves, messages };
  }

  async getRecentMessages(limit = 5): Promise<MessageSummary[]> {
    try {
      const res = await this.getHttp().get<any>(API_ENDPOINTS.COMMUNICATION.MESSAGES);
      const list = (res as any)?.messages || (res as any)?.data || (Array.isArray(res) ? res : []);
      const mapped = (Array.isArray(list) ? list : []).map(mapMessageToSummary);
      return mapped.slice(0, limit);
    } catch (e: any) {
      const serverMessage = e?.response?.message || e?.message;
      throw new Error(serverMessage || 'Erreur lors du chargement des messages');
    }
  }

  // ========== Professeur ==========
  async getProfTodayEmploiTemps(): Promise<TimetableEntry[]> {
    // Prefer unified emploi endpoints
    // Fallback to API endpoints available
    try {
      // Try generic schedule endpoint which likely requires filters by day - we will use planning-jour with current day
      const day = this.getCurrentFrenchDay();
      const res = await this.getHttp().get<ApiResponse<any[]>>(`/api/emploi_temps/planning-jour/${encodeURIComponent(day)}`);
      const data = res?.data || [];
      const today = data.filter((e: any) => !e.professeur_matricule || e.professeur_matricule); // no filter without context
      return today.map(mapEmploiToEntry);
    } catch {
      // Fallback: use base list if available
      try {
        const res = await this.getHttp().get<ApiResponse<EmploiTemps[]>>(API_ENDPOINTS.SCHEDULE.EMPLOI_TEMPS_PROFESSEUR);
        const data = res?.data || [];
        const day = this.getCurrentFrenchDay();
        const filtered = data.filter((e: any) => (e.jour ? e.jour.toLowerCase() === day : true));
        return filtered.map(mapEmploiToEntry);
      } catch (e: any) {
        const serverMessage = e?.response?.message || e?.message;
        throw new Error(serverMessage || 'Erreur lors du chargement de l’emploi du temps');
      }
    }
  }

  async getProfPendingDevoir(): Promise<DevoirSummary[]> {
    // Use the correct API endpoint for professor devoir
    try {
      const res = await this.getHttp().get<any>(API_ENDPOINTS.ACADEMIC.DEVOIRS);
      const responseData = res?.data || res || {};
      const list = responseData?.devoirs || responseData?.data || [];
      return (Array.isArray(list) ? list : []).map(mapDevoirToSummary).filter(d => (d.status ?? '').toLowerCase() !== 'done');
    } catch (e: any) {
      const serverMessage = e?.response?.message || e?.message;
      throw new Error(serverMessage || 'Erreur lors du chargement des devoirs');
    }
  }

  async getProfRecentMessages(limit = 5): Promise<MessageSummary[]> {
    try {
      // Use the conversations endpoint which handles role-based filtering
      const res = await this.getHttp().get<any>(API_ENDPOINTS.COMMUNICATION.CONVERSATIONS);
      const responseData = res?.data || res || {};
      const conversations = responseData?.conversations || [];
      const mapped = (Array.isArray(conversations) ? conversations : []).map(mapMessageToSummary);
      return mapped.slice(0, limit);
    } catch (e: any) {
      const serverMessage = e?.response?.message || e?.message;
      throw new Error(serverMessage || 'Erreur lors du chargement des messages');
    }
  }

  // ========== Eleve ==========
  async getEleveTodayEmploiTemps(): Promise<TimetableEntry[]> {
    try {
      // Use the student-specific endpoint that gets schedule based on their class
      const res = await this.getHttp().get<any>(API_ENDPOINTS.SCHEDULE.EMPLOI_TEMPS_ELEVE);
      const responseData = res?.data || res || {};
      const emploiTemps = responseData?.emploiTemps || responseData?.data || [];
      const day = this.getCurrentFrenchDay();
      const filtered = (Array.isArray(emploiTemps) ? emploiTemps : []).filter((e: any) =>
        (e.jour_semaine || e.jour || '').toLowerCase() === day.toLowerCase()
      );
      return filtered.map(mapEmploiToEntry);
    } catch (e: any) {
      const serverMessage = e?.response?.message || e?.message;
      throw new Error(serverMessage || 'Erreur lors du chargement de l’emploi du temps');
    }
  }

  async getEleveLatestNotes(limit = 3): Promise<NoteSummary[]> {
    try {
      const res = await this.getHttp().get<any>(API_ENDPOINTS.ACADEMIC.NOTES);
      const responseData = res?.data || res || {};
      const list = responseData?.notes || [];
      const mapped = (Array.isArray(list) ? list : []).map(mapNoteToSummary);
      // Sort by date_evaluation desc if present
      mapped.sort((a, b) => (new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()));
      return mapped.slice(0, limit);
    } catch (e: any) {
      const serverMessage = e?.response?.message || e?.message;
      throw new Error(serverMessage || 'Erreur lors du chargement des notes');
    }
  }

  async getEleveLatestAnnonces(limit = 3): Promise<AnnonceSummary[]> {
    try {
      // Announcements are messages with type 'annonce'; using conversations endpoint
      const res = await this.getHttp().get<any>(API_ENDPOINTS.COMMUNICATION.CONVERSATIONS);
      const responseData = res?.data || res || {};
      const conversations = responseData?.conversations || [];
      const annonces = (Array.isArray(conversations) ? conversations : []).filter((m: any) => (m.type || '').toLowerCase() === 'annonce');
      const mapped = annonces.map(mapAnnonceToSummary);
      mapped.sort((a, b) => (new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()));
      return mapped.slice(0, limit);
    } catch (e: any) {
      const serverMessage = e?.response?.message || e?.message;
      throw new Error(serverMessage || 'Erreur lors du chargement des annonces');
    }
  }

  // Helpers
  private getCurrentFrenchDay(): string {
    const days = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
    const d = new Date();
    return days[d.getDay()];
  }
}

export const dashboardService = new DashboardService();
export type { AdminSummary, MessageSummary, TimetableEntry, DevoirSummary, NoteSummary, AnnonceSummary };