import type { CreateSalleDto, FormO<PERSON>s, Salle, SalleStats, UpdateSalleDto } from '../types/salles';
import { getHttpClient } from './httpClient';
import { API_ENDPOINTS } from '../config/api';

/**
 * Align with the authenticated HttpClient used across the app so
 * baseURL, auth headers, and interceptors are applied consistently.
 *
 * If API_ENDPOINTS has a Salles section, use those constants.
 * Otherwise, fall back to RESTful paths under /api/salles.
 */
const http = () => getHttpClient();

// Prefer configured endpoints if present; otherwise fallback paths
const ENDPOINTS = {
  LIST: (API_ENDPOINTS as any)?.SALLE?.BASE || '/api/salles',
  ITEM: (id: number | string) => ((API_ENDPOINTS as any)?.SALLE?.ITEM?.(id) || `/api/salles/${id}`),
  STATS: ((API_ENDPOINTS as any)?.SALLE?.STATS || '/api/salles/stats'),
  BUILDINGS: ((API_ENDPOINTS as any)?.SALLE?.BUILDINGS || '/api/salles/buildings'),
  OPTIONS_FORM: ((API_ENDPOINTS as any)?.SALLE?.OPTIONS_FORM || '/api/salles/options/form'),
};

// Helper function to map backend salle_id to frontend id
const mapSalleResponse = (salle: any): Salle => ({
  ...salle,
  id: salle.salle_id || salle.id, // Map salle_id to id for frontend compatibility
});

export const sallesService = {
  async getSalles(params?: { type?: string; batiment?: string; q?: string; page?: number; limit?: number }): Promise<Salle[] | { data: Salle[]; pagination?: { page: number; limit: number; total: number } }> {
    // The shared HttpClient does not accept an axios-like { params } option.
    // Manually serialize query parameters into the URL.
    const query = params
      ? `?${new URLSearchParams(Object.entries(params).reduce((acc: Record<string, string>, [k, v]) => {
          if (v !== undefined && v !== null && String(v).length > 0) acc[k] = String(v);
          return acc;
        }, {})).toString()}`
      : '';
    const res = await http().get<any>(`${ENDPOINTS.LIST}${query}`);
    // Support both { data } and array payloads
    const rawData = res?.data ?? res ?? [];

    // Map salle_id to id for all items
    if (Array.isArray(rawData)) {
      return rawData.map(mapSalleResponse);
    } else if (rawData.data && Array.isArray(rawData.data)) {
      return {
        ...rawData,
        data: rawData.data.map(mapSalleResponse)
      };
    }
    return rawData;
  },

  async getSalleById(id: number | string): Promise<Salle> {
    const res = await http().get<any>(ENDPOINTS.ITEM(id));
    const rawData = res?.data ?? res;
    return mapSalleResponse(rawData);
  },

  async createSalle(payload: CreateSalleDto): Promise<Salle> {
    const res = await http().post<any>(ENDPOINTS.LIST, payload);
    const rawData = res?.data ?? res;
    return mapSalleResponse(rawData);
  },

  async updateSalle(id: number | string, payload: UpdateSalleDto): Promise<Salle> {
    const res = await http().put<any>(ENDPOINTS.ITEM(id), payload);
    const rawData = res?.data ?? res;
    return mapSalleResponse(rawData);
  },

  async deleteSalle(id: number | string): Promise<{ success: boolean }> {
    const res = await http().delete<any>(ENDPOINTS.ITEM(id));
    const data = res?.data ?? res;
    return typeof data === 'object' && data ? data : { success: true };
  },

  async getSalleStats(): Promise<SalleStats> {
    const res = await http().get<any>(ENDPOINTS.STATS);
    return res?.data ?? res;
  },

  async getBuildings(): Promise<string[]> {
    const res = await http().get<any>(ENDPOINTS.BUILDINGS);
    return res?.data ?? res ?? [];
  },

  async getFormOptions(): Promise<FormOptions> {
    const res = await http().get<any>(ENDPOINTS.OPTIONS_FORM);
    return res?.data ?? res;
  },
};