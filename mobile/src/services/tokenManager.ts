/**
 * Token Manager for NS School Manager Mobile App
 * Secure JWT token storage and management using Expo SecureStore
 */

import * as SecureStore from 'expo-secure-store';
import { StoredTokens, AuthTokens } from '../types/api';
import { Logger } from '../utils/logger';

export class TokenManager {
  private static instance: TokenManager;
  private static readonly ACCESS_TOKEN_KEY = 'ns_access_token';
  private static readonly REFRESH_TOKEN_KEY = 'ns_refresh_token';
  private static readonly TOKEN_EXPIRY_KEY = 'ns_token_expiry';

  private isRefreshing = false;
  private refreshPromise: Promise<string> | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Store authentication tokens securely
   */
  async storeTokens(tokens: AuthTokens): Promise<void> {
    try {
      const expiresAt = this.calculateTokenExpiry(tokens.accessToken);
      
      await Promise.all([
        SecureStore.setItemAsync(TokenManager.ACCESS_TOKEN_KEY, tokens.accessToken),
        SecureStore.setItemAsync(TokenManager.REFRESH_TOKEN_KEY, tokens.refreshToken),
        SecureStore.setItemAsync(TokenManager.TOKEN_EXPIRY_KEY, expiresAt.toString()),
      ]);

      Logger.debug('Tokens stored successfully', { expiresAt: new Date(expiresAt) });
    } catch (error) {
      Logger.error('Failed to store tokens', error);
      throw new Error('Failed to store authentication tokens');
    }
  }

  /**
   * Get access token if valid, otherwise attempt refresh
   */
  async getAccessToken(): Promise<string | null> {
    try {
      const accessToken = await SecureStore.getItemAsync(TokenManager.ACCESS_TOKEN_KEY);

      if (!accessToken) {
        Logger.debug('No access token found');
        return null;
      }

      // Check if token is expired
      if (await this.isTokenExpired()) {
        Logger.debug('Access token expired, attempting refresh');

        // If already refreshing, wait for the existing refresh to complete
        if (this.isRefreshing && this.refreshPromise) {
          try {
            return await this.refreshPromise;
          } catch (error) {
            Logger.warn('Waiting for refresh failed', error);
            return null;
          }
        }

        try {
          const newToken = await this.refreshToken();
          return newToken;
        } catch (error) {
          Logger.warn('Token refresh failed', error);
          await this.clearTokens();
          return null;
        }
      }

      return accessToken;
    } catch (error) {
      Logger.error('Failed to get access token', error);
      return null;
    }
  }

  /**
   * Get refresh token
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(TokenManager.REFRESH_TOKEN_KEY);
    } catch (error) {
      Logger.error('Failed to get refresh token', error);
      return null;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(): Promise<string> {
    // If already refreshing, return the existing promise
    if (this.isRefreshing && this.refreshPromise) {
      return this.refreshPromise;
    }

    // Set refreshing flag and create promise
    this.isRefreshing = true;
    this.refreshPromise = this.performRefresh();

    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performRefresh(): Promise<string> {
    try {
      const refreshToken = await this.getRefreshToken();

      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      // Import here to avoid circular dependency
      const { AuthService } = await import('./authService');
      const authService = AuthService.getInstance();

      const response = await authService.refreshToken(refreshToken);

      if (response.tokens) {
        await this.storeTokens(response.tokens);
        Logger.debug('Token refreshed successfully');
        return response.tokens.accessToken;
      }

      throw new Error('Invalid refresh response');
    } catch (error) {
      Logger.error('Token refresh failed', error);
      throw error;
    }
  }

  /**
   * Check if access token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      const expiryString = await SecureStore.getItemAsync(TokenManager.TOKEN_EXPIRY_KEY);
      
      if (!expiryString) {
        return true;
      }

      const expiryTime = parseInt(expiryString, 10);
      const currentTime = Date.now();
      
      // Consider token expired if it expires within the next 5 minutes
      const bufferTime = 5 * 60 * 1000; // 5 minutes in milliseconds
      
      return currentTime >= (expiryTime - bufferTime);
    } catch (error) {
      Logger.error('Failed to check token expiry', error);
      return true;
    }
  }

  /**
   * Check if user has valid tokens
   */
  async hasValidTokens(): Promise<boolean> {
    try {
      const accessToken = await SecureStore.getItemAsync(TokenManager.ACCESS_TOKEN_KEY);
      const refreshToken = await SecureStore.getItemAsync(TokenManager.REFRESH_TOKEN_KEY);
      
      if (!accessToken || !refreshToken) {
        return false;
      }

      // If access token is expired, check if refresh token is available
      if (await this.isTokenExpired()) {
        return !!refreshToken;
      }

      return true;
    } catch (error) {
      Logger.error('Failed to check token validity', error);
      return false;
    }
  }

  /**
   * Clear all stored tokens
   */
  async clearTokens(): Promise<void> {
    try {
      await Promise.all([
        SecureStore.deleteItemAsync(TokenManager.ACCESS_TOKEN_KEY),
        SecureStore.deleteItemAsync(TokenManager.REFRESH_TOKEN_KEY),
        SecureStore.deleteItemAsync(TokenManager.TOKEN_EXPIRY_KEY),
      ]);

      Logger.debug('All tokens cleared');
    } catch (error) {
      Logger.error('Failed to clear tokens', error);
      // Don't throw error here as clearing tokens should always succeed
    }
  }

  /**
   * Get all stored tokens (for debugging or migration)
   */
  async getAllTokens(): Promise<StoredTokens | null> {
    try {
      const [accessToken, refreshToken, expiryString] = await Promise.all([
        SecureStore.getItemAsync(TokenManager.ACCESS_TOKEN_KEY),
        SecureStore.getItemAsync(TokenManager.REFRESH_TOKEN_KEY),
        SecureStore.getItemAsync(TokenManager.TOKEN_EXPIRY_KEY),
      ]);

      if (!accessToken || !refreshToken || !expiryString) {
        return null;
      }

      return {
        accessToken,
        refreshToken,
        expiresAt: parseInt(expiryString, 10),
      };
    } catch (error) {
      Logger.error('Failed to get all tokens', error);
      return null;
    }
  }

  /**
   * Calculate token expiry time from JWT
   */
  private calculateTokenExpiry(token: string): number {
    try {
      // Decode JWT payload (without verification since we trust our backend)
      const payload = JSON.parse(atob(token.split('.')[1]));
      
      if (payload.exp) {
        // JWT exp is in seconds, convert to milliseconds
        return payload.exp * 1000;
      }
      
      // Fallback: assume 24 hours from now
      return Date.now() + (24 * 60 * 60 * 1000);
    } catch (error) {
      Logger.warn('Failed to decode token expiry, using fallback', error);
      // Fallback: assume 24 hours from now
      return Date.now() + (24 * 60 * 60 * 1000);
    }
  }

  /**
   * Get token expiry time
   */
  async getTokenExpiry(): Promise<Date | null> {
    try {
      const expiryString = await SecureStore.getItemAsync(TokenManager.TOKEN_EXPIRY_KEY);
      
      if (!expiryString) {
        return null;
      }

      return new Date(parseInt(expiryString, 10));
    } catch (error) {
      Logger.error('Failed to get token expiry', error);
      return null;
    }
  }

  /**
   * Get time until token expires (in milliseconds)
   */
  async getTimeUntilExpiry(): Promise<number | null> {
    try {
      const expiry = await this.getTokenExpiry();
      
      if (!expiry) {
        return null;
      }

      return expiry.getTime() - Date.now();
    } catch (error) {
      Logger.error('Failed to calculate time until expiry', error);
      return null;
    }
  }

  /**
   * Check if refresh token is expired (basic check)
   */
  async isRefreshTokenExpired(): Promise<boolean> {
    try {
      const refreshToken = await this.getRefreshToken();
      
      if (!refreshToken) {
        return true;
      }

      // Decode refresh token to check expiry
      const payload = JSON.parse(atob(refreshToken.split('.')[1]));
      
      if (payload.exp) {
        const expiryTime = payload.exp * 1000; // Convert to milliseconds
        return Date.now() >= expiryTime;
      }

      // If no expiry in token, assume it's valid
      return false;
    } catch (error) {
      Logger.error('Failed to check refresh token expiry', error);
      return true;
    }
  }
}
