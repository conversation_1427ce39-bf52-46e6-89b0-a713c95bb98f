import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
  useColorScheme,
  Platform,
} from 'react-native';

export interface SelectOption {
  label: string;
  value: string;
}

interface SelectProps {
  options: SelectOption[];
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  style?: any;
  disabled?: boolean;
}

export default function Select({
  options,
  value,
  onValueChange,
  placeholder = 'Sélectionner...',
  label,
  style,
  disabled = false,
}: SelectProps) {
  const [modalVisible, setModalVisible] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Theme tokens aligned with the app palette used in dashboards
  const colors = useMemo(() => {
    return {
      text: isDark ? '#E2E8F0' : '#0F172A',
      textMuted: isDark ? '#94A3B8' : '#64748B',
      subText: isDark ? '#CBD5E1' : '#334155',
      bgSurface: isDark ? 'rgba(255,255,255,0.04)' : 'rgba(15,23,42,0.02)',
      bgElevated: isDark ? '#0f172a' : '#ffffff',
      overlay: 'rgba(0,0,0,0.5)',
      border: isDark ? 'rgba(148,163,184,0.22)' : 'rgba(15,23,42,0.08)',
      borderSoft: isDark ? 'rgba(148,163,184,0.18)' : 'rgba(15,23,42,0.06)',
      primary: isDark ? '#3B82F6' : '#2563EB',
      primarySoft: isDark ? 'rgba(59,130,246,0.3)' : 'rgba(37,99,235,0.1)',
      shadowColor: '#000',
    };
  }, [isDark]);

  const selectedOption = options.find(option => option.value === value);

  const handleSelect = (selectedValue: string) => {
    onValueChange(selectedValue);
    setModalVisible(false);
  };

  const renderOption = ({ item }: { item: SelectOption }) => {
    const selected = item.value === value;
    return (
      <TouchableOpacity
        style={[
          styles.option,
          {
            backgroundColor: selected ? colors.primarySoft : 'transparent',
            borderBottomColor: colors.borderSoft,
          }
        ]}
        onPress={() => handleSelect(item.value)}
        accessibilityRole="button"
        accessibilityLabel={`Sélectionner ${item.label}`}
      >
        <Text
          style={[
            styles.optionText,
            {
              color: colors.text,
              fontWeight: selected ? '700' : '400',
            }
          ]}
        >
          {item.label}
        </Text>
        {selected && (
          <Text style={[styles.checkmark, { color: colors.primary }]}>
            ✓
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={style}>
      {label && (
        <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>
          {label}
        </Text>
      )}
      
      <TouchableOpacity
        style={[
          styles.selectButton,
          {
            borderColor: colors.border,
            backgroundColor: disabled ? (isDark ? 'rgba(148,163,184,0.10)' : 'rgba(15,23,42,0.04)') : colors.bgSurface,
            opacity: disabled ? 0.6 : 1,
            shadowColor: colors.shadowColor,
            shadowOpacity: isDark ? 0.18 : 0.08,
            shadowRadius: 10,
            shadowOffset: { width: 0, height: 6 },
            elevation: Platform.OS === 'android' ? 3 : 0,
          }
        ]}
        onPress={() => !disabled && setModalVisible(true)}
        disabled={disabled}
        accessibilityRole="button"
        accessibilityLabel={`${label || 'Select'}: ${selectedOption?.label || placeholder}`}
      >
        <Text
          style={[
            styles.selectText,
            {
              color: selectedOption ? colors.text : colors.textMuted,
            }
          ]}
        >
          {selectedOption?.label || placeholder}
        </Text>
        <Text style={[styles.arrow, { color: colors.textMuted }]}>
          ▼
        </Text>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={[styles.modalOverlay, { backgroundColor: colors.overlay }]}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View
            style={[
              styles.modalContent,
              {
                backgroundColor: colors.bgElevated,
                borderColor: colors.border,
                shadowColor: colors.shadowColor,
                shadowOpacity: isDark ? 0.22 : 0.12,
                shadowRadius: 16,
                shadowOffset: { width: 0, height: 10 },
                elevation: Platform.OS === 'android' ? 6 : 0,
              }
            ]}
          >
            <View style={[styles.modalHeader, { borderBottomColor: colors.borderSoft }]}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {label || 'Sélectionner une option'}
              </Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
                accessibilityRole="button"
                accessibilityLabel="Fermer"
              >
                <Text style={[styles.closeText, { color: colors.textMuted }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <FlatList
              data={options}
              renderItem={renderOption}
              keyExtractor={(item) => item.value}
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  label: {
    fontSize: 12,
    fontWeight: '700',
    marginBottom: 6,
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 44,
  },
  selectText: {
    flex: 1,
    fontSize: 16,
  },
  arrow: {
    fontSize: 12,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 420,
    maxHeight: '70%',
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '800',
    letterSpacing: 0.2,
    flex: 1,
  },
  closeButton: {
    padding: 6,
    borderRadius: 10,
  },
  closeText: {
    fontSize: 18,
    fontWeight: '800',
  },
  optionsList: {
    maxHeight: 320,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  optionText: {
    fontSize: 16,
    flex: 1,
  },
  checkmark: {
    fontSize: 16,
    fontWeight: '800',
    marginLeft: 8,
  },
});
