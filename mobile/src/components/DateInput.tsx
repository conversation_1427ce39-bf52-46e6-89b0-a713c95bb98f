import React, { useMemo, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, useColorScheme } from 'react-native';
import DateTimePickerModal from 'react-native-modal-datetime-picker';

export interface DateInputProps {
  label?: string;
  value?: string; // expected format yyyy-MM-dd
  onChange?: (yyyyMmDd: string) => void;
  placeholder?: string;
  required?: boolean;
  style?: any;
  inputStyle?: any;
  accessibilityLabel?: string;
  minimumDate?: Date;
  maximumDate?: Date;
  mode?: 'date'; // we keep only date for now for simplicity
}

function formatYyyyMmDd(d: Date) {
  const yyyy = d.getFullYear();
  const mm = String(d.getMonth() + 1).padStart(2, '0');
  const dd = String(d.getDate()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd}`;
}

function parseFromValueOrToday(value?: string) {
  if (value && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
    const [y, m, d] = value.split('-').map((n) => Number(n));
    const dt = new Date();
    dt.setFullYear(y);
    dt.setMonth(Math.max(0, Math.min(11, (m || 1) - 1)));
    dt.setDate(Math.max(1, Math.min(31, d || 1)));
    return dt;
  }
  return new Date();
}

export default function DateInput({
  label = 'Date',
  value,
  onChange,
  placeholder = 'YYYY-MM-DD',
  required,
  style,
  inputStyle,
  accessibilityLabel,
  minimumDate,
  maximumDate,
}: DateInputProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [open, setOpen] = useState(false);

  const initial = useMemo(() => parseFromValueOrToday(value), [value]);

  const borderColor = isDark ? 'rgba(148,163,184,0.25)' : 'rgba(15,23,42,0.12)';

  const handleConfirm = (date: Date) => {
    onChange?.(formatYyyyMmDd(date));
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <View style={style}>
      {label ? (
        <Text style={[styles.label, { color: isDark ? '#CBD5E1' : '#334155' }]}>
          {label}{required ? ' *' : ''}
        </Text>
      ) : null}

      <TouchableOpacity
        onPress={() => setOpen(true)}
        style={[styles.input, { borderColor }, inputStyle, { justifyContent: 'center' }]}
        accessibilityRole="button"
        accessibilityLabel={accessibilityLabel || 'Choisir la date'}
      >
        <Text style={{ color: isDark ? '#E2E8F0' : '#0F172A' }}>
          {value && /^\d{4}-\d{2}-\d{2}$/.test(value) ? value : placeholder}
        </Text>
      </TouchableOpacity>

      <DateTimePickerModal
        isVisible={open}
        mode="date"
        date={initial}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        minimumDate={minimumDate}
        maximumDate={maximumDate}
        themeVariant={isDark ? 'dark' : 'light'}
        display="inline"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  label: { fontSize: 12, fontWeight: '700', marginTop: 8, marginBottom: 6 },
  input: { borderWidth: 1, borderRadius: 12, paddingHorizontal: 12, paddingVertical: 10 },
});