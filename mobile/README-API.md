# NS School Manager Mobile App - API Integration Guide

## 🚀 Quick Start

### 1. Environment Setup

Copy the environment template and configure your API settings:

```bash
cp .env.example .env
```

Edit `.env` with your backend URL:

```env
# API Configuration (EXPO_PUBLIC_ prefix required)
EXPO_PUBLIC_API_BASE_URL=http://localhost:8080
EXPO_PUBLIC_API_TIMEOUT=30000

# Feature Flags
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_ENABLE_LOGGING=true
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start the Development Server

```bash
npx expo start
```

## 📱 Navigation

The app now includes three main tabs:

1. **Home** - Welcome screen with app overview
2. **Explore** - General exploration features
3. **API Test** - Complete API testing interface

## 🧪 Testing API Integration

Navigate to the **"API Test"** tab to:

- ✅ Test JWT authentication (student, teacher, admin)
- ✅ Verify token refresh functionality
- ✅ Test API endpoint communication
- ✅ Check error handling
- ✅ Validate session management

### Default Test Credentials

From the backend seed data:

**Admin:**
- Matricule: `ADM001`
- Password: `admin123`

**Student:**
- Matricule: `ELV001`
- Password: `eleve123`

**Teacher:**
- Matricule: `PROF001`
- Password: `prof123`

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `EXPO_PUBLIC_API_BASE_URL` | Backend API URL | ✅ Yes |
| `EXPO_PUBLIC_API_TIMEOUT` | Request timeout (ms) | ✅ Yes |
| `EXPO_PUBLIC_DEBUG_MODE` | Enable debug logging | ❌ No |
| `EXPO_PUBLIC_ENABLE_LOGGING` | Enable detailed logging | ❌ No |

### API Configuration

The API configuration is automatically loaded from environment variables in `src/config/api.ts`:

```typescript
// Modern Expo environment variables - NO fallbacks, fail fast if not configured
const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;
const API_TIMEOUT = process.env.EXPO_PUBLIC_API_TIMEOUT;

// Validate required environment variables
if (!API_BASE_URL) {
  throw new Error('EXPO_PUBLIC_API_BASE_URL is required in .env file');
}

export const API_CONFIG: ApiConfig = {
  baseURL: API_BASE_URL,
  timeout: parseInt(API_TIMEOUT, 10),
  retryAttempts: 3,
  retryDelay: 1000,
};
```

## 🏗️ Architecture

### Service Layer

- **`src/services/httpClient.ts`** - Professional HTTP client with interceptors
- **`src/services/authService.ts`** - Authentication management
- **`src/services/tokenManager.ts`** - Secure JWT token storage
- **`src/services/apiService.ts`** - API endpoint services
- **`src/contexts/AuthContext.tsx`** - Global authentication state

### Type Safety

- **`src/types/api.ts`** - Comprehensive TypeScript types
- **`types/env.d.ts`** - Environment variable types

### Error Handling

- **`src/utils/errorHandler.ts`** - Centralized error management
- **`src/utils/logger.ts`** - Professional logging system

## 🔐 Authentication Flow

### Mobile Authentication

1. User enters credentials in API Test screen
2. App sends login request with `api: true` flag
3. Backend validates and returns JWT tokens
4. Tokens are stored securely using Expo SecureStore
5. Subsequent requests include `Authorization: Bearer <token>` header

### Token Management

- **Access tokens** expire in 24 hours (configurable)
- **Refresh tokens** expire in 7 days
- **Automatic refresh** before token expiration
- **Secure storage** using Expo SecureStore

## 🛠️ Development

### Adding New API Endpoints

1. Add types to `src/types/api.ts`
2. Add endpoint to `src/config/api.ts`
3. Create service method in appropriate service class
4. Use in components via service instances

### Environment-Specific Configuration

**Development:**
```env
EXPO_PUBLIC_API_BASE_URL=http://localhost:8080
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_ENABLE_LOGGING=true
```

**Production:**
```env
EXPO_PUBLIC_API_BASE_URL=https://your-production-api.com
EXPO_PUBLIC_DEBUG_MODE=false
EXPO_PUBLIC_ENABLE_LOGGING=false
```

## 🐛 Troubleshooting

### Common Issues

1. **Environment variables not loading**
   - Restart Metro bundler: `npx expo start --clear`
   - Ensure variables have `EXPO_PUBLIC_` prefix
   - Check .env file is in project root

2. **API connection failed**
   - Verify backend is running on specified URL
   - Check network connectivity
   - Ensure CORS is properly configured

3. **Authentication errors**
   - Verify credentials in API Test screen
   - Check backend authentication endpoints
   - Clear app data and retry

### Debug Mode

Enable detailed logging by setting debug flags in `.env`:

```env
EXPO_PUBLIC_DEBUG_MODE=true
EXPO_PUBLIC_ENABLE_LOGGING=true
```

## 📋 Next Steps

1. **Build UI screens** using the authentication context
2. **Implement features** using the API services
3. **Add navigation** with role-based routing
4. **Create forms** with validation and error handling
5. **Add offline support** using the existing infrastructure

The mobile app is now fully configured and ready for feature development! 🎉
