/**
 * Module professionnel pour la gestion des emplois du temps
 * NS School Manager - Timetable Management
 */

class EmploiTempsManager {
  constructor() {
    this.apiBase = '/api/emploi_temps';
    this.currentClass = null;
    this.timetableData = null;
    this.isLoading = false;
    
    // Configuration des jours et heures
    this.joursSemaine = ['<PERSON><PERSON>', '<PERSON><PERSON>', 'Mercredi', '<PERSON><PERSON>', 'Vendredi'];
    this.creneauxHoraires = [];
    
    // Éléments DOM
    this.elements = {
      classeFiltre: document.getElementById('classeFiltre'),
      jourFiltre: document.getElementById('jourFiltre'),
      planningBody: document.getElementById('planningBody'),
      addModal: document.getElementById('addModal'),
      addForm: document.getElementById('addForm'),
      addError: document.getElementById('addError'),
      loadingSpinner: null
    };

    this.init();
  }

  /**
   * Initialise le gestionnaire d'emploi du temps
   */
  async init() {
    try {
      this.createLoadingSpinner();
      await this.loadInitialData();
      this.bindEvents();
      this.setupFormValidation();
      console.log('✅ EmploiTempsManager initialisé avec succès');
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error);
      this.showError('Erreur lors de l\'initialisation de l\'emploi du temps');
    }
  }

  /**
   * Crée un spinner de chargement
   */
  createLoadingSpinner() {
    this.elements.loadingSpinner = document.createElement('div');
    this.elements.loadingSpinner.className = 'text-center p-4 d-none';
    this.elements.loadingSpinner.innerHTML = `
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="mt-2">Chargement en cours...</p>
    `;
    
    if (this.elements.planningBody) {
      this.elements.planningBody.parentNode.insertBefore(
        this.elements.loadingSpinner, 
        this.elements.planningBody
      );
    }
  }

  /**
   * Charge les données initiales
   */
  async loadInitialData() {
    await Promise.all([
      this.loadClasses(),
      this.loadMatieres(),
      this.loadSalles(),
      this.loadCreneauxDisponibles()
    ]);
  }

  /**
   * Charge la liste des classes
   */
  async loadClasses() {
    try {
      const response = await this.apiCall('GET', '/classes');
      const classes = response.data;

      this.populateSelect('classeFiltre', classes, 'nom_classe', 'nom_classe', '-- Sélectionner une classe --');
      this.populateSelect('classeSelect', classes, 'nom_classe', 'nom_classe', '-- Sélectionner une classe --');
      
      console.log(`✅ ${classes.length} classes chargées`);
    } catch (error) {
      console.error('❌ Erreur lors du chargement des classes:', error);
      throw error;
    }
  }

  /**
   * Charge la liste des matières
   */
  async loadMatieres() {
    try {
      const response = await this.apiCall('GET', '/matieres');
      const matieres = response.data;

      this.populateSelect('matiereSelect', matieres, 'matiere_id', 'nom_matiere', '-- Sélectionner une matière --');
      
      console.log(`✅ ${matieres.length} matières chargées`);
    } catch (error) {
      console.error('❌ Erreur lors du chargement des matières:', error);
      throw error;
    }
  }

  /**
   * Charge la liste des salles
   */
  async loadSalles() {
    try {
      const response = await fetch('/api/salles/options/select');
      const result = await response.json();
      const sallesData = result.data;

      const salleSelect = document.getElementById('salle_id');
      if (!salleSelect) return;

      // Clear existing options except the first one
      salleSelect.innerHTML = '<option value="">Sélectionner une salle...</option>';

      // Use the grouped data for better organization
      if (sallesData.grouped && sallesData.grouped.length > 0) {
        sallesData.grouped.forEach(group => {
          const optgroup = document.createElement('optgroup');
          optgroup.label = group.label;

          group.options.forEach(salle => {
            const option = document.createElement('option');
            option.value = salle.value;
            option.textContent = salle.info || salle.text;
            optgroup.appendChild(option);
          });

          salleSelect.appendChild(optgroup);
        });
      } else if (sallesData.flat) {
        // Fallback to flat list if grouped data is not available
        sallesData.flat.forEach(salle => {
          const option = document.createElement('option');
          option.value = salle.value;
          option.textContent = salle.info || salle.text;
          salleSelect.appendChild(option);
        });
      }

      console.log(`✅ ${result.count || 0} salles chargées`);
    } catch (error) {
      console.error('❌ Erreur lors du chargement des salles:', error);
      throw error;
    }
  }

  /**
   * Charge les créneaux horaires disponibles
   */
  async loadCreneauxDisponibles() {
    try {
      const response = await this.apiCall('GET', '/creneaux-disponibles');
      this.creneauxHoraires = response.data.creneauxHoraires;
      this.joursSemaine = response.data.joursSemaine;
      
      console.log(`✅ ${this.creneauxHoraires.length} créneaux horaires chargés`);
    } catch (error) {
      console.error('❌ Erreur lors du chargement des créneaux:', error);
      // Utiliser des valeurs par défaut en cas d'erreur
      this.creneauxHoraires = this.generateDefaultTimeSlots();
    }
  }

  /**
   * Génère des créneaux horaires par défaut
   */
  generateDefaultTimeSlots() {
    const slots = [];
    for (let hour = 8; hour <= 16; hour++) {
      slots.push({
        debut: `${hour.toString().padStart(2, '0')}:00`,
        fin: `${(hour + 1).toString().padStart(2, '0')}:00`,
        libelle: `${hour.toString().padStart(2, '0')}:00 - ${(hour + 1).toString().padStart(2, '0')}:00`
      });
    }
    return slots;
  }

  /**
   * Remplit un élément select avec des données
   */
  populateSelect(elementId, data, valueField, textField, placeholder = '') {
    const select = document.getElementById(elementId);
    if (!select) return;

    select.innerHTML = placeholder ? `<option value="">${placeholder}</option>` : '';
    
    data.forEach(item => {
      const option = document.createElement('option');
      option.value = item[valueField];
      option.textContent = item[textField] || item[valueField];
      select.appendChild(option);
    });
  }

  /**
   * Lie les événements aux éléments DOM
   */
  bindEvents() {
    // Changement de classe
    if (this.elements.classeFiltre) {
      this.elements.classeFiltre.addEventListener('change', (e) => {
        // Réinitialiser le filtre jour quand une classe est sélectionnée
        if (this.elements.jourFiltre && e.target.value) {
          this.elements.jourFiltre.value = '';
        }
        this.loadPlanning(e.target.value);
      });
    }

    // Changement de jour (filtre)
    if (this.elements.jourFiltre) {
      this.elements.jourFiltre.addEventListener('change', () => {
        const jourSelectionne = this.elements.jourFiltre.value;
        const classeSelectionnee = this.elements.classeFiltre.value;

        if (jourSelectionne && !classeSelectionnee) {
          // Si un jour est sélectionné mais pas de classe, charger tous les créneaux du jour
          this.loadPlanningParJour(jourSelectionne);
        } else if (jourSelectionne && classeSelectionnee) {
          // Si un jour est sélectionné avec une classe, réinitialiser la classe et charger le jour
          this.elements.classeFiltre.value = '';
          this.loadPlanningParJour(jourSelectionne);
        } else if (classeSelectionnee) {
          // Si une classe est sélectionnée, filtrer les données existantes
          this.renderPlanning();
        } else {
          // Aucun filtre, vider l'affichage
          this.clearPlanning();
        }
      });
    }

    // Soumission du formulaire d'ajout
    if (this.elements.addForm) {
      this.elements.addForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleAddCreneau();
      });
    }

    // Gestion des clics sur les créneaux (pour modification/suppression)
    if (this.elements.planningBody) {
      this.elements.planningBody.addEventListener('click', (e) => {
        if (e.target.classList.contains('creneau-cell')) {
          this.handleCreneauClick(e.target);
        }
      });
    }
  }

  /**
   * Configure la validation du formulaire
   */
  setupFormValidation() {
    const form = this.elements.addForm;
    if (!form) return;

    const inputs = form.querySelectorAll('input[required], select[required]');
    inputs.forEach(input => {
      input.addEventListener('blur', () => this.validateField(input));
      input.addEventListener('input', () => this.clearFieldError(input));
    });
  }

  /**
   * Valide un champ de formulaire
   */
  validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    if (field.hasAttribute('required') && !value) {
      isValid = false;
      errorMessage = 'Ce champ est requis';
    }

    // Validation spécifique selon le type de champ
    if (value && field.type === 'time') {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(value)) {
        isValid = false;
        errorMessage = 'Format d\'heure invalide';
      }
    }

    this.setFieldValidation(field, isValid, errorMessage);
    return isValid;
  }

  /**
   * Définit l'état de validation d'un champ
   */
  setFieldValidation(field, isValid, errorMessage = '') {
    field.classList.remove('is-valid', 'is-invalid');
    field.classList.add(isValid ? 'is-valid' : 'is-invalid');

    // Gestion du message d'erreur
    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (!isValid && errorMessage) {
      if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        field.parentNode.appendChild(feedback);
      }
      feedback.textContent = errorMessage;
    } else if (feedback) {
      feedback.remove();
    }
  }

  /**
   * Efface l'erreur d'un champ
   */
  clearFieldError(field) {
    field.classList.remove('is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) feedback.remove();
  }

  /**
   * Charge l'emploi du temps d'une classe
   */
  async loadPlanning(nomClasse) {
    if (!nomClasse) {
      this.clearPlanning();
      return;
    }

    try {
      this.showLoading(true);
      this.currentClass = nomClasse;

      const response = await this.apiCall('GET', `/planning/${encodeURIComponent(nomClasse)}`);
      this.timetableData = response.data;

      this.renderPlanning();
      console.log(`✅ Planning chargé pour la classe ${nomClasse}`);
    } catch (error) {
      console.error('❌ Erreur lors du chargement du planning:', error);
      this.showError('Erreur lors du chargement de l\'emploi du temps');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Charge l'emploi du temps d'un jour spécifique pour toutes les classes
   */
  async loadPlanningParJour(jourSemaine) {
    if (!jourSemaine) {
      this.clearPlanning();
      return;
    }

    try {
      this.showLoading(true);
      this.currentClass = null; // Pas de classe spécifique

      const response = await this.apiCall('GET', `/planning-jour/${encodeURIComponent(jourSemaine)}`);
      this.timetableData = response.data;

      this.renderPlanning();
      console.log(`✅ Planning chargé pour ${jourSemaine} avec ${response.data.creneaux.length} créneaux`);
    } catch (error) {
      console.error('❌ Erreur lors du chargement du planning par jour:', error);
      this.showError(`Erreur lors du chargement de l'emploi du temps pour ${jourSemaine}`);
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Affiche/masque le spinner de chargement
   */
  showLoading(show) {
    if (this.elements.loadingSpinner) {
      this.elements.loadingSpinner.classList.toggle('d-none', !show);
    }
    if (this.elements.planningBody) {
      this.elements.planningBody.classList.toggle('d-none', show);
    }
  }

  /**
   * Efface l'affichage du planning
   */
  clearPlanning() {
    if (this.elements.planningBody) {
      this.elements.planningBody.innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-muted p-4">
            <i class="bi bi-calendar-x fs-1"></i>
            <p class="mt-2">Sélectionnez une classe pour voir son emploi du temps</p>
          </td>
        </tr>
      `;
    }
  }

  /**
   * Rend l'emploi du temps sous forme de grille
   */
  renderPlanning() {
    if (!this.timetableData || !this.elements.planningBody) return;

    let { creneaux } = this.timetableData;

    // Appliquer le filtre par jour si sélectionné
    const jourFiltre = this.elements.jourFiltre ? this.elements.jourFiltre.value : '';
    if (jourFiltre) {
      creneaux = creneaux.filter(creneau => creneau.jour_semaine === jourFiltre);
    }

    if (creneaux.length === 0) {
      let messageVide;
      if (jourFiltre && !this.currentClass) {
        messageVide = `Aucun créneau programmé pour ${jourFiltre} (toutes classes confondues)`;
      } else if (jourFiltre && this.currentClass) {
        messageVide = `Aucun créneau programmé pour ${jourFiltre} dans la classe ${this.currentClass}`;
      } else if (this.currentClass) {
        messageVide = `Aucun créneau programmé pour la classe ${this.currentClass}`;
      } else {
        messageVide = 'Sélectionnez une classe ou un jour pour voir l\'emploi du temps';
      }

      this.elements.planningBody.innerHTML = `
        <tr>
          <td colspan="7" class="text-center text-muted p-4">
            <i class="bi bi-calendar-plus fs-1"></i>
            <p class="mt-2">${messageVide}</p>
            ${this.currentClass ? `
              <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addModal">
                <i class="bi bi-plus-circle"></i> Ajouter un créneau
              </button>
            ` : ''}
          </td>
        </tr>
      `;
      return;
    }

    // Rendu sous forme de liste (compatible avec l'ancien format)
    let html = '';
    creneaux.forEach(creneau => {
      html += `
        <tr class="creneau-row" data-id="${creneau.id}">
          <td class="fw-medium">${creneau.jour_semaine}</td>
          <td>
            <span class="badge bg-primary">${creneau.nom_matiere}</span>
          </td>
          <td>
            <i class="bi bi-clock"></i> ${creneau.heure_debut}
          </td>
          <td>
            <i class="bi bi-clock-fill"></i> ${creneau.heure_fin}
          </td>
          <td>
            <i class="bi bi-geo-alt"></i> ${creneau.salle}
          </td>
          <td>
            <span class="badge bg-secondary">${creneau.nom_classe}</span>
          </td>
          <td>
            <div class="btn-group btn-group-sm">
              <button class="btn btn-outline-primary btn-edit" data-id="${creneau.id}" title="Modifier">
                <i class="bi bi-pencil"></i>
              </button>
              <button class="btn btn-outline-danger btn-delete" data-id="${creneau.id}" title="Supprimer">
                <i class="bi bi-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      `;
    });

    this.elements.planningBody.innerHTML = html;
    this.bindPlanningEvents();
  }

  /**
   * Lie les événements aux éléments du planning
   */
  bindPlanningEvents() {
    // Boutons de modification
    document.querySelectorAll('.btn-edit').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.handleEditCreneau(btn.dataset.id);
      });
    });

    // Boutons de suppression
    document.querySelectorAll('.btn-delete').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.handleDeleteCreneau(btn.dataset.id);
      });
    });
  }

  /**
   * Gère l'ajout d'un nouveau créneau
   */
  async handleAddCreneau() {
    try {
      // Validation du formulaire
      const formData = this.getFormData();
      if (!this.validateForm(formData)) {
        return;
      }

      // Vérification des conflits
      const conflits = await this.checkConflicts(formData);
      if (conflits.aConflits) {
        this.showConflictWarning(conflits);
        return;
      }

      // Ajout du créneau
      this.showLoading(true);
      await this.apiCall('POST', '', formData);

      // Rechargement du planning
      await this.loadPlanning(this.currentClass);

      // Fermeture du modal et reset du formulaire
      this.closeModal();
      this.resetForm();

      this.showSuccess('Créneau ajouté avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de l\'ajout:', error);
      this.showError(error.message || 'Erreur lors de l\'ajout du créneau');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Gère la modification d'un créneau
   */
  async handleEditCreneau(idCreneau) {
    try {
      // Trouver le créneau dans les données
      const creneau = this.timetableData.creneaux.find(c => c.id == idCreneau);
      if (!creneau) {
        this.showError('Créneau non trouvé');
        return;
      }

      // Pré-remplir le formulaire avec les données du créneau
      this.fillFormWithData(creneau);

      // Changer le mode du formulaire en modification
      this.setFormMode('edit', idCreneau);

      // Ouvrir le modal
      const modal = new bootstrap.Modal(this.elements.addModal);
      modal.show();

    } catch (error) {
      console.error('❌ Erreur lors de la préparation de la modification:', error);
      this.showError('Erreur lors de la préparation de la modification');
    }
  }

  /**
   * Gère la suppression d'un créneau
   */
  async handleDeleteCreneau(idCreneau) {
    try {
      // Confirmation de suppression
      if (!confirm('Êtes-vous sûr de vouloir supprimer ce créneau ?')) {
        return;
      }

      this.showLoading(true);
      await this.apiCall('DELETE', `/${idCreneau}`);

      // Rechargement du planning
      await this.loadPlanning(this.currentClass);

      this.showSuccess('Créneau supprimé avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de la suppression:', error);
      this.showError(error.message || 'Erreur lors de la suppression du créneau');
    } finally {
      this.showLoading(false);
    }
  }

  /**
   * Récupère les données du formulaire
   */
  getFormData() {
    const form = this.elements.addForm;
    if (!form) return {};

    return {
      nom_classe: form.classeSelect?.value || this.currentClass,
      matiere_id: parseInt(form.matiereSelect?.value),
      jour_semaine: form.jourSelect?.value,
      heure_debut: form.heureDebut?.value,
      heure_fin: form.heureFin?.value,
      salle_id: parseInt(form.salle_id?.value) || null
    };
  }

  /**
   * Valide les données du formulaire
   */
  validateForm(data) {
    const errors = [];

    if (!data.nom_classe) errors.push('La classe est requise');
    if (!data.matiere_id || isNaN(data.matiere_id)) errors.push('La matière est requise');
    if (!data.jour_semaine) errors.push('Le jour est requis');
    if (!data.heure_debut) errors.push('L\'heure de début est requise');
    if (!data.heure_fin) errors.push('L\'heure de fin est requise');
    if (!data.salle_id) errors.push('La salle est requise');

    // Validation des heures
    if (data.heure_debut && data.heure_fin) {
      const debut = new Date(`2000-01-01T${data.heure_debut}:00`);
      const fin = new Date(`2000-01-01T${data.heure_fin}:00`);

      if (debut >= fin) {
        errors.push('L\'heure de début doit être antérieure à l\'heure de fin');
      }
    }

    if (errors.length > 0) {
      this.showFormErrors(errors);
      return false;
    }

    this.clearFormErrors();
    return true;
  }

  /**
   * Vérifie les conflits pour un créneau
   */
  async checkConflicts(data, excludeId = null) {
    try {
      const url = '/verifier-conflits' + (excludeId ? `?id_creneau=${excludeId}` : '');
      const response = await this.apiCall('POST', url, data);
      return response.data;
    } catch (error) {
      console.error('❌ Erreur lors de la vérification des conflits:', error);
      return { aConflits: false, conflitsClasse: [], conflitsProfesseur: [] };
    }
  }

  /**
   * Affiche un avertissement de conflit
   */
  showConflictWarning(conflits) {
    let message = 'Conflits détectés :\n';

    if (conflits.conflitsClasse.length > 0) {
      message += `\n• Conflit de classe avec ${conflits.conflitsClasse[0].nom_matiere}`;
    }

    if (conflits.conflitsProfesseur.length > 0) {
      message += '\n• Conflit de professeur détecté';
    }

    this.showError(message);
  }

  /**
   * Pré-remplit le formulaire avec les données d'un créneau
   */
  fillFormWithData(creneau) {
    const form = this.elements.addForm;
    if (!form) return;

    if (form.classeSelect) form.classeSelect.value = creneau.nom_classe;
    if (form.matiereSelect) form.matiereSelect.value = creneau.matiere_id;
    if (form.jourSelect) form.jourSelect.value = creneau.jour_semaine;
    if (form.heureDebut) form.heureDebut.value = creneau.heure_debut;
    if (form.heureFin) form.heureFin.value = creneau.heure_fin;
    if (form.salle_id) form.salle_id.value = creneau.salle_id || '';
  }

  /**
   * Configure le mode du formulaire (ajout ou modification)
   */
  setFormMode(mode, id = null) {
    const modal = this.elements.addModal;
    if (!modal) return;

    const title = modal.querySelector('.modal-title');
    const submitBtn = modal.querySelector('button[type="submit"]');

    if (mode === 'edit') {
      if (title) title.textContent = 'Modifier le créneau';
      if (submitBtn) submitBtn.textContent = 'Modifier';
      modal.dataset.mode = 'edit';
      modal.dataset.id = id;
    } else {
      if (title) title.textContent = 'Ajouter un créneau';
      if (submitBtn) submitBtn.textContent = 'Ajouter';
      modal.dataset.mode = 'add';
      delete modal.dataset.id;
    }
  }

  /**
   * Ferme le modal
   */
  closeModal() {
    const modal = bootstrap.Modal.getInstance(this.elements.addModal);
    if (modal) modal.hide();
  }

  /**
   * Remet à zéro le formulaire
   */
  resetForm() {
    if (this.elements.addForm) {
      this.elements.addForm.reset();
      this.clearFormErrors();
      this.setFormMode('add');
    }
  }

  /**
   * Affiche les erreurs du formulaire
   */
  showFormErrors(errors) {
    if (this.elements.addError) {
      this.elements.addError.innerHTML = `
        <div class="alert alert-danger">
          <ul class="mb-0">
            ${errors.map(error => `<li>${error}</li>`).join('')}
          </ul>
        </div>
      `;
      this.elements.addError.classList.remove('d-none');
    }
  }

  /**
   * Efface les erreurs du formulaire
   */
  clearFormErrors() {
    if (this.elements.addError) {
      this.elements.addError.innerHTML = '';
      this.elements.addError.classList.add('d-none');
    }
  }

  /**
   * Effectue un appel API
   */
  async apiCall(method, endpoint, data = null) {
    const url = this.apiBase + endpoint;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Erreur HTTP ${response.status}`);
    }

    return await response.json();
  }

  /**
   * Affiche un message d'erreur
   */
  showError(message) {
    this.showToast(message, 'error');
  }

  /**
   * Affiche un message de succès
   */
  showSuccess(message) {
    this.showToast(message, 'success');
  }

  /**
   * Affiche un toast de notification
   */
  showToast(message, type = 'info') {
    // Créer le toast s'il n'existe pas
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
      toastContainer.style.zIndex = '9999';
      document.body.appendChild(toastContainer);
    }

    const toastId = 'toast-' + Date.now();
    const bgClass = type === 'error' ? 'bg-danger' : type === 'success' ? 'bg-success' : 'bg-info';

    const toastHtml = `
      <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
        <div class="toast-header ${bgClass} text-white border-0">
          <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
          <strong class="me-auto">${type === 'error' ? 'Erreur' : type === 'success' ? 'Succès' : 'Information'}</strong>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
          ${message}
        </div>
      </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
    toast.show();

    // Nettoyer après fermeture
    toastElement.addEventListener('hidden.bs.toast', () => {
      toastElement.remove();
    });
  }

  /**
   * Efface l'affichage du planning
   */
  clearPlanning() {
    if (this.elements.planningBody) {
      this.elements.planningBody.innerHTML = `
        <tr>
          <td colspan="6" class="text-center text-muted p-4">
            <i class="bi bi-calendar3"></i>
            <p class="mt-2">Sélectionnez une classe pour voir son emploi du temps</p>
          </td>
        </tr>
      `;
    }
    this.currentClass = null;
    this.timetableData = null;
  }

  // ===== FONCTIONNALITÉS AVANCÉES =====

  /**
   * Exporte l'emploi du temps au format CSV
   */
  async exportToCSV(nomClasse) {
    try {
      const response = await fetch(`/api/emploi_temps/export/csv/${encodeURIComponent(nomClasse)}`);

      if (!response.ok) {
        throw new Error('Erreur lors de l\'export CSV');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `emploi_temps_${nomClasse}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      this.showToast('Export CSV réussi', 'success');
    } catch (error) {
      console.error('Erreur export CSV:', error);
      this.showToast('Erreur lors de l\'export CSV', 'error');
    }
  }

  /**
   * Exporte l'emploi du temps au format JSON
   */
  async exportToJSON(nomClasse) {
    try {
      const response = await fetch(`/api/emploi_temps/export/json/${encodeURIComponent(nomClasse)}`);

      if (!response.ok) {
        throw new Error('Erreur lors de l\'export JSON');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `emploi_temps_${nomClasse}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      this.showToast('Export JSON réussi', 'success');
    } catch (error) {
      console.error('Erreur export JSON:', error);
      this.showToast('Erreur lors de l\'export JSON', 'error');
    }
  }

  /**
   * Importe l'emploi du temps depuis un fichier JSON
   */
  async importFromJSON(nomClasse, fileData) {
    try {
      const response = await fetch(`/api/emploi_temps/import/${encodeURIComponent(nomClasse)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(fileData)
      });

      const result = await response.json();

      if (result.success) {
        this.showToast(result.data.message, 'success');
        // Recharger l'emploi du temps
        await this.loadPlanning(nomClasse);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Erreur import JSON:', error);
      this.showToast('Erreur lors de l\'import JSON', 'error');
    }
  }

  /**
   * Vérifie la disponibilité d'un professeur
   */
  async verifierDisponibiliteProfesseur(matriculeProfesseur, jourSemaine, heureDebut, heureFin, excludeId = null) {
    try {
      const response = await fetch('/api/emploi_temps/verifier-disponibilite-professeur', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          matricule_professeur: matriculeProfesseur,
          jour_semaine: jourSemaine,
          heure_debut: heureDebut,
          heure_fin: heureFin,
          exclude_id: excludeId
        })
      });

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Erreur vérification disponibilité professeur:', error);
      return { disponible: false, conflits: [] };
    }
  }

  /**
   * Vérifie la disponibilité d'une salle
   */
  async verifierDisponibiliteSalle(salle, jourSemaine, heureDebut, heureFin, excludeId = null) {
    try {
      const response = await fetch('/api/emploi_temps/verifier-disponibilite-salle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          salle: salle,
          jour_semaine: jourSemaine,
          heure_debut: heureDebut,
          heure_fin: heureFin,
          exclude_id: excludeId
        })
      });

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error('Erreur vérification disponibilité salle:', error);
      return { disponible: false, conflits: [] };
    }
  }

  /**
   * Obtient des suggestions d'optimisation
   */
  async obtenirSuggestions(nomClasse) {
    try {
      const response = await fetch(`/api/emploi_temps/suggestions/${encodeURIComponent(nomClasse)}`);
      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Erreur récupération suggestions:', error);
      return [];
    }
  }

  /**
   * Copie l'emploi du temps d'une classe vers une autre
   */
  async copierEmploiTemps(classeSource, classeDestination) {
    try {
      const response = await fetch('/api/emploi_temps/copier', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          classe_source: classeSource,
          classe_destination: classeDestination
        })
      });

      const result = await response.json();

      if (result.success) {
        this.showToast(result.data.message, 'success');
        return result.data;
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Erreur copie emploi du temps:', error);
      this.showToast('Erreur lors de la copie', 'error');
      throw error;
    }
  }
}

// Initialisation automatique quand le DOM est prêt
document.addEventListener('DOMContentLoaded', () => {
  if (document.getElementById('planningBody')) {
    window.emploiTempsManager = new EmploiTempsManager();
  }

  // Lightweight hooks for eleve timetable navigation (prev/next/today buttons)
  if (!window.emploiTemps) window.emploiTemps = {};
  window.emploiTemps.navigateWeek = function(offset){
    try {
      const url = new URL(window.location.href);
      const current = parseInt(url.searchParams.get('weekOffset') || '0', 10);
      const next = isNaN(current) ? offset : current + offset;
      url.searchParams.set('weekOffset', String(next));
      window.location.href = url.toString();
    } catch (e) {
      window.location.search = '?weekOffset=' + offset;
    }
  };
  window.emploiTemps.today = function(){
    try {
      const url = new URL(window.location.href);
      url.searchParams.set('weekOffset', '0');
      window.location.href = url.toString();
    } catch (e) {
      window.location.search = '?weekOffset=0';
    }
  };
});
