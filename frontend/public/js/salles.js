/**
 * NS School Manager - Room Management
 * Gestion des salles de classe
 */

class SalleManager {
  constructor() {
    this.salles = [];
    this.salleEnEdition = null;
    this.init();
  }

  async init() {
    await this.chargerSalles();
    await this.chargerStatistiques();
    this.configurerEvenements();
  }

  /**
   * Charge toutes les salles depuis l'API
   */
  async chargerSalles() {
    try {
      this.afficherSpinner(true);
      const response = await fetch('/api/salles');
      const result = await response.json();
      
      if (result.success) {
        this.salles = result.data;
        this.afficherSalles();
        this.remplirFiltres();
      } else {
        this.afficherToast('Erreur lors du chargement des salles', 'error');
      }
    } catch (error) {
      console.error('Erreur:', error);
      this.afficherToast('Erreur de connexion', 'error');
    } finally {
      this.afficherSpinner(false);
    }
  }

  /**
   * Charge les statistiques des salles
   */
  async chargerStatistiques() {
    try {
      const response = await fetch('/api/salles/statistiques');
      const result = await response.json();
      
      if (result.success) {
        this.afficherStatistiques(result.data);
      }
    } catch (error) {
      console.error('Erreur statistiques:', error);
    }
  }

  /**
   * Affiche les statistiques
   */
  afficherStatistiques(stats) {
    const container = document.getElementById('statistiques');
    if (!container) return;
    container.innerHTML = `
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">${stats.total || 0}</h4>
                <p class="mb-0">Total Salles</p>
              </div>
              <i class="bi bi-building fs-1 opacity-50"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">${stats.disponibles || 0}</h4>
                <p class="mb-0">Disponibles</p>
              </div>
              <i class="bi bi-check-circle fs-1 opacity-50"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">${stats.capaciteTotal || 0}</h4>
                <p class="mb-0">Capacité Totale</p>
              </div>
              <i class="bi bi-people fs-1 opacity-50"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">${Object.keys(stats.parType || {}).length}</h4>
                <p class="mb-0">Types de Salles</p>
              </div>
              <i class="bi bi-grid fs-1 opacity-50"></i>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Affiche la liste des salles
   */
  afficherSalles(sallesFiltrees = null) {
    const salles = sallesFiltrees || this.salles;
    const container = document.getElementById('sallesContainer');
    if (!container) return;
    
    if (salles.length === 0) {
      container.innerHTML = `
        <div class="col-12">
          <div class="text-center py-5">
            <i class="bi bi-building display-1 text-muted"></i>
            <h4 class="text-muted mt-3">Aucune salle trouvée</h4>
            <p class="text-muted">Ajoutez votre première salle pour commencer</p>
          </div>
        </div>
      `;
      return;
    }

    container.innerHTML = salles.map(salle => `
      <div class="col-md-6 col-lg-4 mb-4">
        <div class="card room-card h-100">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="capacity-indicator me-2">${salle.capacite}</div>
              <div>
                <h6 class="mb-0">${salle.nom_salle}</h6>
                <small class="text-muted">${salle.batiment} - ${salle.etage}</small>
              </div>
            </div>
            <span class="badge room-type-badge ${this.getBadgeClass(salle.type_salle)}">
              ${this.getTypeLabel(salle.type_salle)}
            </span>
          </div>
          <div class="card-body">
            ${salle.equipements ? `
              <p class="card-text small text-muted mb-2">
                <i class="bi bi-tools me-1"></i>${salle.equipements}
              </p>
            ` : ''}
            <div class="d-flex justify-content-between align-items-center">
              <span class="badge ${salle.disponible ? 'bg-success' : 'bg-secondary'}">
                <i class="bi bi-${salle.disponible ? 'check-circle' : 'x-circle'} me-1"></i>
                ${salle.disponible ? 'Disponible' : 'Indisponible'}
              </span>
              <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" onclick="salleManager.modifierSalle(${salle.salle_id})" title="Modifier">
                  <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-outline-danger" onclick="salleManager.confirmerSuppression(${salle.salle_id})" title="Supprimer">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `).join('');
  }

  /**
   * Remplit les filtres avec les données disponibles
   */
  remplirFiltres() {
    const batiments = [...new Set(this.salles.map(s => s.batiment))].sort();
    const filtreBatiment = document.getElementById('filtreBatiment');
    
    filtreBatiment.innerHTML = '<option value="">Tous les bâtiments</option>' +
      batiments.map(b => `<option value="${b}">${b}</option>`).join('');
  }

  /**
   * Filtre les salles selon les critères
   */
  filtrerSalles() {
    const typeEl = document.getElementById('filtreType');
    const batimentEl = document.getElementById('filtreBatiment');
    const rechercheEl = document.getElementById('rechercheNom');
    const type = typeEl ? typeEl.value : '';
    const batiment = batimentEl ? batimentEl.value : '';
    const recherche = (rechercheEl ? rechercheEl.value : '').toLowerCase();

    const sallesFiltrees = this.salles.filter(salle => {
      const matchType = !type || salle.type_salle === type;
      const matchBatiment = !batiment || salle.batiment === batiment;
      const matchRecherche = !recherche || salle.nom_salle.toLowerCase().includes(recherche);
      
      return matchType && matchBatiment && matchRecherche;
    });

    this.afficherSalles(sallesFiltrees);
  }

  /**
   * Ouvre le modal pour ajouter une nouvelle salle
   */
  ouvrirModalAjout() {
    this.salleEnEdition = null;
    const title = document.getElementById('modalTitle');
    const form = document.getElementById('salleForm');
    const disponible = document.getElementById('disponible');
    if (title) title.innerHTML = '<i class="bi bi-building me-2"></i>Nouvelle Salle';
    if (form) form.reset();
    if (disponible) disponible.checked = true;
  }

  /**
   * Ouvre le modal pour modifier une salle
   */
  async modifierSalle(salleId) {
    const salle = this.salles.find(s => s.salle_id === salleId);
    if (!salle) return;

    this.salleEnEdition = salle;
    const title = document.getElementById('modalTitle');
    if (title) title.innerHTML = '<i class="bi bi-pencil me-2"></i>Modifier la Salle';
    
    // Remplir le formulaire
    const nomSalle = document.getElementById('nomSalle');
    const typeSalle = document.getElementById('typeSalle');
    const capacite = document.getElementById('capacite');
    const etage = document.getElementById('etage');
    const batiment = document.getElementById('batiment');
    const equipements = document.getElementById('equipements');
    const disponible = document.getElementById('disponible');

    if (nomSalle) nomSalle.value = salle.nom_salle;
    if (typeSalle) typeSalle.value = salle.type_salle;
    if (capacite) capacite.value = salle.capacite;
    if (etage) etage.value = salle.etage;
    if (batiment) batiment.value = salle.batiment;
    if (equipements) equipements.value = salle.equipements || '';
    if (disponible) disponible.checked = salle.disponible;

    // Ouvrir le modal
    const salleModalEl = document.getElementById('salleModal');
    if (salleModalEl) {
      new bootstrap.Modal(salleModalEl).show();
    }
  }

  /**
   * Sauvegarde une salle (création ou modification)
   */
  async sauvegarderSalle(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
      nom_salle: formData.get('nom_salle'),
      type_salle: formData.get('type_salle'),
      capacite: parseInt(formData.get('capacite')),
      etage: formData.get('etage'),
      batiment: formData.get('batiment'),
      equipements: formData.get('equipements') || null,
      disponible: formData.has('disponible')
    };

    try {
      this.afficherSpinner(true);
      
      const url = this.salleEnEdition 
        ? `/api/salles/${this.salleEnEdition.salle_id}`
        : '/api/salles';
      const method = this.salleEnEdition ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        this.afficherToast(
          this.salleEnEdition ? 'Salle modifiée avec succès' : 'Salle ajoutée avec succès',
          'success'
        );
        const salleModalEl = document.getElementById('salleModal');
        if (salleModalEl) {
          const instance = bootstrap.Modal.getInstance(salleModalEl);
          if (instance) instance.hide();
        }
        await this.chargerSalles();
        await this.chargerStatistiques();
      } else {
        this.afficherToast(result.message || 'Erreur lors de la sauvegarde', 'error');
      }
    } catch (error) {
      console.error('Erreur:', error);
      this.afficherToast('Erreur de connexion', 'error');
    } finally {
      this.afficherSpinner(false);
    }
  }

  /**
   * Confirme la suppression d'une salle
   */
  confirmerSuppression(salleId) {
    const salle = this.salles.find(s => s.salle_id === salleId);
    if (!salle) return;

    this.salleASupprimer = salle;
    const confirmEl = document.getElementById('confirmDeleteModal');
    if (confirmEl) {
      new bootstrap.Modal(confirmEl).show();
    }
  }

  /**
   * Supprime une salle
   */
  async supprimerSalle() {
    if (!this.salleASupprimer) return;

    try {
      this.afficherSpinner(true);
      
      const response = await fetch(`/api/salles/${this.salleASupprimer.salle_id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        this.afficherToast('Salle supprimée avec succès', 'success');
        const confirmEl = document.getElementById('confirmDeleteModal');
        if (confirmEl) {
          const instance = bootstrap.Modal.getInstance(confirmEl);
          if (instance) instance.hide();
        }
        await this.chargerSalles();
        await this.chargerStatistiques();
      } else {
        this.afficherToast(result.message || 'Erreur lors de la suppression', 'error');
      }
    } catch (error) {
      console.error('Erreur:', error);
      this.afficherToast('Erreur de connexion', 'error');
    } finally {
      this.afficherSpinner(false);
    }
  }

  /**
   * Configure les événements
   */
  configurerEvenements() {
    // Formulaire de salle
    const form = document.getElementById('salleForm');
    if (form) {
      form.addEventListener('submit', (e) => this.sauvegarderSalle(e));
    }
    
    // Bouton de confirmation de suppression
    const btnConfirmer = document.getElementById('btnConfirmerSuppression');
    if (btnConfirmer) {
      btnConfirmer.addEventListener('click', () => this.supprimerSalle());
    }
  }

  /**
   * Utilitaires
   */
  getBadgeClass(type) {
    const classes = {
      'normale': 'bg-primary',
      'laboratoire': 'bg-success',
      'informatique': 'bg-info',
      'sport': 'bg-warning',
      'bibliotheque': 'bg-secondary',
      'autre': 'bg-dark'
    };
    return classes[type] || 'bg-secondary';
  }

  getTypeLabel(type) {
    const labels = {
      'normale': 'Normale',
      'laboratoire': 'Laboratoire',
      'informatique': 'Informatique',
      'sport': 'Sport',
      'bibliotheque': 'Bibliothèque',
      'autre': 'Autre'
    };
    return labels[type] || type;
  }

  afficherSpinner(show) {
    const spinner = document.getElementById('loadingSpinner');
    if (!spinner) return;
    spinner.classList.toggle('d-none', !show);
  }

  afficherToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) return;
    const toastId = 'toast-' + Date.now();
    
    const bgClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
    
    const toastHtml = `
      <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
        <div class="toast-body">
          <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
          ${message}
        </div>
      </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    if (!toastElement) return;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    toastElement.addEventListener('hidden.bs.toast', () => {
      toastElement.remove();
    });
  }
}

// Fonctions globales pour les événements onclick
function ouvrirModalAjout() {
  salleManager.ouvrirModalAjout();
}

function filtrerSalles() {
  salleManager.filtrerSalles();
}

// Initialisation
let salleManager;
document.addEventListener('DOMContentLoaded', () => {
  salleManager = new SalleManager();
});
