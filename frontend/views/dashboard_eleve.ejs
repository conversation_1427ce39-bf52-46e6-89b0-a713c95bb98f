<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>NS School Manager - Tableau de bord Élève</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

  <%- include('partials/dashboard-styles') %>

  <style>
    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }

    /* Student-specific styles */
    .student-badge {
      background: linear-gradient(135deg, #10b981, #34d399);
      color: white;
      font-weight: 600;
      border-radius: 50%;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .welcome-section {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      color: white;
      border-radius: var(--border-radius);
      padding: 2rem;
      margin-bottom: 2rem;
      position: relative;
      overflow: hidden;
    }

    .welcome-section::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    .welcome-content {
      position: relative;
      z-index: 1;
    }

    .grade-circle {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      font-weight: 700;
      color: white;
      margin-left: auto;
    }

    /* Additional dashboard layout styles */
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 2rem;
      min-height: calc(100vh - 60px);
      background-color: #f9fafb;
    }
  </style>
</head>
<body>
  <!-- Student Topbar -->
  <%- include('partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'dashboard'; %>
  <%- include('partials/eleve-sidebar') %>

  <!-- Professional Main Content -->
  <div class="dashboard-main">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h1 class="mb-2">Bonjour, Jules! 👋</h1>
            <p class="mb-0 opacity-90">Voici un aperçu de votre parcours scolaire aujourd'hui</p>
          </div>
          <div class="grade-circle">
            <%= typeof note === 'number' ? note.toFixed(1) : note %>/20
          </div>
        </div>
      </div>
    </div>

    <!-- Professional Statistics -->
    <div class="stats-grid">
      <%- include('partials/stat-card', {
        icon: 'bi-journal-text',
        value: typeof note === 'number' ? note.toFixed(2) : note,
        label: 'Moyenne générale',
        type: 'success',
        change: '+0.5',
        changeType: 'positive'
      }) %>

      <%- include('partials/stat-card', {
        icon: 'bi-calendar-week',
        value: programme || '0',
        label: 'Cours cette semaine',
        type: 'info',
        change: null,
        changeType: null
      }) %>

      <%- include('partials/stat-card', {
        icon: 'bi-book-half',
        value: cours || 'Aucun',
        label: 'Cours en cours',
        type: 'warning',
        change: null,
        changeType: null
      }) %>

      <%- include('partials/stat-card', {
        icon: 'bi-x-circle',
        value: absence || '0',
        label: 'Absences ce mois',
        type: 'danger',
        change: absence > 0 ? '+' + absence : null,
        changeType: absence > 0 ? 'negative' : null
      }) %>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <a href="/eleve/notes" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-journal-text"></i>
        </div>
        <div class="quick-action-label">Consulter mes notes</div>
      </a>

      <a href="/eleve/devoirs" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-clipboard-check"></i>
        </div>
        <div class="quick-action-label">Mes devoirs</div>
      </a>

      <a href="/eleve/emploi-temps" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-calendar-week"></i>
        </div>
        <div class="quick-action-label">Emploi du temps</div>
      </a>

      <a href="/eleve/cours" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-book"></i>
        </div>
        <div class="quick-action-label">Mes cours</div>
      </a>
    </div>

    <!-- Professional Activity Feed -->
    <%- include('partials/activity-feed', {
      activities: typeof activites !== 'undefined' ? activites : [],
      title: 'Mes activités récentes',
      emptyMessage: 'Aucune activité récente à afficher'
    }) %>

    <!-- Additional Dashboard Sections -->
    <div class="row">
      <div class="col-lg-6">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="bi bi-calendar-check"></i>
              Prochains devoirs
            </h3>
          </div>
          <div class="card-body">
            <div class="text-center py-4">
              <i class="bi bi-clipboard-check display-4 text-muted mb-3"></i>
              <p class="text-muted">Aucun devoir à rendre prochainement</p>
              <a href="/eleve/devoirs" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-eye me-1"></i>
                Voir tous les devoirs
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="bi bi-megaphone"></i>
              Dernières annonces
            </h3>
          </div>
          <div class="card-body">
            <div class="text-center py-4">
              <i class="bi bi-megaphone display-4 text-muted mb-3"></i>
              <p class="text-muted">Aucune nouvelle annonce</p>
              <a href="/eleve/Annonces" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-eye me-1"></i>
                Voir toutes les annonces
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Add smooth animations on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Animate stat cards with staggered delay
      const statCards = document.querySelectorAll('.stat-card');
      statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
      });

      // Animate quick actions
      const quickActions = document.querySelectorAll('.quick-action');
      quickActions.forEach((action, index) => {
        action.style.animationDelay = `${0.4 + index * 0.1}s`;
        action.classList.add('fade-in');
      });
    });
  </script>
</body>
</html>
