<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Gestion des absences</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }
    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }
    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }
    .icon-cours { color: #0d6efd; }
    .icon-eleves { color: #20c997; }
    .icon-devoirs { color: #ffc107; }
    .icon-absence { color: #dc3545; }
    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    .initial-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }
    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }
  </style>
</head>
<body>

  <!-- Professor Topbar -->
  <%- include('../partials/prof-topbar') %>

  <!-- Professor Sidebar -->
  <%
    // Set current page for active sidebar link
    let currentPage = 'absences';
  %>
  <%- include('../partials/prof-sidebar') %>
  <div class="main-content">
  <h2>Gestion des absences</h2>

  <div class="mb-3">
    <label for="classe">Sélectionner une classe :</label>
    <select id="classe" class="form-select">
      <option value="">-- Choisir une classe --</option>
      <% classes.forEach(classe => { %>
        <option value="<%= classe.nom_classe %>"><%= classe.nom_classe %></option>
      <% }) %>
    </select>
  </div>

  <div id="elevesSection" style="display:none;">
    <form id="absenceForm">
      <input type="hidden" name="nom_classe" id="inputClasse" />
      <div class="table-responsive">
        <table class="table table-bordered">
          <thead class="table-dark">
            <tr>
              <th>Nom</th>
              <th>Prénom</th>
              <th>Présent ?</th>
            </tr>
          </thead>
          <tbody id="elevesTableBody">
            <!-- Les élèves seront injectés ici -->
          </tbody>
        </table>
      </div>

      <div class="mb-3">
        <label for="heure">Durée du cours (en heures) :</label>
        <input type="number" class="form-control" name="heure" id="heure" required min="1" />
      </div>

      <button type="submit" class="btn btn-success">Fin de l'appel</button>
    </form>
  </div>
</div>
  <script>
    document.getElementById('classe').addEventListener('change', function () {
      const nomClasse = this.value;
      if (!nomClasse) return;

      document.getElementById('inputClasse').value = nomClasse;
      fetch(`/prof/absence/eleves/${nomClasse}`)
        .then(res => res.json())
        .then(eleves => {
          const tbody = document.getElementById('elevesTableBody');
          tbody.innerHTML = '';
          eleves.forEach(eleve => {
            const row = `
              <tr>
                <td>${eleve.nom}</td>
                <td>${eleve.prenom}</td>
                <td class="text-center">
                  <input type="checkbox" name="present[]" value="${eleve.matricule}" checked>
                  <input type="hidden" name="allEleves[]" value="${eleve.matricule}">
                </td>
              </tr>`;
            tbody.innerHTML += row;
          });
          document.getElementById('elevesSection').style.display = 'block';
        });
    });

    document.getElementById('absenceForm').addEventListener('submit', function (e) {
      e.preventDefault();

      const form = e.target;
      const formData = new FormData(form);
      const present = formData.getAll('present[]');
      const allEleves = formData.getAll('allEleves[]');
      const absents = allEleves.filter(id => !present.includes(id));
      const duree = formData.get('heure');
      const nom_classe = formData.get('nom_classe');

      fetch('/prof/absence/enregistrer-absences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ nom_classe, absents, duree })
      })
      .then(res => res.json())
      .then(data => {
        showToast(data.message || 'Absence enregistrée', 'success');
      })
      .catch(err => {
        showToast('Erreur lors de l’enregistrement', 'danger');
        console.error(err);
      });
    });

    function showToast(message, type) {
      const toast = document.getElementById('toast');
      const toastBody = document.getElementById('toast-body');
      toastBody.textContent = message;
      toast.classList.remove('bg-success', 'bg-danger');
      toast.classList.add(`bg-${type}`);
      const toastInstance = new bootstrap.Toast(toast);
      toastInstance.show();
    }
  </script>
<!-- Toast Notification -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1100">
  <div id="toast" class="toast align-items-center text-white border-0 bg-success" role="alert" aria-live="assertive" aria-atomic="true">
    <div class="d-flex">
      <div class="toast-body" id="toast-body">
        <!-- Message will appear here -->
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
  </div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
