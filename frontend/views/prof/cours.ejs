<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Mes cours</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }
    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }
    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }
    .icon-cours { color: #0d6efd; }
    .icon-eleves { color: #20c997; }
    .icon-devoirs { color: #ffc107; }
    .icon-absence { color: #dc3545; }
    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    .initial-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }
    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }
  </style>
</head>
<body>

  <!-- Professor Topbar -->
  <%- include('../partials/prof-topbar') %>

  <!-- Professor Sidebar -->
  <%
    // Set current page for active sidebar link
    let currentPage = 'cours';
  %>
  <%- include('../partials/prof-sidebar') %>
  <div class="main-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="bi bi-book me-2"></i>Mes Cours</h2>
      <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
        <i class="bi bi-cloud-upload me-2"></i>Ajouter un cours
      </button>
    </div>

    <!-- Alert pour les messages -->
    <div id="alertContainer"></div>

    <!-- Statistiques -->
    <div class="row mb-4" id="statsContainer">
      <div class="col-md-4">
        <div class="card-stat">
          <div class="icon icon-cours">
            <i class="bi bi-book"></i>
          </div>
          <div>
            <h4 id="totalCours">-</h4>
            <p class="mb-0">Total cours</p>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card-stat">
          <div class="icon icon-eleves">
            <i class="bi bi-building"></i>
          </div>
          <div>
            <h4 id="totalClasses">-</h4>
            <p class="mb-0">Classes enseignées</p>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card-stat">
          <div class="icon icon-devoirs">
            <i class="bi bi-journal-text"></i>
          </div>
          <div>
            <h4 id="totalMatieres">-</h4>
            <p class="mb-0">Matières enseignées</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Tableau des cours -->
    <div class="table-box p-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="mb-0">Liste des cours</h5>
        <div class="d-flex gap-2">
          <input type="text" id="searchInput" class="form-control form-control-sm" placeholder="Rechercher..." style="width: 200px;">
          <select id="filterMatiere" class="form-select form-select-sm" style="width: 150px;">
            <option value="">Toutes les matières</option>
          </select>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th><i class="bi bi-file-text me-2"></i>Titre</th>
              <th><i class="bi bi-book me-2"></i>Matière</th>
              <th><i class="bi bi-building me-2"></i>Classe</th>
              <th><i class="bi bi-file-earmark me-2"></i>Fichier</th>
              <th><i class="bi bi-calendar me-2"></i>Date</th>
              <th><i class="bi bi-gear me-2"></i>Actions</th>
            </tr>
          </thead>
          <tbody id="coursTableBody">
            <% cours.forEach(c => { %>
              <tr data-cours-id="<%= c.cours_id %>">
                <td>
                  <strong><%= c.titre %></strong>
                </td>
                <td>
                  <span class="badge bg-primary"><%= c.matiere %></span>
                </td>
                <td>
                  <span class="badge bg-info"><%= c.nom_classe %></span>
                </td>
                <td>
                  <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-pdf text-danger me-2"></i>
                    <small class="text-muted"><%= c.fichier.split('-').slice(1).join('-') %></small>
                  </div>
                </td>
                <td>
                  <small><%= new Date(c.date_ajout).toLocaleDateString('fr-FR') %></small>
                </td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="downloadCours(<%= c.cours_id %>)" title="Télécharger">
                      <i class="bi bi-download"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewCoursInfo(<%= c.cours_id %>)" title="Informations">
                      <i class="bi bi-info-circle"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteCours(<%= c.cours_id %>, '<%= c.titre %>')" title="Supprimer">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            <% }) %>
          </tbody>
        </table>
      </div>

      <div id="noResults" class="text-center py-4" style="display: none;">
        <i class="bi bi-search fs-1 text-muted"></i>
        <p class="text-muted mt-2">Aucun cours trouvé</p>
      </div>
    </div>
  </div>

  <!-- Modal Upload -->
  <div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title"><i class="bi bi-cloud-upload me-2"></i>Ajouter un cours</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form id="uploadForm" enctype="multipart/form-data">
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="titre" name="titre" placeholder="Titre du cours" required>
                  <label for="titre"><i class="bi bi-file-text me-2"></i>Titre du cours</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <select class="form-select" id="matiere_id" name="matiere_id" required>
                    <option value="">Sélectionner une matière</option>
                  </select>
                  <label for="matiere_id"><i class="bi bi-book me-2"></i>Matière</label>
                </div>
              </div>
            </div>

            <div class="form-floating mb-3">
              <select class="form-select" id="nom_classe" name="nom_classe" required>
                <option value="">Sélectionner une classe</option>
              </select>
              <label for="nom_classe"><i class="bi bi-building me-2"></i>Classe</label>
            </div>

            <div class="mb-3">
              <label for="fichier" class="form-label"><i class="bi bi-file-earmark me-2"></i>Fichier du cours</label>
              <input type="file" class="form-control" id="fichier" name="fichier" required accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.png,.zip">
              <div class="form-text">
                Formats acceptés: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, TXT, JPG, PNG, ZIP (Max: 10 MB)
              </div>
              <div class="mt-2">
                <div class="progress" id="uploadProgress" style="display: none;">
                  <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
              </div>
            </div>

            <div id="filePreview" class="alert alert-info" style="display: none;">
              <i class="bi bi-file-earmark me-2"></i>
              <span id="fileName"></span>
              <span id="fileSize" class="text-muted ms-2"></span>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="submit" class="btn btn-primary" id="uploadBtn">
              <i class="bi bi-cloud-upload me-2"></i>Ajouter le cours
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal Info Cours -->
  <div class="modal fade" id="infoModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title"><i class="bi bi-info-circle me-2"></i>Informations du cours</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body" id="infoModalBody">
          <!-- Contenu dynamique -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Variables globales
    let matieres = [];

    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
      loadStats();
      loadMatieres();
      setupEventListeners();
    });

    // Charger les statistiques
    function loadStats() {
      fetch('/professeur/cours/api/stats')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            document.getElementById('totalCours').textContent = data.data.total_cours;
            document.getElementById('totalClasses').textContent = data.data.classes_enseignees;
            document.getElementById('totalMatieres').textContent = data.data.matieres_enseignees;
          }
        })
        .catch(error => console.error('Erreur chargement stats:', error));
    }

    // Charger les matières du professeur
    function loadMatieres() {
      fetch('/professeur/cours/api/matieres')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            matieres = data.data;
            populateMatiereSelects();
          }
        })
        .catch(error => console.error('Erreur chargement matières:', error));
    }

    // Remplir les selects de matières
    function populateMatiereSelects() {
      const matiereSelect = document.getElementById('matiere_id');
      const filterSelect = document.getElementById('filterMatiere');

      // Vider les options existantes
      matiereSelect.innerHTML = '<option value="">Sélectionner une matière</option>';
      filterSelect.innerHTML = '<option value="">Toutes les matières</option>';

      // Grouper par matière
      const matieresUniques = {};
      matieres.forEach(m => {
        if (!matieresUniques[m.nom]) {
          matieresUniques[m.nom] = [];
        }
        matieresUniques[m.nom].push(m);
      });

      // Ajouter les options
      Object.keys(matieresUniques).forEach(nomMatiere => {
        // Pour le filtre
        const filterOption = document.createElement('option');
        filterOption.value = nomMatiere;
        filterOption.textContent = nomMatiere;
        filterSelect.appendChild(filterOption);

        // Pour le formulaire d'upload
        matieresUniques[nomMatiere].forEach(matiere => {
          const option = document.createElement('option');
          option.value = matiere.matiere_id;
          option.textContent = `${matiere.nom} - ${matiere.nom_classe} (${matiere.niveau})`;
          matiereSelect.appendChild(option);
        });
      });
    }

    // Mettre à jour les classes selon la matière sélectionnée
    document.getElementById('matiere_id').addEventListener('change', function() {
      const matiereId = this.value;
      const classeSelect = document.getElementById('nom_classe');

      classeSelect.innerHTML = '<option value="">Sélectionner une classe</option>';

      if (matiereId) {
        const matiere = matieres.find(m => m.matiere_id == matiereId);
        if (matiere) {
          const option = document.createElement('option');
          option.value = matiere.nom_classe;
          option.textContent = `${matiere.nom_classe} - ${matiere.niveau}`;
          classeSelect.appendChild(option);
          classeSelect.value = matiere.nom_classe;
        }
      }
    });

    // Gestion du fichier sélectionné
    document.getElementById('fichier').addEventListener('change', function() {
      const file = this.files[0];
      const preview = document.getElementById('filePreview');
      const fileName = document.getElementById('fileName');
      const fileSize = document.getElementById('fileSize');

      if (file) {
        fileName.textContent = file.name;
        fileSize.textContent = `(${formatFileSize(file.size)})`;
        preview.style.display = 'block';

        // Vérifier la taille
        if (file.size > 10 * 1024 * 1024) {
          showAlert('danger', 'Le fichier est trop volumineux. Taille maximale: 10 MB');
          this.value = '';
          preview.style.display = 'none';
        }
      } else {
        preview.style.display = 'none';
      }
    });

    // Soumission du formulaire d'upload
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(this);
      const uploadBtn = document.getElementById('uploadBtn');
      const progress = document.getElementById('uploadProgress');
      const progressBar = progress.querySelector('.progress-bar');

      // Désactiver le bouton et afficher la progression
      uploadBtn.disabled = true;
      uploadBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Upload en cours...';
      progress.style.display = 'block';

      // Créer une requête avec suivi de progression
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
          const percentComplete = (e.loaded / e.total) * 100;
          progressBar.style.width = percentComplete + '%';
          progressBar.textContent = Math.round(percentComplete) + '%';
        }
      });

      xhr.addEventListener('load', function() {
        if (xhr.status === 200 || xhr.status === 201) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
              showAlert('success', response.message);
              bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
              setTimeout(() => location.reload(), 1500);
            } else {
              showAlert('danger', response.message);
            }
          } catch (e) {
            // Réponse HTML (redirection)
            showAlert('success', 'Cours ajouté avec succès');
            setTimeout(() => location.reload(), 1500);
          }
        } else {
          showAlert('danger', 'Erreur lors de l\'upload du fichier');
        }

        // Réinitialiser
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="bi bi-cloud-upload me-2"></i>Ajouter le cours';
        progress.style.display = 'none';
        progressBar.style.width = '0%';
      });

      xhr.addEventListener('error', function() {
        showAlert('danger', 'Erreur de connexion lors de l\'upload');
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="bi bi-cloud-upload me-2"></i>Ajouter le cours';
        progress.style.display = 'none';
      });

      xhr.open('POST', '/professeur/cours/ajouter');
      xhr.send(formData);
    });

    // Fonction de recherche
    document.getElementById('searchInput').addEventListener('input', function() {
      filterTable();
    });

    document.getElementById('filterMatiere').addEventListener('change', function() {
      filterTable();
    });

    function filterTable() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const matiereFilter = document.getElementById('filterMatiere').value.toLowerCase();
      const rows = document.querySelectorAll('#coursTableBody tr');
      let visibleRows = 0;

      rows.forEach(row => {
        const titre = row.cells[0].textContent.toLowerCase();
        const matiere = row.cells[1].textContent.toLowerCase();
        const classe = row.cells[2].textContent.toLowerCase();

        const matchesSearch = titre.includes(searchTerm) || matiere.includes(searchTerm) || classe.includes(searchTerm);
        const matchesMatiere = !matiereFilter || matiere.includes(matiereFilter);

        if (matchesSearch && matchesMatiere) {
          row.style.display = '';
          visibleRows++;
        } else {
          row.style.display = 'none';
        }
      });

      document.getElementById('noResults').style.display = visibleRows === 0 ? 'block' : 'none';
    }

    // Fonctions d'action
    function downloadCours(coursId) {
      window.open(`/professeur/cours/download/${coursId}`, '_blank');
    }

    function viewCoursInfo(coursId) {
      fetch(`/api/download/cours/${coursId}/info`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const cours = data.data;
            document.getElementById('infoModalBody').innerHTML = `
              <div class="row">
                <div class="col-sm-4"><strong>Titre:</strong></div>
                <div class="col-sm-8">${cours.titre}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Matière:</strong></div>
                <div class="col-sm-8">${cours.matiere}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Classe:</strong></div>
                <div class="col-sm-8">${cours.classe}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Fichier:</strong></div>
                <div class="col-sm-8">${cours.fichier.nom_original}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Taille:</strong></div>
                <div class="col-sm-8">${cours.fichier.taille}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Date d'ajout:</strong></div>
                <div class="col-sm-8">${new Date(cours.date_ajout).toLocaleDateString('fr-FR')}</div>
              </div>
            `;
            new bootstrap.Modal(document.getElementById('infoModal')).show();
          } else {
            showAlert('danger', data.message);
          }
        })
        .catch(error => {
          console.error('Erreur:', error);
          showAlert('danger', 'Erreur lors de la récupération des informations');
        });
    }

    function deleteCours(coursId, titre) {
      if (confirm(`Êtes-vous sûr de vouloir supprimer le cours "${titre}" ?`)) {
        fetch(`/professeur/cours/api/${coursId}`, {
          method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            showAlert('success', data.message);
            document.querySelector(`tr[data-cours-id="${coursId}"]`).remove();
            loadStats(); // Recharger les stats
          } else {
            showAlert('danger', data.message);
          }
        })
        .catch(error => {
          console.error('Erreur:', error);
          showAlert('danger', 'Erreur lors de la suppression');
        });
      }
    }

    // Fonctions utilitaires
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showAlert(type, message) {
      const alertContainer = document.getElementById('alertContainer');
      const alert = document.createElement('div');
      alert.className = `alert alert-${type} alert-dismissible fade show`;
      alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

      alertContainer.innerHTML = '';
      alertContainer.appendChild(alert);

      setTimeout(() => {
        if (alert.parentNode) {
          alert.remove();
        }
      }, 5000);
    }

    function setupEventListeners() {
      // Réinitialiser le formulaire quand le modal se ferme
      document.getElementById('uploadModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('uploadForm').reset();
        document.getElementById('filePreview').style.display = 'none';
        document.getElementById('uploadProgress').style.display = 'none';
      });
    }
  </script>
</body>
</html>
