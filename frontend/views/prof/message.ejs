<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Messagerie Professeur - NS School Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <!-- Include professional message styles -->
  <%- include('../partials/message-styles') %>

  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }
  </style>
</head>
<body>
  <!-- Professor <PERSON> -->
  <%- include('../partials/prof-topbar') %>

  <!-- Professor <PERSON> -->
  <%
    // Set current page for active sidebar link
    let currentPage = 'message';
  %>
  <%- include('../partials/prof-sidebar') %>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Professional Header -->
    <div class="message-header mb-4">
      <div class="row align-items-center">
        <div class="col-md-6">
          <h1 class="h4 mb-0">
            <i class="bi bi-chat-dots me-2"></i>
            Messagerie Professeur
          </h1>
          <small class="text-muted">Gérez vos messages et communications</small>
        </div>
        <div class="col-md-6 text-end">
          <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#composeModal">
            <i class="bi bi-plus-circle me-1"></i>
            Nouveau Message
          </button>
        </div>
      </div>
    </div>

    <!-- Professional Message Interface -->
    <%- include('../partials/message-layout') %>
  </div>

  <!-- Professional JavaScript for Professor Messaging -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Initialize professor messaging system
    let currentUser = {
      matricule: '<%= typeof matricule !== "undefined" ? matricule : "" %>',
      role: 'professeur'
    };

    // Load conversations on page load
    document.addEventListener('DOMContentLoaded', function() {
      refreshConversations();

      // Set up periodic refresh
      setInterval(refreshConversations, 30000); // Refresh every 30 seconds
    });

    // Refresh conversations
    async function refreshConversations() {
      try {
        const response = await fetch('/api/message/conversations');
        const data = await response.json();

        if (data.success) {
          updateConversationsList(data.conversations);
        }
      } catch (error) {
        console.error('Error refreshing conversations:', error);
      }
    }

    // Update conversations list
    function updateConversationsList(conversations) {
      const conversationsList = document.getElementById('conversationList');
      if (!conversationsList) return;

      if (conversations.length === 0) {
        conversationsList.innerHTML = `
          <div class="text-center py-4 text-muted">
            <i class="bi bi-chat-dots fs-1 mb-3 d-block"></i>
            <p>Aucune conversation</p>
            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#composeModal">
              Commencer une conversation
            </button>
          </div>
        `;
        return;
      }

      conversationsList.innerHTML = conversations.map(conv => `
        <div class="conversation-item" onclick="selectConversation('${conv.id}', '${conv.participant_name}', '${conv.participant_role}')">
          <div class="conversation-avatar">
            ${conv.participant_name.split(' ').map(n => n.charAt(0)).join('').toUpperCase()}
          </div>
          <div class="conversation-content">
            <div class="conversation-header">
              <span class="conversation-name">${conv.participant_name}</span>
              <span class="conversation-time">${formatTime(conv.last_message_time)}</span>
            </div>
            <div class="conversation-preview">
              ${conv.last_message || 'Aucun message'}
            </div>
          </div>
          ${conv.unread_count > 0 ? `<div class="conversation-unread">${conv.unread_count}</div>` : ''}
        </div>
      `).join('');
    }

    // Format time helper
    function formatTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;

      if (diff < 24 * 60 * 60 * 1000) {
        return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
      } else {
        return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
      }
    }

    // Show success message
    function showSuccessMessage(message) {
      // Create a toast or alert
      const alert = document.createElement('div');
      alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
      alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
      alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;
      document.body.appendChild(alert);

      setTimeout(() => {
        if (alert.parentNode) {
          alert.parentNode.removeChild(alert);
        }
      }, 5000);
    }
  </script>
</body>
</html>
