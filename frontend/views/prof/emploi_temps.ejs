<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Mon <PERSON><PERSON><PERSON><PERSON> du Temps - NS School Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>
  
  <style>
    /* Page-specific styles */
    @media print {
      .no-print {
        display: none !important;
      }
      body {
        background: white;
      }
    }

    .planning-table {
      table-layout: fixed;
      width: 100%;
    }
    .planning-table th, .planning-table td {
      text-align: center;
      vertical-align: middle;
    }

    .stats-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 0.75rem;
      padding: 1.5rem;
      margin-bottom: 1rem;
    }

    .creneau-card {
      background: white;
      border-radius: 0.5rem;
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      border-left: 4px solid #667eea;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s;
    }

    .creneau-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .jour-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 1rem;
      text-align: center;
      font-weight: 600;
    }

    .empty-day {
      text-align: center;
      color: #6c757d;
      font-style: italic;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 0.5rem;
    }
  </style>
</head>
<body>

  <!-- Professor Topbar -->
  <%- include('../partials/prof-topbar') %>

  <!-- Professor Sidebar -->
  <% var currentPage = 'emploi-temps'; %>
  <%- include('../partials/prof-sidebar') %>

  <div class="dashboard-main">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-1">
          <i class="bi bi-calendar-week text-primary me-2"></i>
          Mon Emploi du Temps
        </h2>
        <p class="text-muted mb-0">
          Emploi du temps de <strong><%= professeur.prenom %> <%= professeur.nom %></strong>
          <% if (professeur.specialiste) { %>
            - <span class="badge bg-info"><%= professeur.specialiste %></span>
          <% } %>
        </p>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-primary no-print" onclick="window.print()">
          <i class="bi bi-printer"></i> Imprimer
        </button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="stats-card">
          <div class="d-flex align-items-center">
            <i class="bi bi-calendar-check fs-2 me-3"></i>
            <div>
              <h4 class="mb-0"><%= stats.totalCreneaux %></h4>
              <small>Créneaux/semaine</small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="d-flex align-items-center">
            <i class="bi bi-clock fs-2 me-3"></i>
            <div>
              <h4 class="mb-0"><%= Math.round(stats.heuresParSemaine) %>h</h4>
              <small>Heures/semaine</small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="d-flex align-items-center">
            <i class="bi bi-people fs-2 me-3"></i>
            <div>
              <h4 class="mb-0"><%= stats.totalClasses %></h4>
              <small>Classes enseignées</small>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="stats-card">
          <div class="d-flex align-items-center">
            <i class="bi bi-book fs-2 me-3"></i>
            <div>
              <h4 class="mb-0"><%= stats.totalMatieres %></h4>
              <small>Matières enseignées</small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Weekly Schedule -->
    <div class="row">
      <% joursSemaine.forEach(jour => { %>
        <div class="col-lg-6 col-xl-4 mb-4">
          <div class="jour-header">
            <i class="bi bi-calendar-day me-2"></i>
            <%= jour %>
          </div>
          
          <% if (emploiStructure[jour] && emploiStructure[jour].length > 0) { %>
            <% emploiStructure[jour].forEach(creneau => { %>
              <div class="creneau-card">
                <div class="d-flex justify-content-between align-items-start mb-2">
                  <h6 class="mb-0 text-primary"><%= creneau.nom_matiere %></h6>
                  <span class="badge bg-secondary"><%= creneau.nom_classe %></span>
                </div>
                <div class="d-flex justify-content-between text-muted small">
                  <span>
                    <i class="bi bi-clock me-1"></i>
                    <%= creneau.heure_debut %> - <%= creneau.heure_fin %>
                  </span>
                  <span>
                    <i class="bi bi-geo-alt me-1"></i>
                    <%= creneau.salle_nom || 'Salle non définie' %>
                  </span>
                </div>
              </div>
            <% }) %>
          <% } else { %>
            <div class="empty-day">
              <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
              Aucun cours programmé
            </div>
          <% } %>
        </div>
      <% }) %>
    </div>

    <!-- Detailed Table View -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="bi bi-table me-2"></i>
          Vue détaillée de l'emploi du temps
        </h5>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover planning-table mb-0">
            <thead class="table-primary">
              <tr>
                <th style="width: 15%">Jour</th>
                <th style="width: 20%">Matière</th>
                <th style="width: 12%">Début</th>
                <th style="width: 12%">Fin</th>
                <th style="width: 15%">Salle</th>
                <th style="width: 15%">Classe</th>
                <th style="width: 11%">Durée</th>
              </tr>
            </thead>
            <tbody>
              <% if (emploiTemps.length > 0) { %>
                <% emploiTemps.forEach(creneau => { %>
                  <tr>
                    <td><strong><%= creneau.jour_semaine %></strong></td>
                    <td>
                      <span class="badge bg-primary"><%= creneau.nom_matiere %></span>
                    </td>
                    <td><%= creneau.heure_debut %></td>
                    <td><%= creneau.heure_fin %></td>
                    <td>
                      <i class="bi bi-geo-alt text-muted me-1"></i>
                      <%= creneau.salle_nom || 'Non définie' %>
                    </td>
                    <td>
                      <span class="badge bg-secondary"><%= creneau.nom_classe %></span>
                    </td>
                    <td>
                      <% 
                        const debut = new Date(`1970-01-01T${creneau.heure_debut}`);
                        const fin = new Date(`1970-01-01T${creneau.heure_fin}`);
                        const duree = (fin - debut) / (1000 * 60); // en minutes
                      %>
                      <%= Math.floor(duree / 60) %>h<%= duree % 60 > 0 ? (duree % 60) + 'min' : '' %>
                    </td>
                  </tr>
                <% }) %>
              <% } else { %>
                <tr>
                  <td colspan="7" class="text-center text-muted p-4">
                    <i class="bi bi-calendar-x fs-1"></i>
                    <p class="mt-2">Aucun créneau programmé dans votre emploi du temps</p>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
