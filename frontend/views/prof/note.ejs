<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestion des notes - NS School Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    :root{
      --card-bg:#ffffff;
      --border:#e5e7eb;
      --muted:#6b7280;
      --text:#111827;
      --accent:#0d6efd;
      --soft:#f8fafc;
    }
    body { background-color: #f8f9fa; margin: 0; padding: 0; }
    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal { display: none !important; }
      body { background: white; }
    }
    .main-content { margin-left: 250px; margin-top: 60px; padding: 20px; }
    .page-header { display:flex; justify-content: space-between; align-items:center; gap:12px; flex-wrap:wrap; margin-bottom: 16px; }
    .subtle { color: var(--muted); }
    .card { border:1px solid var(--border); box-shadow: 0 1px 2px rgba(16, 24, 40, 0.06); }
    .card-header { background: var(--soft); font-weight:600; }
    .grid-2 { display:grid; grid-template-columns: 1fr 1fr; gap:12px; }
    @media (max-width: 768px){ .grid-2{ grid-template-columns: 1fr; } .main-content{ margin-left:0; } }
    .chip { display:inline-flex; align-items:center; gap:6px; border-radius:999px; padding:2px 8px; font-size:.75rem; border:1px solid var(--border); color:#374151; background:#f3f4f6; }
    .chip.type { background:#ecfeff; color:#164e63; border-color:#cffafe; }
    .chip.date { background:#fef3c7; color:#92400e; border-color:#fde68a; }
    .table thead th { background: var(--soft); border-bottom:1px solid var(--border); }
    .table tbody tr:hover { background:#fafafa; }
    .search-row { display:flex; gap:8px; align-items:center; flex-wrap:wrap; }
    .form-hint { font-size:.85rem; color:var(--muted); }
    .kpi { display:flex; gap:12px; }
    .kpi .badge { font-size:.9rem; }
    .sticky-actions { display:flex; gap:8px; align-items:center; }
  </style>
</head>
<body>
  <%- include('../partials/prof-topbar') %>
  <%
    let currentPage = 'notes';
  %>
  <%- include('../partials/prof-sidebar') %>

  <div class="main-content mt-5">
    <div class="page-header">
      <div>
        <h2 class="mb-0">Gestion des notes</h2>
        <div class="subtle">Saisissez et consultez les notes de vos élèves</div>
      </div>
      <div class="sticky-actions no-print">
        <button class="btn btn-outline-secondary" onclick="window.print()"><i class="bi bi-printer"></i> Imprimer</button>
      </div>
    </div>

    <!-- KPIs -->
    <div class="kpi mb-3">
      <span class="badge bg-primary">Élèves: <%= (eleves && eleves.length) || 0 %></span>
      <span class="badge bg-secondary">Matières: <%= (matieres && matieres.length) || 0 %></span>
      <span class="badge bg-success">Notes: <%= (notes && notes.length) || 0 %></span>
    </div>

    <!-- Formulaire d'ajout -->
    <div class="card mb-4">
      <div class="card-header">Ajouter une note</div>
      <div class="card-body">
        <form action="/prof/note/ajouter" method="POST" class="needs-validation" novalidate>
          <div class="grid-2">
            <div class="mb-3">
              <label for="eleve_matricule" class="form-label">Élève</label>
              <select id="eleve_matricule" name="eleve_matricule" class="form-select" required>
                <option value="">Sélectionner un élève…</option>
                <% eleves.forEach(e => { %>
                  <option value="<%= e.matricule %>"><%= e.nom %> <%= e.prenom %> (<%= e.matricule %>)</option>
                <% }) %>
              </select>
              <div class="invalid-feedback">Veuillez choisir un élève</div>
            </div>

            <div class="mb-3">
              <label for="matiere_id" class="form-label">Matière</label>
              <select id="matiere_id" name="matiere_id" class="form-select" required>
                <option value="">Sélectionner une matière…</option>
                <% matieres.forEach(m => { %>
                  <option value="<%= m.matiere_id %>"><%= m.nom %></option>
                <% }) %>
              </select>
              <div class="invalid-feedback">Veuillez choisir une matière</div>
            </div>

            <div class="mb-3">
              <label for="type_evaluation" class="form-label">Type</label>
              <select id="type_evaluation" name="type_evaluation" class="form-select" required>
                <option value="">Sélectionner un type…</option>
                <option value="Devoir">Devoir</option>
                <option value="Composition">Composition</option>
              </select>
              <div class="invalid-feedback">Veuillez préciser le type</div>
            </div>

            <div class="mb-3">
              <label for="date_evaluation" class="form-label">Date</label>
              <input id="date_evaluation" type="date" name="date_evaluation" class="form-control" required>
              <div class="invalid-feedback">Veuillez choisir une date</div>
            </div>

            <div class="mb-3">
              <label for="note" class="form-label">Note</label>
              <input id="note" type="number" step="0.01" min="0" max="20" name="note" class="form-control" required>
              <div class="form-hint">Échelle par défaut: 0 à 20</div>
              <div class="invalid-feedback">Veuillez saisir une note valide</div>
            </div>

            <div class="mb-3">
              <label for="coefficient" class="form-label">Coefficient (optionnel)</label>
              <input id="coefficient" type="number" step="0.1" min="0.1" max="10" name="coefficient" class="form-control">
            </div>
          </div>

          <div class="d-flex gap-2 mt-1">
            <button class="btn btn-primary"><i class="bi bi-plus-circle"></i> Ajouter</button>
            <button type="reset" class="btn btn-outline-secondary">Réinitialiser</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card mb-3 no-print">
      <div class="card-body">
        <div class="search-row">
          <div class="input-group" style="max-width:360px;">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input id="searchInput" type="text" class="form-control" placeholder="Rechercher par élève ou matière…">
          </div>
          <select id="filterType" class="form-select" style="max-width:220px;">
            <option value="">Tous les types</option>
            <option value="Devoir">Devoir</option>
            <option value="Composition">Composition</option>
          </select>
          <input id="filterDate" type="date" class="form-control" style="max-width:200px;">
          <button id="btnClearFilters" class="btn btn-outline-secondary"><i class="bi bi-x-circle"></i> Effacer</button>
        </div>
      </div>
    </div>

    <!-- Tableau des notes -->
    <div class="table-responsive table-box">
      <table class="table align-middle mb-0">
        <thead>
          <tr>
            <th>Élève</th>
            <th>Matière</th>
            <th>Note</th>
            <th>Type</th>
            <th>Date</th>
          </tr>
        </thead>
        <tbody id="notesBody">
          <% if (Array.isArray(notes) && notes.length) { %>
            <% notes.forEach(n => { %>
              <tr>
                <td><%= n.eleve_nom %> <%= n.eleve_prenom %> (<%= n.eleve_matricule %>)</td>
                <td><%= n.matiere_nom %></td>
                <td><span class="badge bg-primary"><%= Number(n.note).toFixed(2) %></span></td>
                <td><span class="chip type"><i class="bi bi-journal-text"></i> <%= n.type_evaluation %></span></td>
                <td><span class="chip date"><i class="bi bi-calendar-event"></i> <%= n.date_evaluation ? n.date_evaluation.toISOString().split('T')[0] : '' %></span></td>
              </tr>
            <% }) %>
          <% } else { %>
            <tr>
              <td colspan="5" class="text-center text-muted py-4">
                <i class="bi bi-clipboard-data"></i> Aucune note enregistrée
              </td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Client-side validation
    (function () {
      const form = document.querySelector('form.needs-validation');
      if (!form) return;
      form.addEventListener('submit', function (e) {
        if (!form.checkValidity()) {
          e.preventDefault();
          e.stopPropagation();
        }
        form.classList.add('was-validated');
      }, false);
    })();

    // Lightweight table filtering
    (function(){
      const body = document.getElementById('notesBody');
      const search = document.getElementById('searchInput');
      const type = document.getElementById('filterType');
      const date = document.getElementById('filterDate');
      const clear = document.getElementById('btnClearFilters');
      if (!body) return;

      function normalize(s){ return (s||'').toString().toLowerCase(); }

      function matches(row){
        const tds = row.querySelectorAll('td');
        const s = normalize(search?.value);
        const typeVal = type?.value || '';
        const dateVal = date?.value || '';

        const txt = normalize(Array.from(tds).slice(0,2).map(td => td.textContent).join(' ')); // élève + matière
        const rowType = normalize(tds[3]?.innerText);
        const rowDate = (tds[4]?.innerText || '').trim();

        if (s && !txt.includes(s)) return false;
        if (typeVal && !rowType.includes(normalize(typeVal))) return false;
        if (dateVal && !rowDate.includes(dateVal)) return false;
        return true;
      }

      function apply(){
        const rows = body.querySelectorAll('tr');
        rows.forEach(r => { r.style.display = matches(r) ? '' : 'none'; });
      }

      search?.addEventListener('input', apply);
      type?.addEventListener('change', apply);
      date?.addEventListener('change', apply);
      clear?.addEventListener('click', () => {
        if (search) search.value = '';
        if (type) type.value = '';
        if (date) date.value = '';
        apply();
      });
    })();
  </script>
</body>
</html>
