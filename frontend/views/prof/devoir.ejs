<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Gestion des devoirs</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }
    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }
    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }
    .icon-cours { color: #0d6efd; }
    .icon-eleves { color: #20c997; }
    .icon-devoirs { color: #ffc107; }
    .icon-absence { color: #dc3545; }
    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    .initial-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }
    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }
  </style>
</head>
<body>

  <!-- Professor Topbar -->
  <%- include('../partials/prof-topbar') %>

  <!-- Professor Sidebar -->
  <%
    // Set current page for active sidebar link
    let currentPage = 'devoirs';
  %>
  <%- include('../partials/prof-sidebar') %>
<div class="main-content">
  <h2 class="mb-4">📚 Liste des devoirs</h2>

  <!-- Bouton ajouter un devoir -->
  <button class="btn btn-primary mb-3" data-bs-toggle="modal" data-bs-target="#modalAjoutDevoir">
    <i class="bi bi-plus-circle"></i> Ajouter un devoir
  </button>

  <!-- Tableau des devoirs -->
  <table class="table table-bordered">
    <thead class="table-dark">
      <tr>
        <th>Titre</th>
        <th>Matière</th>
        <th>Date création</th>
        <th>Date limite</th>
        <th>Fichier</th>
        <th>Soumissions</th>
      </tr>
    </thead>
    <tbody>
      <% devoirs.forEach((devoir) => { %>
        <tr>
          <td><%= devoir.titre %></td>
          <td><%= devoir.nom_matiere %></td>
          <td><%= devoir.date_creation.toISOString().split('T')[0] %></td>
          <td><%= devoir.date_limite.toISOString().split('T')[0] %></td>
          <td>
            <% if (devoir.fichier) { %>
              <a href="/uploads/devoirs/<%= devoir.fichier %>" target="_blank" class="btn btn-sm btn-outline-primary" download>Télécharger</a>

            <% } else { %>
              Aucun
            <% } %>
          </td>
          <td>
            <button class="btn btn-sm btn-secondary" data-bs-toggle="modal" data-bs-target="#modalSoumissions<%= devoir.devoir_id %>">
              Voir (<%= devoir.soumissions.length %>)
            </button>
          </td>
        </tr>

        <!-- Modal de soumissions pour ce devoir -->
        <div class="modal fade" id="modalSoumissions<%= devoir.devoir_id %>" tabindex="-1" aria-hidden="true">
          <div class="modal-dialog modal-lg">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Soumissions pour : <%= devoir.titre %></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
              </div>
              <div class="modal-body">
                <% if (devoir.soumissions.length === 0) { %>
                  <p>Aucune soumission.</p>
                <% } else { %>
                  <table class="table">
                    <thead>
                      <tr>
                        <th>Élève</th>
                        <th>Date de soumission</th>
                        <th>Fichier</th>
                        <th>Note</th>
                        <th>Remarque</th>
                        <th>Corriger</th>
                      </tr>
                    </thead>
                    <tbody>
                      <% devoir.soumissions.forEach((s) => { %>
                        <tr>
                          <td><%= s.nom_eleve %></td>
                          <td><%= new Date(s.date_soumission).toLocaleString() %></td>
                          <td><a href="/uploads/devoirs/<%= s.fichier %>" target="_blank">Voir</a></td>
                          <td><%= s.note ?? '—' %></td>
                          <td><%= s.remarque ?? '' %></td>
                          <td>
                            <form action="/prof/devoir/corriger" method="POST" class="d-flex" style="gap: 5px;">
                              <input type="hidden" name="soumission_id" value="<%= s.soumission_id %>" />
                              <input type="number" name="note" placeholder="Note" step="0.01" class="form-control form-control-sm" required />
                              <input type="text" name="remarque" placeholder="Remarque" class="form-control form-control-sm" />
                              <button type="submit" class="btn btn-sm btn-success">✅</button>
                            </form>
                          </td>
                        </tr>
                      <% }); %>
                    </tbody>
                  </table>
                <% } %>
              </div>
            </div>
          </div>
        </div>

      <% }); %>
    </tbody>
  </table>

  <!-- Modal ajout devoir -->
  <div class="modal fade" id="modalAjoutDevoir" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
      <form class="modal-content" action="/prof/devoir/ajouter" method="POST" enctype="multipart/form-data">
        <div class="modal-header">
          <h5 class="modal-title">Ajouter un devoir</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
        </div>
        <div class="modal-body">
          <div class="mb-2">
            <label class="form-label">Titre</label>
            <input type="text" name="titre" class="form-control" required>
          </div>
          <div class="mb-2">
            <label class="form-label">Description</label>
            <textarea name="description" class="form-control" rows="3" required></textarea>
          </div>
          <div class="mb-2">
            <label class="form-label">Matière</label>
            <select name="matiere_id" class="form-select" required>
              <% matieres.forEach((m) => { %>
                <option value="<%= m.matiere_id %>"><%= m.nom %></option>
              <% }); %>
            </select>
          </div>
          <div class="mb-2">
            <label class="form-label">Date limite</label>
            <input type="date" name="date_limite" class="form-control" required>
          </div>
          <div class="mb-2">
            <label class="form-label">Fichier (PDF, DOC, etc.)</label>
            <input type="file" name="fichier" class="form-control">
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">Ajouter</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        </div>
      </form>
    </div>
  </div>
</div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
