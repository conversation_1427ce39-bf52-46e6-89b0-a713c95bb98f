<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>NS School Manager - Admin</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

    .topbar {
      width: 100%;
      height: 60px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding:40px;
      margin-bottom: 50px;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color:white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
    
      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body>

  <!-- Admin Topbar -->
  <%- include('./partials/admin-topbar') %>

  <!-- Sidebar partial include to standardize admin navigation -->
  <%- include('./partials/sidebar', { currentPage: 'dashboard' }) %>


  <!-- Main Content -->
  <div class="main-content">

    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>Tableau de bord</h2>
<button class="btn btn-primary" onclick="location.href='/admin/utilisateur'">
  <i class="bi bi-plus-circle me-2"></i> Nouveau
</button>
    </div>

<!-- Statistiques -->
<div class="row mb-4">
  <div class="col-md-3">
    <div class="card-stat">
      <div class="icon icon-eleve"><i class="bi bi-people-fill"></i></div>
      <div>
        <h6 class="mb-0">Élèves</h6>
        <p class="fs-5 fw-bold"><%= stats.eleves %></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card-stat">
      <div class="icon icon-prof"><i class="bi bi-person-badge-fill"></i></div>
      <div>
        <h6 class="mb-0">Professeurs</h6>
        <p class="fs-5 fw-bold"><%= stats.profs %></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card-stat">
      <div class="icon icon-classe"><i class="bi bi-building"></i></div>
      <div>
        <h6 class="mb-0">Classes</h6>
        <p class="fs-5 fw-bold"><%= stats.classes %></p>
      </div>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card-stat">
      <div class="icon icon-absence"><i class="bi bi-x-circle-fill"></i></div>
      <div>
        <h6 class="mb-0">Absences</h6>
        <p class="fs-5 fw-bold"><%= stats.absences %></p>
      </div>
    </div>
  </div>
</div>
    <!-- Activités récentes -->
    <div class="table-box p-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="mb-0">Activités récentes</h5>
        <a href="#" class="btn btn-sm btn-outline-primary">Voir plus</a>
      </div>
      <div class="table-responsive">
        <table class="table table-hover align-middle">
          <thead class="table-light">
            <tr>
              <th>Utilisateur</th>
              <th>Action</th>
              <th>Date</th>
              <th>Statut</th>
            </tr>
          </thead>
          <tbody>
            <% if (activites && activites.length > 0) { %>
              <% activites.forEach(act => { %>
                <tr>
                  <td>
                    <div class="d-flex align-items-center gap-2">
                      <div class="initial-circle <%= act.statutClass %>"><%= act.utilisateur.initials %></div>
                      <div>
                        <strong><%= act.utilisateur.fullName %></strong><br />
                        <span class="role-text"><%= act.utilisateur.role %></span>
                      </div>
                    </div>
                  </td>
                  <td><%= act.action %></td>
                  <td><%= act.date %></td>
                  <td><span class="badge <%= act.statutClass %>"><%= act.statut %></span></td>
                </tr>
              <% }); %>
            <% } else { %>
              <tr><td colspan="4" class="text-center text-muted">Aucune activité récente</td></tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>

  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
