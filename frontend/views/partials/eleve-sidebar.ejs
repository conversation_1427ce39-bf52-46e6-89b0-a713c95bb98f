<!-- Student Sidebar Partial -->
<div class="sidebar">
  <ul class="nav flex-column">
    <li class="nav-item">
      <a href="/dashboard-eleve" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'dashboard') ? 'active' : '' %>">
        <i class="bi bi-speedometer2"></i>
        <span>Tableau de bord</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/notes" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'notes') ? 'active' : '' %>">
        <i class="bi bi-journal-text"></i>
        <span>Notes</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/bulletin" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'bulletin') ? 'active' : '' %>">
        <i class="bi bi-file-earmark-text"></i>
        <span>Bulletin</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/cours" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'cours') ? 'active' : '' %>">
        <i class="bi bi-book"></i>
        <span>Cours</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/devoirs" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'devoirs') ? 'active' : '' %>">
        <i class="bi bi-clipboard-check"></i>
        <span>Devoirs</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/emploi-temps" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'emploi-temps') ? 'active' : '' %>">
        <i class="bi bi-calendar-week"></i>
        <span>Emploi du temps</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/absences" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'absences') ? 'active' : '' %>">
        <i class="bi bi-x-circle"></i>
        <span>Absences</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/eleve/Annonces" class="nav-link <%= (typeof currentPage !== 'undefined' && currentPage === 'annonces') ? 'active' : '' %>">
        <i class="bi bi-megaphone"></i>
        <span>Annonces</span>
      </a>
    </li>
    <li class="nav-item mt-auto">
      <a href="/logout" class="nav-link text-danger">
        <i class="bi bi-box-arrow-right"></i>
        <span>Déconnexion</span>
      </a>
    </li>
  </ul>
</div>

<style>
/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  width: 250px;
  height: calc(100vh - 60px);
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  z-index: 999;
  box-shadow: 1px 0 3px 0 rgb(0 0 0 / 0.1);
}

.sidebar .nav {
  padding: 1rem 0;
  height: 100%;
}

.sidebar .nav-item {
  margin: 0.25rem 0;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-radius: 0;
}

.sidebar .nav-link:hover {
  background-color: #f3f4f6;
  color: #374151;
  transform: translateX(2px);
}

.sidebar .nav-link.active {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-weight: 600;
}

.sidebar .nav-link.active:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateX(0);
}

.sidebar .nav-link i {
  width: 20px;
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.sidebar .nav-link span {
  font-size: 0.9rem;
}

.sidebar .nav-link.text-danger:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* Main content adjustment */
.dashboard-main {
  margin-left: 250px;
  margin-top: 60px;
  padding: 2rem;
  min-height: calc(100vh - 60px);
  background-color: #f9fafb;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .dashboard-main {
    margin-left: 0;
  }
}
</style>
