<%
// Main message layout partial
// Parameters: userRole, currentUser, conversations, messages
const userRole = locals.userRole || 'admin';
const currentUser = locals.currentUser || {};
const conversations = locals.conversations || [];
const messages = locals.messages || [];
const isAdmin = userRole === 'admin';
const isProfesseur = userRole === 'professeur';
const isEleve = userRole === 'eleve';

// Get user display name
const getUserDisplayName = (user) => {
  if (user.nom && user.prenom) {
    return `${user.prenom} ${user.nom}`;
  }
  return user.matricule || 'Utilisateur';
};

// Get total unread count
const getTotalUnreadCount = () => {
  return conversations.reduce((total, conv) => total + (conv.unread_count || 0), 0);
};
%>

<!-- Include Message Styles -->
<%- include('message-styles') %>

<!-- Main Message Container -->
<div class="message-container">
  <!-- Conversation Sidebar -->
  <div class="conversation-sidebar">
    <!-- Sidebar Header -->
    <div class="conversation-header">
      <div class="d-flex align-items-center justify-content-between">
        <h5 class="mb-0">
          <i class="bi bi-chat-dots me-2"></i>Messagerie
        </h5>
        <div class="d-flex align-items-center gap-2">
          <!-- Unread Count Badge -->
          <% if (getTotalUnreadCount() > 0) { %>
            <span class="badge bg-light text-primary rounded-pill">
              <%= getTotalUnreadCount() %>
            </span>
          <% } %>
          
          <!-- Compose Button -->
          <% if (!isEleve) { %>
            <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#composeModal" title="Nouveau message">
              <i class="bi bi-plus-lg"></i>
            </button>
          <% } %>
          
          <!-- Refresh Button -->
          <button class="btn btn-light btn-sm" onclick="refreshConversations()" title="Actualiser">
            <i class="bi bi-arrow-clockwise"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Search Bar -->
    <div class="conversation-search">
      <div class="input-group">
        <span class="input-group-text bg-white border-end-0">
          <i class="bi bi-search text-muted"></i>
        </span>
        <input type="text" class="form-control border-start-0" placeholder="Rechercher..." 
               id="conversationSearch" onkeyup="filterConversations(this.value)">
      </div>
    </div>

    <!-- Conversation Filters (for admin and professors) -->
    <% if (!isEleve) { %>
      <div class="conversation-filters px-3 py-2 border-bottom">
        <div class="btn-group btn-group-sm w-100" role="group">
          <input type="radio" class="btn-check" name="conversationFilter" id="filterAll" value="all" checked>
          <label class="btn btn-outline-primary" for="filterAll">Tous</label>
          
          <input type="radio" class="btn-check" name="conversationFilter" id="filterUnread" value="unread">
          <label class="btn btn-outline-primary" for="filterUnread">Non lus</label>
          
          <input type="radio" class="btn-check" name="conversationFilter" id="filterPrivate" value="privé">
          <label class="btn btn-outline-primary" for="filterPrivate">Privés</label>
          
          <input type="radio" class="btn-check" name="conversationFilter" id="filterAnnounce" value="annonce">
          <label class="btn btn-outline-primary" for="filterAnnounce">Annonces</label>
        </div>
      </div>
    <% } %>

    <!-- Conversation List -->
    <div class="conversation-list" id="conversationList">
      <% if (conversations.length === 0) { %>
        <div class="empty-state p-4 text-center">
          <i class="bi bi-chat-dots text-muted"></i>
          <p class="text-muted mb-0 mt-2">Aucune conversation</p>
        </div>
      <% } else { %>
        <% conversations.forEach(conversation => { %>
          <%- include('conversation-item', { conversation, currentUser }) %>
        <% }) %>
      <% } %>
    </div>
  </div>

  <!-- Message Thread Panel -->
  <div class="message-thread">
    <!-- Thread Header -->
    <div class="thread-header" id="threadHeader" style="display: none;">
      <div class="thread-title">
        <div class="thread-avatar">?</div>
        <div class="thread-info">
          <h6 class="mb-0">Sélectionner une conversation</h6>
          <small class="text-muted">Choisissez une conversation pour voir les messages</small>
        </div>
      </div>
      
      <div class="thread-actions">
        <!-- Thread Actions -->
        <div class="dropdown">
          <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" 
                  data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-three-dots"></i>
          </button>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="markAllAsRead()">
              <i class="bi bi-check2-all me-2"></i>Marquer comme lu
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="archiveConversation()">
              <i class="bi bi-archive me-2"></i>Archiver
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="#" onclick="deleteConversation()">
              <i class="bi bi-trash me-2"></i>Supprimer
            </a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Message List -->
    <div class="message-list" id="messageList">
      <div class="empty-state">
        <i class="bi bi-chat-text"></i>
        <h5 class="mt-3 mb-2">Bienvenue dans votre messagerie</h5>
        <p class="text-muted">
          <% if (isEleve) { %>
            Consultez vos annonces et messages de l'administration.
          <% } else { %>
            Sélectionnez une conversation ou créez un nouveau message pour commencer.
          <% } %>
        </p>
        <% if (!isEleve) { %>
          <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#composeModal">
            <i class="bi bi-plus-lg me-2"></i>Nouveau message
          </button>
        <% } %>
      </div>
    </div>

    <!-- Compose Area (for active conversations) -->
    <% if (!isEleve) { %>
      <div class="compose-area" id="composeArea" style="display: none;">
        <form class="compose-form" onsubmit="sendQuickMessage(event)">
          <textarea class="compose-input" placeholder="Tapez votre message..." 
                    rows="1" id="quickMessageInput" maxlength="1000"></textarea>
          
          <div class="compose-actions">
            <button type="button" class="btn btn-outline-secondary btn-sm" title="Pièce jointe">
              <i class="bi bi-paperclip"></i>
            </button>
            <button type="submit" class="btn-send" title="Envoyer" disabled>
              <i class="bi bi-send"></i>
            </button>
          </div>
        </form>
      </div>
    <% } %>
  </div>
</div>

<!-- Include Compose Modal -->
<% if (!isEleve) { %>
  <%- include('compose-modal', { userRole, availableRecipients: locals.availableRecipients || [] }) %>
<% } %>

<script>
// Message Layout Functionality
let currentConversationId = null;
let currentParticipant = null;
let allConversations = <%- JSON.stringify(conversations) %>;
let filteredConversations = [...allConversations];

function persistActiveConversation(conversationId, participantName, participantRole) {
  try {
    localStorage.setItem('activeConversation', JSON.stringify({
      id: conversationId,
      name: participantName,
      role: participantRole
    }));
  } catch (e) {}
}

function restoreActiveConversation() {
  try {
    const data = localStorage.getItem('activeConversation');
    if (data) {
      const { id, name, role } = JSON.parse(data);
      if (id && name && role) {
        // Try to select and load the conversation if it exists
        setTimeout(() => {
          const item = document.querySelector(`.conversation-item[data-conversation-id='${id}']`);
          if (item) {
            item.click();
          }
        }, 200);
      }
    }
  } catch (e) {}
}

document.addEventListener('DOMContentLoaded', function() {
  initializeMessageLayout();
});

function initializeMessageLayout() {
  // Initialize conversation filters
  const filterRadios = document.querySelectorAll('input[name="conversationFilter"]');
  filterRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      filterConversationsByType(this.value);
    });
  });

  // Initialize quick message input
  const quickInput = document.getElementById('quickMessageInput');
  if (quickInput) {
    quickInput.addEventListener('input', function() {
      const sendBtn = document.querySelector('.btn-send');
      sendBtn.disabled = this.value.trim().length === 0;
      // Auto-resize textarea
      this.style.height = 'auto';
      this.style.height = Math.min(this.scrollHeight, 100) + 'px';
    });
  }

  // Restore last open conversation if available
  restoreActiveConversation();

  // Load initial data
  refreshConversations();
}

window.selectConversation = function selectConversation(conversationId, participantName, participantRole) {
  // Remove active class from all items
  document.querySelectorAll('.conversation-item').forEach(item => {
    item.classList.remove('active');
  });
  // Add active class to selected item
  const thisItem = document.querySelector(`.conversation-item[data-conversation-id='${conversationId}']`);
  if (thisItem) thisItem.classList.add('active');
  // Set global state
  currentConversationId = conversationId;
  currentParticipant = { name: participantName, role: participantRole };
  // Persist active conversation
  persistActiveConversation(conversationId, participantName, participantRole);
  // Update thread header
  updateThreadHeader(participantName, participantRole);
  // Show thread header and compose area
  document.getElementById('threadHeader').style.display = '';
  const composeArea = document.getElementById('composeArea');
  if (composeArea) composeArea.style.display = '';
  // Load messages
  window.loadConversationMessages(conversationId, participantName, participantRole);
}

// Thread header update function (fix for ReferenceError)
window.updateThreadHeader = function updateThreadHeader(participantName, participantRole) {
  const threadTitle = document.querySelector('.thread-title');
  if (!threadTitle) return;
  const avatar = threadTitle.querySelector('.thread-avatar');
  const info = threadTitle.querySelector('.thread-info');
  if (avatar) {
    avatar.textContent = participantName.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('');
    avatar.style.backgroundColor = getRoleColor(participantRole);
  }
  if (info) {
    info.querySelector('h6').textContent = participantName;
    info.querySelector('small').textContent = participantRole;
  }
};

// Utility functions for client-side rendering
function getInitials(name) {
  if (!name) return '?';
  return name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('');
}
function getRoleColor(role) {
  switch(role) {
    case 'admin': return '#dc3545';
    case 'professeur': return '#198754';
    case 'eleve': return '#0d6efd';
    default: return '#6c757d';
  }
}
function formatTime(date) {
  const now = new Date();
  const diff = now - date;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  if (minutes < 1) return 'À l\'instant';
  if (minutes < 60) return `${minutes}min`;
  if (hours < 24) return `${hours}h`;
  if (days < 7) return `${days}j`;
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
}
function truncateMessage(message, maxLength = 50) {
  if (!message) return 'Aucun message';
  return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
}

function refreshConversations() {
  fetch('/api/message/conversations')
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        allConversations = data.conversations;
        filteredConversations = [...allConversations];
        updateConversationList();
        updateUnreadCount();
        // Try to restore active conversation after refresh
        restoreActiveConversation();
      }
    })
    .catch(error => {
      console.error('Error refreshing conversations:', error);
    });
}

function filterConversations(searchTerm) {
  const term = searchTerm.toLowerCase();
  filteredConversations = allConversations.filter(conv => {
    const participantName = (conv.participant_name || conv.sender_matricule || '').toLowerCase();
    const lastMessage = (conv.last_message || '').toLowerCase();
    return participantName.includes(term) || lastMessage.includes(term);
  });
  updateConversationList();
}

function filterConversationsByType(type) {
  if (type === 'all') {
    filteredConversations = [...allConversations];
  } else if (type === 'unread') {
    filteredConversations = allConversations.filter(conv => conv.unread_count > 0);
  } else {
    filteredConversations = allConversations.filter(conv => conv.type === type);
  }
  updateConversationList();
}

function updateConversationList() {
  const listContainer = document.getElementById('conversationList');
  if (!listContainer) return;

  if (filteredConversations.length === 0) {
    listContainer.innerHTML = `
      <div class="empty-state p-4 text-center">
        <i class="bi bi-chat-dots text-muted"></i>
        <p class="text-muted mb-0 mt-2">Aucune conversation trouvée</p>
      </div>
    `;
    return;
  }

  // Re-render conversation items
  let html = '';
  filteredConversations.forEach(conversation => {
    // Use EJS partial include for each conversation item
    // This is a workaround for client-side only; in production, consider using a front-end framework
    html += `<div class="conversation-item${conversation.unread_count > 0 ? ' unread' : ''}${conversation.id === currentConversationId ? ' active' : ''}" 
      data-conversation-id="${conversation.id}" 
      data-participant="${conversation.participant_name}" 
      onclick="selectConversation('${conversation.id}', '${conversation.participant_name}', '${conversation.participant_role}')">
      <div class="d-flex align-items-center">
        <div class="conversation-avatar" style="background-color: ${getRoleColor(conversation.participant_role)}">
          ${getInitials(conversation.participant_name)}
        </div>
        <div class="conversation-content">
          <div class="conversation-title">
            <span class="participant-name">${conversation.participant_name}</span>
            <span class="conversation-time">${formatTime(new Date(conversation.last_message_time))}</span>
          </div>
          <div class="d-flex align-items-center justify-content-between">
            <div class="conversation-preview">
              ${conversation.type === 'annonce' ? '<i class="bi bi-megaphone me-1"></i>' : ''}
              ${truncateMessage(conversation.last_message)}
            </div>
            <span class="badge rounded-pill" style="background-color: ${getRoleColor(conversation.participant_role)}; font-size: 0.7rem;">
              ${conversation.participant_role}
            </span>
          </div>
        </div>
      </div>
      ${conversation.unread_count > 0 ? `<div class="unread-badge">${conversation.unread_count > 99 ? '99+' : conversation.unread_count}</div>` : ''}
      ${conversation.type === 'annonce' ? '<div class="position-absolute" style="top: 8px; left: 8px;"><i class="bi bi-megaphone text-warning" title="Annonce"></i></div>' : ''}
    </div>`;
  });
  listContainer.innerHTML = html;

  // Re-highlight active conversation after list update
  if (currentConversationId) {
    const activeItem = document.querySelector(`.conversation-item[data-conversation-id='${currentConversationId}']`);
    if (activeItem) activeItem.classList.add('active');
  }
}

function updateUnreadCount() {
  const totalUnread = allConversations.reduce((total, conv) => total + (conv.unread_count || 0), 0);
  const badge = document.querySelector('.conversation-header .badge');
  
  if (totalUnread > 0) {
    if (badge) {
      badge.textContent = totalUnread;
    } else {
      // Create badge if it doesn't exist
      const badgeHtml = `<span class="badge bg-light text-primary rounded-pill">${totalUnread}</span>`;
      document.querySelector('.conversation-header .d-flex').insertAdjacentHTML('beforeend', badgeHtml);
    }
  } else if (badge) {
    badge.remove();
  }
}

function sendQuickMessage(event) {
  event.preventDefault();
  const input = document.getElementById('quickMessageInput');
  const message = input.value.trim();
  if (!message || !currentConversationId) return;
  const sendBtn = document.querySelector('.btn-send');
  sendBtn.disabled = true;
  sendBtn.innerHTML = '<div class="loading-spinner"></div>';
  fetch('/api/message/send-quick', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ conversation_id: currentConversationId, contenu: message, type: 'privé' })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      input.value = '';
      input.style.height = 'auto';
      loadConversationMessages(currentConversationId, currentParticipant.name, currentParticipant.role);
      // Persist active conversation again in case it changed
      persistActiveConversation(currentConversationId, currentParticipant.name, currentParticipant.role);
    } else {
      alert('Erreur lors de l\'envoi du message');
    }
  })
  .catch(error => {
    console.error('Error sending message:', error);
    alert('Erreur lors de l\'envoi du message');
  })
  .finally(() => {
    sendBtn.disabled = false;
    sendBtn.innerHTML = '<i class="bi bi-send"></i>';
  });
}

window.loadConversationMessages = function loadConversationMessages(conversationId, participantName, participantRole) {
  const messageList = document.getElementById('messageList');
  if (!messageList) return;
  // Show loading state
  messageList.innerHTML = '<div class="text-center p-4"><div class="loading-spinner"></div></div>';
  fetch(`/api/message/conversation/${conversationId}/messages`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        displayMessages(data.messages, participantName);
      } else {
        messageList.innerHTML = '<div class="text-center p-4 text-muted">Erreur lors du chargement des messages</div>';
      }
    })
    .catch(error => {
      console.error('Error loading messages:', error);
      messageList.innerHTML = '<div class="text-center p-4 text-muted">Erreur lors du chargement des messages</div>';
    });
};

function displayMessages(messages, participantName) {
  const messageList = document.getElementById('messageList');
  if (!messageList) return;

  if (messages.length === 0) {
    messageList.innerHTML = `
      <div class="empty-state">
        <i class="bi bi-chat-text"></i>
        <h5 class="mt-3 mb-2">Aucun message</h5>
        <p class="text-muted">Commencez une conversation avec ${participantName}</p>
      </div>
    `;
    return;
  }

  let messagesHtml = '';
  let lastSender = null;
  let lastDate = null;

  messages.forEach((message, index) => {
    const messageDate = new Date(message.date_envoi);
    const isOwnMessage = message.sender_matricule === (typeof currentUser !== 'undefined' ? currentUser.matricule : '');
    const isGrouped = lastSender === message.sender_matricule &&
                     lastDate &&
                     (messageDate - lastDate) < 300000; // 5 minutes

    // Add date separator if needed
    if (!lastDate || messageDate.toDateString() !== lastDate.toDateString()) {
      messagesHtml += `
        <div class="message-date-separator">
          <span>${formatMessageDate(messageDate)}</span>
        </div>
      `;
    }

    // Create message bubble
    messagesHtml += `
      <div class="message-bubble ${isOwnMessage ? 'sent' : 'received'} ${isGrouped ? 'grouped' : ''}">
        ${!isGrouped && !isOwnMessage ? `<div class="message-sender">${message.sender_name}</div>` : ''}
        <div class="message-content">
          ${message.subject ? `<div class="message-subject">${message.subject}</div>` : ''}
          <div class="message-text">${escapeHtml(message.contenu)}</div>
          ${message.message_type === 'annonce' ? `<div class="message-type-badge">Annonce</div>` : ''}
        </div>
        <div class="message-time">${formatMessageTime(messageDate)}</div>
      </div>
    `;

    lastSender = message.sender_matricule;
    lastDate = messageDate;
  });

  messageList.innerHTML = messagesHtml;
  messageList.scrollTop = messageList.scrollHeight;
}

// Helper functions for message display
function formatMessageDate(date) {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (date.toDateString() === today.toDateString()) {
    return "Aujourd'hui";
  } else if (date.toDateString() === yesterday.toDateString()) {
    return "Hier";
  } else {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}

function formatMessageTime(date) {
  return date.toLocaleTimeString('fr-FR', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

// Thread actions
function markAllAsRead() {
  if (!currentConversationId) return;
  
  fetch(`/api/message/conversation/${currentConversationId}/read-all`, {
    method: 'POST'
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      refreshConversations();
    }
  })
  .catch(error => {
    console.error('Error marking all as read:', error);
  });
}

function archiveConversation() {
  if (!currentConversationId) return;
  
  if (confirm('Archiver cette conversation ?')) {
    fetch(`/api/message/conversation/${currentConversationId}/archive`, {
      method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        refreshConversations();
        // Clear current conversation
        currentConversationId = null;
        document.getElementById('threadHeader').style.display = 'none';
        document.getElementById('composeArea').style.display = 'none';
        document.getElementById('messageList').innerHTML = `
          <div class="empty-state">
            <i class="bi bi-chat-text"></i>
            <h5 class="mt-3 mb-2">Conversation archivée</h5>
            <p class="text-muted">Sélectionnez une autre conversation</p>
          </div>
        `;
      }
    })
    .catch(error => {
      console.error('Error archiving conversation:', error);
    });
  }
}

function deleteConversation() {
  if (!currentConversationId) return;
  
  if (confirm('Supprimer définitivement cette conversation ? Cette action est irréversible.')) {
    fetch(`/api/message/conversation/${currentConversationId}`, {
      method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        refreshConversations();
        // Clear current conversation
        currentConversationId = null;
        document.getElementById('threadHeader').style.display = 'none';
        document.getElementById('composeArea').style.display = 'none';
        document.getElementById('messageList').innerHTML = `
          <div class="empty-state">
            <i class="bi bi-chat-text"></i>
            <h5 class="mt-3 mb-2">Conversation supprimée</h5>
            <p class="text-muted">Sélectionnez une autre conversation</p>
          </div>
        `;
      }
    })
    .catch(error => {
      console.error('Error deleting conversation:', error);
    });
  }
}

// Mobile responsiveness
function toggleSidebar() {
  const sidebar = document.querySelector('.conversation-sidebar');
  sidebar.classList.toggle('collapsed');
}

// Auto-refresh conversations every 30 seconds
setInterval(refreshConversations, 30000);
</script>
