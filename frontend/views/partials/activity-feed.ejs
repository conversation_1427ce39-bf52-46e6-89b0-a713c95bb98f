
<div class="dashboard-card slide-up">
  <div class="card-header">
    <h3 class="card-title">
      <i class="bi bi-activity"></i>
      <%= title || 'Activités récentes' %>
    </h3>
  </div>

  <div class="card-body">
    <% if (!activities || activities.length === 0) { %>
      <div class="text-center py-4">
        <i class="bi bi-inbox display-4 text-muted mb-3"></i>
        <p class="text-muted"><%= emptyMessage || 'Aucune activité récente' %></p>
      </div>
    <% } else { %>
      <div class="activity-feed">
        <% activities.slice(0, 8).forEach(function(activity) { %>
          <div class="activity-item">
            <div class="activity-icon">
              <i class="bi bi-activity"></i>
            </div>

            <div class="activity-content">
              <div class="activity-title">
                <%= activity.action || activity.title || 'Activité' %>
              </div>

              <div class="activity-description">
                <% if (activity.nom && activity.prenom) { %>
                  Par <%= activity.prenom %> <%= activity.nom %>
                <% } else if (activity.description) { %>
                  <%= activity.description %>
                <% } else { %>
                  Activité système
                <% } %>
              </div>

              <div class="activity-time">
                <%= activity.date || activity.created_at || 'Il y a quelques instants' %>
              </div>
            </div>

            <% if (activity.statut) { %>
              <div class="activity-status">
                <span class="badge bg-<%= activity.statut === 'success' ? 'success' : activity.statut === 'error' ? 'danger' : 'secondary' %>">
                  <%= activity.statut %>
                </span>
              </div>
            <% } %>
          </div>
        <% }) %>

        <% if (activities.length > 8) { %>
          <div class="text-center mt-3">
            <a href="#" class="btn btn-outline-primary btn-sm">
              <i class="bi bi-arrow-down-circle me-1"></i>
              Voir plus d'activités
            </a>
          </div>
        <% } %>
      </div>
    <% } %>
  </div>
</div>
