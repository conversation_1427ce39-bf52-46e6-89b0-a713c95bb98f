<%
// Message bubble partial for message system
// Parameters: message, currentUser, isGrouped (optional)
const message = locals.message || {};
const currentUser = locals.currentUser || {};
const isGrouped = locals.isGrouped || false; // If this message is grouped with previous message from same sender
const isSent = message.sender_matricule === currentUser.matricule;
const messageTime = message.date_envoi ? new Date(message.date_envoi) : new Date();
const isRead = message.lu || false;
const messageType = message.type || 'privé';

// Format time display
const formatMessageTime = (date) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  if (messageDate.getTime() === today.getTime()) {
    return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
  } else if (now - messageDate < 7 * 24 * 60 * 60 * 1000) {
    return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });
  }
};

// Get sender name for display
const getSenderName = () => {
  if (isSent) return 'Vous';
  return message.sender_name || message.sender_matricule || 'Inconnu';
};

// Get message status icon
const getStatusIcon = () => {
  if (!isSent) return '';
  if (isRead) return '<i class="bi bi-check2-all text-primary" title="Lu"></i>';
  return '<i class="bi bi-check2" title="Envoyé"></i>';
};

// Get message type icon
const getTypeIcon = () => {
  if (messageType === 'annonce') return '<i class="bi bi-megaphone me-1" title="Annonce"></i>';
  return '';
};
%>

<div class="message-bubble <%= isSent ? 'sent' : 'received' %> <%= isGrouped ? 'grouped' : '' %> fade-in" 
     data-message-id="<%= message.id %>"
     data-sender="<%= message.sender_matricule %>">
  
  <!-- Message Content -->
  <div class="message-content">
    <!-- Sender name (only for received messages and not grouped) -->
    <% if (!isSent && !isGrouped) { %>
      <div class="message-sender mb-1">
        <small class="text-muted fw-bold">
          <%- getTypeIcon() %><%= getSenderName() %>
          <% if (messageType === 'annonce' && message.nom_classe) { %>
            <span class="badge bg-warning text-dark ms-1"><%= message.nom_classe %></span>
          <% } %>
        </small>
      </div>
    <% } %>
    
    <!-- Message Text -->
    <div class="message-text">
      <%= message.contenu %>
    </div>
    
    <!-- Message Attachments (if any) -->
    <% if (message.attachments && message.attachments.length > 0) { %>
      <div class="message-attachments mt-2">
        <% message.attachments.forEach(attachment => { %>
          <div class="attachment-item">
            <i class="bi bi-paperclip me-1"></i>
            <a href="/api/message/attachment/<%= attachment.id %>" target="_blank" class="text-decoration-none">
              <%= attachment.filename %>
            </a>
            <small class="text-muted">(<%= attachment.size %>)</small>
          </div>
        <% }) %>
      </div>
    <% } %>
  </div>
  
  <!-- Message Meta Information -->
  <div class="message-meta">
    <span class="message-time">
      <%= formatMessageTime(messageTime) %>
    </span>
    
    <!-- Message Status (for sent messages) -->
    <% if (isSent) { %>
      <span class="message-status">
        <%- getStatusIcon() %>
      </span>
    <% } %>
    
    <!-- Message Actions (on hover) -->
    <div class="message-actions">
      <button class="btn btn-sm btn-outline-secondary" onclick="replyToMessage('<%= message.id %>')" title="Répondre">
        <i class="bi bi-reply"></i>
      </button>
      <% if (isSent) { %>
        <button class="btn btn-sm btn-outline-danger" onclick="deleteMessage('<%= message.id %>')" title="Supprimer">
          <i class="bi bi-trash"></i>
        </button>
      <% } %>
    </div>
  </div>
</div>

<style>
/* Message bubble specific styles */
.message-bubble {
  margin-bottom: 1rem;
  max-width: 70%;
  position: relative;
}

.message-bubble.grouped {
  margin-bottom: 0.25rem;
}

.message-bubble.sent {
  align-self: flex-end;
  margin-left: auto;
}

.message-bubble.received {
  align-self: flex-start;
  margin-right: auto;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-bubble.sent .message-content {
  background: #0d6efd;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-bubble.received .message-content {
  background: white;
  color: #212529;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 4px;
}

.message-bubble.grouped.sent .message-content {
  border-bottom-right-radius: 18px;
}

.message-bubble.grouped.received .message-content {
  border-bottom-left-radius: 18px;
}

.message-sender {
  font-size: 0.8rem;
  margin-bottom: 4px;
}

.message-text {
  line-height: 1.4;
  margin: 0;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 0.75rem;
  color: #6c757d;
  opacity: 0.8;
}

.message-bubble.sent .message-meta {
  justify-content: flex-end;
  gap: 8px;
}

.message-actions {
  display: none;
  gap: 4px;
  position: absolute;
  top: -30px;
  right: 0;
  background: white;
  border-radius: 20px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.message-bubble:hover .message-actions {
  display: flex;
}

.message-actions .btn {
  width: 28px;
  height: 28px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-top: 8px;
}

.message-bubble.received .attachment-item {
  background: #f8f9fa;
}

/* Animation for new messages */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-bubble.new {
  animation: messageSlideIn 0.3s ease-out;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .message-content {
    padding: 10px 14px;
  }
  
  .message-actions {
    position: static;
    display: flex;
    justify-content: center;
    margin-top: 8px;
    background: transparent;
    box-shadow: none;
  }
}
</style>

<script>
// Message bubble functionality
function replyToMessage(messageId) {
  const composeInput = document.querySelector('.compose-input');
  if (composeInput) {
    const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
    const messageText = messageElement.querySelector('.message-text').textContent;
    const sender = messageElement.dataset.sender;
    
    // Add reply context to compose input
    composeInput.value = `@${sender} `;
    composeInput.focus();
    
    // Store reply context
    composeInput.dataset.replyTo = messageId;
  }
}

function deleteMessage(messageId) {
  if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
    fetch(`/api/message/${messageId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageElement) {
          messageElement.style.animation = 'fadeOut 0.3s ease-out';
          setTimeout(() => messageElement.remove(), 300);
        }
      } else {
        alert('Erreur lors de la suppression du message');
      }
    })
    .catch(error => {
      console.error('Error deleting message:', error);
      alert('Erreur lors de la suppression du message');
    });
  }
}

// Mark message as read when it comes into view
const observeMessage = (messageElement) => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const messageId = entry.target.dataset.messageId;
        markMessageAsRead(messageId);
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.5 });
  
  observer.observe(messageElement);
};

function markMessageAsRead(messageId) {
  fetch(`/api/message/${messageId}/read`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  }).catch(error => {
    console.error('Error marking message as read:', error);
  });
}

// Initialize message observation for unread messages
document.addEventListener('DOMContentLoaded', () => {
  document.querySelectorAll('.message-bubble[data-message-id]').forEach(observeMessage);
});
</script>
