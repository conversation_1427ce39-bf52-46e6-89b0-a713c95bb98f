<%
// Compose modal partial for message system
// Parameters: userRole, availableRecipients
const userRole = locals.userRole || 'admin';
const availableRecipients = locals.availableRecipients || [];
const isAdmin = userRole === 'admin';
const isProfesseur = userRole === 'professeur';
const isEleve = userRole === 'eleve';
%>

<!-- Compose Message Modal -->
<div class="modal fade compose-modal" id="composeModal" tabindex="-1" aria-labelledby="composeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="composeModalLabel">
          <i class="bi bi-pencil-square me-2"></i>Nouveau message
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      
      <div class="modal-body">
        <form id="composeForm">
          <!-- Message Type Selection -->
          <div class="mb-3">
            <label for="messageType" class="form-label">Type de message</label>
            <select id="messageType" name="type" class="form-select" required>
              <option value="">-- Choisir le type --</option>
              <% if (isAdmin) { %>
                <option value="privé">Message privé (à un professeur)</option>
                <option value="annonce">Annonce (à une classe)</option>
              <% } else if (isProfesseur) { %>
                <option value="privé">Message privé (à l'administration)</option>
                <option value="annonce">Annonce (à une classe)</option>
              <% } else { %>
                <option value="privé">Message privé (à l'administration)</option>
              <% } %>
            </select>
          </div>

          <!-- Private Message Recipients -->
          <div class="mb-3" id="privateRecipients" style="display: none;">
            <label for="recipientSelect" class="form-label">Destinataire</label>
            <select id="recipientSelect" name="receiver_matricule" class="form-select">
              <option value="">-- Choisir un destinataire --</option>
              <% availableRecipients.forEach(recipient => { %>
                <option value="<%= recipient.matricule %>" data-role="<%= recipient.role %>">
                  <%= recipient.nom && recipient.prenom ? `${recipient.nom} ${recipient.prenom}` : recipient.matricule %>
                  <% if (recipient.role) { %>(<%= recipient.role %>)<% } %>
                </option>
              <% }) %>
            </select>
            
            <!-- Selected Recipients Display -->
            <div class="recipient-tags mt-2" id="selectedRecipients"></div>
          </div>

          <!-- Class Announcement Target -->
          <div class="mb-3" id="classTarget" style="display: none;">
            <label for="targetClass" class="form-label">Classe cible</label>
            <% if ((isAdmin || isProfesseur) && typeof classes !== 'undefined' && classes.length > 0) { %>
              <select id="targetClass" name="nom_classe" class="form-select">
                <option value="">-- Choisir une classe --</option>
                <% classes.forEach(classe => { %>
                  <option value="<%= classe.nom_classe %>"><%= classe.nom_classe %></option>
                <% }) %>
              </select>
            <% } else { %>
              <select id="targetClass" name="nom_classe" class="form-select">
                <option value="">Aucune classe disponible</option>
              </select>
            <% } %>
          </div>

          <!-- Message Subject (for important messages) -->
          <div class="mb-3" id="messageSubject" style="display: none;">
            <label for="subject" class="form-label">Sujet (optionnel)</label>
            <input type="text" id="subject" name="subject" class="form-control" placeholder="Sujet du message">
          </div>

          <!-- Message Content -->
          <div class="mb-3">
            <label for="messageContent" class="form-label">Message</label>
            <textarea id="messageContent" name="contenu" class="form-control" rows="5" 
                      placeholder="Tapez votre message ici..." required></textarea>
            <div class="form-text">
              <span id="charCount">0</span>/1000 caractères
            </div>
          </div>

          <!-- Message Priority (for admin) -->
          <% if (isAdmin) { %>
            <div class="mb-3">
              <label for="messagePriority" class="form-label">Priorité</label>
              <select id="messagePriority" name="priority" class="form-select">
                <option value="normal">Normale</option>
                <option value="high">Élevée</option>
                <option value="urgent">Urgente</option>
              </select>
            </div>
          <% } %>

          <!-- File Attachments -->
          <div class="mb-3">
            <label for="messageAttachments" class="form-label">Pièces jointes (optionnel)</label>
            <input type="file" id="messageAttachments" name="attachments" class="form-control" multiple 
                   accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png">
            <div class="form-text">
              Formats acceptés: PDF, DOC, DOCX, TXT, JPG, PNG. Taille max: 5MB par fichier.
            </div>
            <div id="attachmentPreview" class="mt-2"></div>
          </div>

          <!-- Schedule Send (for admin) -->
          <% if (isAdmin) { %>
            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="scheduleMessage">
                <label class="form-check-label" for="scheduleMessage">
                  Programmer l'envoi
                </label>
              </div>
              <div id="scheduleOptions" class="mt-2" style="display: none;">
                <input type="datetime-local" id="scheduleTime" name="schedule_time" class="form-control">
              </div>
            </div>
          <% } %>
        </form>
      </div>
      
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="bi bi-x-circle me-1"></i>Annuler
        </button>
        <button type="submit" class="btn btn-primary" id="sendMessageBtn" form="composeForm">
          <i class="bi bi-send me-1"></i>Envoyer
        </button>
      </div>
    </div>
  </div>
</div>

<style>
/* Compose Modal Styles */
.compose-modal .modal-content {
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.compose-modal .modal-header {
  background: linear-gradient(135deg, #0d6efd, #0b5ed7);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 1.5rem;
}

.compose-modal .modal-body {
  padding: 2rem;
}

.compose-modal .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.compose-modal .form-control,
.compose-modal .form-select {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 0.75rem 1rem;
  transition: border-color 0.2s ease;
}

.compose-modal .form-control:focus,
.compose-modal .form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.recipient-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
  padding: 8px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background: #f8f9fa;
}

.recipient-tag {
  background: #0d6efd;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  animation: slideIn 0.3s ease;
}

.recipient-tag .remove {
  cursor: pointer;
  font-weight: bold;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.recipient-tag .remove:hover {
  background: rgba(255, 255, 255, 0.5);
}

.attachment-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 0.9rem;
}

.attachment-item .remove {
  cursor: pointer;
  color: #dc3545;
  font-weight: bold;
}

.char-counter {
  text-align: right;
  font-size: 0.8rem;
  color: #6c757d;
}

.char-counter.warning {
  color: #ffc107;
}

.char-counter.danger {
  color: #dc3545;
}

#sendMessageBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-btn {
  position: relative;
}

.loading-btn .spinner-border {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
}
</style>

<script>
// Compose Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
  const composeModal = document.getElementById('composeModal');
  const form = document.getElementById('composeForm');
  const messageTypeSelect = document.getElementById('messageType');
  const privateRecipientsDiv = document.getElementById('privateRecipients');
  const classTargetDiv = document.getElementById('classTarget');
  const messageSubjectDiv = document.getElementById('messageSubject');
  const recipientSelect = document.getElementById('recipientSelect');
  const messageContent = document.getElementById('messageContent');
  const charCount = document.getElementById('charCount');
  const sendBtn = document.getElementById('sendMessageBtn');
  const scheduleCheckbox = document.getElementById('scheduleMessage');
  const scheduleOptions = document.getElementById('scheduleOptions');
  const attachmentInput = document.getElementById('messageAttachments');
  const attachmentPreview = document.getElementById('attachmentPreview');

  let selectedRecipients = [];
  let attachedFiles = [];

  // Message type change handler
  messageTypeSelect.addEventListener('change', function() {
    const type = this.value;
    
    privateRecipientsDiv.style.display = type === 'privé' ? 'block' : 'none';
    classTargetDiv.style.display = type === 'annonce' ? 'block' : 'none';
    messageSubjectDiv.style.display = type === 'annonce' ? 'block' : 'none';
    
    // Classes are now server-rendered for both admin and professor
  });

  // Character counter
  messageContent.addEventListener('input', function() {
    const count = this.value.length;
    charCount.textContent = count;
    
    const counter = charCount.parentElement;
    counter.className = 'form-text';
    
    if (count > 800) {
      counter.classList.add('text-warning');
    }
    if (count > 950) {
      counter.classList.add('text-danger');
    }
    
    sendBtn.disabled = count === 0 || count > 1000;
  });

  // Recipient selection
  recipientSelect.addEventListener('change', function() {
    if (this.value) {
      addRecipient(this.value, this.options[this.selectedIndex].text);
      this.value = '';
    }
  });

  // Schedule message toggle
  if (scheduleCheckbox) {
    scheduleCheckbox.addEventListener('change', function() {
      scheduleOptions.style.display = this.checked ? 'block' : 'none';
    });
  }

  // File attachment handler
  attachmentInput.addEventListener('change', function() {
    handleFileAttachments(this.files);
  });

  // Prevent duplicate sends
  let sendingInProgress = false;



  // Form submit handler (handles Enter key or browser submit)
  form.addEventListener('submit', function(event) {
    event.preventDefault();
    if (!sendingInProgress) {
      sendMessage();
    }
  });

  // Functions
  function addRecipient(matricule, displayName) {
    if (selectedRecipients.find(r => r.matricule === matricule)) return;
    
    selectedRecipients.push({ matricule, displayName });
    updateRecipientTags();
  }

  function removeRecipient(matricule) {
    selectedRecipients = selectedRecipients.filter(r => r.matricule !== matricule);
    updateRecipientTags();
  }

  function updateRecipientTags() {
    const container = document.getElementById('selectedRecipients');
    container.innerHTML = '';
    
    selectedRecipients.forEach(recipient => {
      const tag = document.createElement('div');
      tag.className = 'recipient-tag';
      tag.innerHTML = `
        <span>${recipient.displayName}</span>
        <span class="remove" onclick="removeRecipient('${recipient.matricule}')">&times;</span>
      `;
      container.appendChild(tag);
    });
  }

  // Classes are now server-rendered for both admin and professor roles

  function handleFileAttachments(files) {
    attachedFiles = Array.from(files);
    updateAttachmentPreview();
  }

  function updateAttachmentPreview() {
    attachmentPreview.innerHTML = '';
    
    attachedFiles.forEach((file, index) => {
      const item = document.createElement('div');
      item.className = 'attachment-item';
      item.innerHTML = `
        <i class="bi bi-paperclip"></i>
        <span>${file.name}</span>
        <small class="text-muted">(${formatFileSize(file.size)})</small>
        <span class="remove" onclick="removeAttachment(${index})">&times;</span>
      `;
      attachmentPreview.appendChild(item);
    });
  }

  function removeAttachment(index) {
    attachedFiles.splice(index, 1);
    updateAttachmentPreview();
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  function sendMessage() {
    if (sendingInProgress) return;
    sendingInProgress = true;
    const form = document.getElementById('composeForm');
    const formData = new FormData(form);
    
    // Add selected recipients for private messages
    if (messageTypeSelect.value === 'privé') {
      selectedRecipients.forEach(recipient => {
        formData.append('recipients[]', recipient.matricule);
      });
    }
    
    // Add attachments
    attachedFiles.forEach(file => {
      formData.append('attachments', file);
    });
    
    // Show loading state
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Envoi...';
    
    fetch('/api/message/send', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Close modal and refresh conversations
        bootstrap.Modal.getInstance(composeModal).hide();
        showSuccessMessage('Message envoyé avec succès');
        refreshConversations();
        resetForm();
      } else {
        showErrorMessage(data.message || 'Erreur lors de l\'envoi du message');
      }
    })
    .catch(error => {
      console.error('Error sending message:', error);
      showErrorMessage('Erreur lors de l\'envoi du message');
    })
    .finally(() => {
      sendBtn.disabled = false;
      sendBtn.innerHTML = '<i class="bi bi-send me-1"></i>Envoyer';
      sendingInProgress = false;
    });
  }

  function resetForm() {
    document.getElementById('composeForm').reset();
    selectedRecipients = [];
    attachedFiles = [];
    updateRecipientTags();
    updateAttachmentPreview();
    charCount.textContent = '0';
    privateRecipientsDiv.style.display = 'none';
    classTargetDiv.style.display = 'none';
    messageSubjectDiv.style.display = 'none';
  }

  function showSuccessMessage(message) {
    // Implementation depends on your notification system
    console.log('Success:', message);
  }

  function showErrorMessage(message) {
    // Implementation depends on your notification system
    console.error('Error:', message);
  }

  function refreshConversations() {
    // Refresh the conversation list
    if (typeof loadConversations === 'function') {
      loadConversations();
    }
  }

  // Make functions globally available
  window.removeRecipient = removeRecipient;
  window.removeAttachment = removeAttachment;
});
</script>
