<!-- Admin Sidebar Partial -->
<div class="sidebar">
  <ul class="nav flex-column">
    <li class="nav-item">
      <a href="/dashboard-admin" class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>">
        <i class="bi bi-speedometer2"></i>
        <span>Tableau de bord</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/eleve" class="nav-link <%= currentPage === 'eleve' ? 'active' : '' %>">
        <i class="bi bi-people-fill"></i>
        <span>Élèves</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/prof" class="nav-link <%= currentPage === 'prof' ? 'active' : '' %>">
        <i class="bi bi-person-badge-fill"></i>
        <span>Professeurs</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/classe" class="nav-link <%= currentPage === 'classe' ? 'active' : '' %>">
        <i class="bi bi-building"></i>
        <span>Classes</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/emploi_temps" class="nav-link <%= currentPage === 'emploi_temps' ? 'active' : '' %>">
        <i class="bi bi-journal-text"></i>
        <span>Emploi du temps</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/emploi_temps/salles" class="nav-link <%= currentPage === 'salles' ? 'active' : '' %>">
        <i class="bi bi-door-open"></i>
        <span>Gestion des Salles</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/bulletin" class="nav-link <%= currentPage === 'bulletin' ? 'active' : '' %>">
        <i class="bi bi-file-earmark-text"></i>
        <span>Bulletins</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/cour" class="nav-link <%= currentPage === 'cour' ? 'active' : '' %>">
        <i class="bi bi-book"></i>
        <span>Matières</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/utilisateur" class="nav-link <%= currentPage === 'utilisateur' ? 'active' : '' %>">
        <i class="bi bi-people"></i>
        <span>Utilisateurs</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/message" class="nav-link <%= currentPage === 'message' ? 'active' : '' %>">
        <i class="bi bi-chat-dots"></i>
        <span>Messagerie</span>
      </a>
    </li>
    <li class="nav-item">
      <a href="/admin/parametre" class="nav-link <%= currentPage === 'parametre' ? 'active' : '' %>">
        <i class="bi bi-gear-fill"></i>
        <span>Paramètres</span>
      </a>
    </li>
    <li class="nav-item mt-auto">
      <a href="/log_out" class="nav-link text-danger">
        <i class="bi bi-box-arrow-right"></i>
        <span>Déconnexion</span>
      </a>
    </li>
  </ul>
</div>

<style>
/* Sidebar Styles */
.sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  width: 250px;
  height: calc(100vh - 60px);
  background: white;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  z-index: 999;
  box-shadow: 1px 0 3px 0 rgb(0 0 0 / 0.1);
}

.sidebar .nav {
  padding: 1rem 0;
  height: 100%;
}

.sidebar .nav-item {
  margin: 0.25rem 0;
}

.sidebar .nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #6b7280;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border-radius: 0;
}

.sidebar .nav-link:hover {
  background-color: #f3f4f6;
  color: #374151;
  transform: translateX(2px);
}

.sidebar .nav-link.active {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  font-weight: 600;
}

.sidebar .nav-link.active:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateX(0);
}

.sidebar .nav-link i {
  width: 20px;
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.sidebar .nav-link span {
  font-size: 0.9rem;
}

.sidebar .nav-link.text-danger:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* Main content adjustment */
.dashboard-main {
  margin-left: 250px;
  margin-top: 60px;
  padding: 2rem;
  min-height: calc(100vh - 60px);
  background-color: #f9fafb;
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .dashboard-main {
    margin-left: 0;
  }
}
</style>
