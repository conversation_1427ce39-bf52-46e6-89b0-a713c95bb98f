<style>
  body {
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
  }

  .topbar {
    width: 100%;
    height: 60px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px;
    margin-bottom: 50px;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  .admin-circle {
    background-color: #0d6efd;
    color: white;
    font-weight: bold;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar {
    width: 250px;
    background-color: #fff;
    border-right: 1px solid #dee2e6;
    padding-top: 20px;
    position: fixed;
    top: 60px;
    left: 0;
    bottom: 0;
    margin-top: 30px;
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
  }

  .sidebar a {
    color: #333;
    text-decoration: none;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    transition: background 0.2s ease-in-out;
  }

  .sidebar a i {
    margin-right: 10px;
  }

  .sidebar a:hover,
  .sidebar a.active {
    background-color: #1e77cf;
    color: white;
  }

  .main-content {
    margin-left: 250px;
    margin-top: 60px;
    padding: 20px;
  }

  @media (max-width: 768px) {
    .sidebar {
      transform: translateX(-100%);
    }
    .main-content {
      margin-left: 0;
    }
  }
</style>
