<!-- Standardized Student Topbar Partial -->
<%
  // Get user data from locals or fallback to session/defaults
  const user = locals.currentUser || {
    matricule: typeof matricule !== 'undefined' ? matricule : 'N/A',
    nom: typeof nom !== 'undefined' ? nom : '',
    prenom: typeof prenom !== 'undefined' ? prenom : '',
    role: typeof role !== 'undefined' ? role : 'eleve',
    fullName: '',
    initials: 'EL',
    notificationsCount: typeof notificationsCount !== 'undefined' ? notificationsCount : 0
  };

  // Ensure fullName and initials are set
  if (!user.fullName) {
    user.fullName = `${user.prenom} ${user.nom}`.trim() || 'Élève';
  }

  if (!user.initials || user.initials === '') {
    user.initials = `${(user.prenom || '').charAt(0)}${(user.nom || '').charAt(0)}`.toUpperCase() || 'EL';
  }
%>

<div class="topbar">
  <div class="d-flex align-items-center">
    <i class="bi bi-mortarboard-fill fs-3 me-2 text-primary"></i>
    <span class="fs-5 fw-bold">NS School Manager</span>
    <span class="badge bg-success ms-2">Élève</span>
  </div>
  <div class="d-flex align-items-center gap-4">
    <!-- Notifications -->
    <div class="position-relative">
      <i class="bi bi-bell fs-4 text-muted"></i>
      <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
        <%= user.notificationsCount || 0 %>
      </span>
    </div>

    <!-- User Avatar -->
    <div class="student-badge">
      <%= user.initials %>
    </div>

    <!-- User Dropdown -->
    <div class="dropdown">
      <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle"
         id="profileDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-person-circle fs-4"></i>
      </a>
      <ul class="dropdown-menu dropdown-menu-end shadow-lg" aria-labelledby="profileDropdown">
        <li class="dropdown-header">Profil Élève</li>
        <li><hr class="dropdown-divider"></li>
        <li class="dropdown-item"><strong>Nom:</strong> <%= user.nom || 'N/A' %></li>
        <li class="dropdown-item"><strong>Prénom:</strong> <%= user.prenom || 'N/A' %></li>
        <li class="dropdown-item"><strong>Matricule:</strong> <%= user.matricule %></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="/eleve/profil">
          <i class="bi bi-person me-2"></i>Mon Profil
        </a></li>
        <li><a class="dropdown-item" href="/log_out">
          <i class="bi bi-box-arrow-right me-2"></i>Déconnexion
        </a></li>
      </ul>
    </div>
  </div>
</div>

<style>
/* Topbar Styles */
.topbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem;
  z-index: 1000;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

.student-badge {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  font-weight: 600;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  border-radius: 0.5rem;
  min-width: 200px;
}

.dropdown-header {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background-color: #f3f4f6;
}
</style>
