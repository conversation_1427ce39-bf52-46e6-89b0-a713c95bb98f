<style>
/* Professional Message System Styles */
:root {
  --message-primary: #0d6efd;
  --message-secondary: #6c757d;
  --message-success: #198754;
  --message-warning: #ffc107;
  --message-danger: #dc3545;
  --message-light: #f8f9fa;
  --message-dark: #212529;
  --message-border: #dee2e6;
  --message-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --message-radius: 12px;
  --message-spacing: 1rem;
}

/* Message Container Layout */
.message-container {
  display: flex;
  height: calc(100vh - 120px);
  background: var(--message-light);
  border-radius: var(--message-radius);
  overflow: hidden;
  box-shadow: var(--message-shadow);
}

/* Conversation Sidebar */
.conversation-sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid var(--message-border);
  display: flex;
  flex-direction: column;
}

.conversation-header {
  padding: var(--message-spacing);
  border-bottom: 1px solid var(--message-border);
  background: var(--message-primary);
  color: white;
}

.conversation-header h5 {
  margin: 0;
  font-weight: 600;
}

.conversation-search {
  padding: var(--message-spacing);
  border-bottom: 1px solid var(--message-border);
}

.conversation-search input {
  border-radius: 20px;
  border: 1px solid var(--message-border);
  padding: 8px 16px;
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Conversation Item */
.conversation-item {
  padding: var(--message-spacing);
  border-bottom: 1px solid var(--message-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.conversation-item:hover {
  background-color: var(--message-light);
}

.conversation-item.active {
  background-color: rgba(13, 110, 253, 0.1);
  border-left: 4px solid var(--message-primary);
}

.conversation-item.unread {
  background-color: rgba(25, 135, 84, 0.05);
}

.conversation-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: var(--message-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  margin-right: 12px;
  flex-shrink: 0;
}

.conversation-content {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  font-weight: 600;
  color: var(--message-dark);
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.conversation-preview {
  color: var(--message-secondary);
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.8rem;
  color: var(--message-secondary);
}

.unread-badge {
  background: var(--message-success);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  position: absolute;
  top: 10px;
  right: 10px;
}

/* Message Thread Panel */
.message-thread {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.thread-header {
  padding: var(--message-spacing);
  border-bottom: 1px solid var(--message-border);
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.thread-title {
  display: flex;
  align-items: center;
}

.thread-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--message-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.thread-info h6 {
  margin: 0;
  font-weight: 600;
}

.thread-info small {
  color: var(--message-secondary);
}

.thread-actions {
  display: flex;
  gap: 8px;
}

/* Message List */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--message-spacing);
  background: #fafbfc;
}

/* Message Bubble */
.message-bubble {
  max-width: 70%;
  margin-bottom: var(--message-spacing);
  display: flex;
  flex-direction: column;
}

.message-bubble.sent {
  align-self: flex-end;
  align-items: flex-end;
}

.message-bubble.received {
  align-self: flex-start;
  align-items: flex-start;
}

.message-content {
  background: white;
  padding: 12px 16px;
  border-radius: var(--message-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-bubble.sent .message-content {
  background: var(--message-primary);
  color: white;
}

.message-bubble.received .message-content {
  background: white;
  color: var(--message-dark);
}

.message-text {
  margin: 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.message-sender {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--message-secondary);
  margin-bottom: 0.25rem;
}

.message-subject {
  font-weight: 600;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.message-bubble.received .message-subject {
  border-bottom-color: var(--message-border);
}

.message-type-badge {
  display: inline-block;
  background: var(--message-warning);
  color: var(--message-dark);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  margin-top: 0.5rem;
}

.message-date-separator {
  text-align: center;
  margin: 1rem 0;
  position: relative;
}

.message-date-separator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--message-border);
  z-index: 1;
}

.message-date-separator span {
  background: var(--message-light);
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  color: var(--message-secondary);
  border-radius: 15px;
  position: relative;
  z-index: 2;
}

.message-bubble.grouped {
  margin-top: 0.25rem;
}

.message-bubble.grouped .message-sender {
  display: none;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  font-size: 0.8rem;
  color: var(--message-secondary);
}

.message-time {
  color: inherit;
}

.message-status {
  display: flex;
  align-items: center;
  gap: 2px;
}

.status-icon {
  width: 12px;
  height: 12px;
}

/* Compose Area */
.compose-area {
  padding: var(--message-spacing);
  border-top: 1px solid var(--message-border);
  background: white;
}

.compose-form {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.compose-input {
  flex: 1;
  border: 1px solid var(--message-border);
  border-radius: 20px;
  padding: 10px 16px;
  resize: none;
  max-height: 100px;
  min-height: 40px;
}

.compose-input:focus {
  outline: none;
  border-color: var(--message-primary);
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
}

.compose-actions {
  display: flex;
  gap: 8px;
}

.btn-send {
  background: var(--message-primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-send:hover {
  background: #0b5ed7;
}

.btn-send:disabled {
  background: var(--message-secondary);
  cursor: not-allowed;
}

/* Compose Modal */
.compose-modal .modal-dialog {
  max-width: 600px;
}

.compose-modal .modal-header {
  background: var(--message-primary);
  color: white;
  border-bottom: none;
}

.compose-modal .modal-header .btn-close {
  filter: invert(1);
}

.recipient-selector {
  margin-bottom: var(--message-spacing);
}

.recipient-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.recipient-tag {
  background: var(--message-primary);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.recipient-tag .remove {
  cursor: pointer;
  font-weight: bold;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--message-danger);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  border: 2px solid white;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--message-secondary);
  text-align: center;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: var(--message-spacing);
  opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-container {
    flex-direction: column;
    height: calc(100vh - 80px);
  }
  
  .conversation-sidebar {
    width: 100%;
    height: 200px;
  }
  
  .conversation-sidebar.collapsed {
    height: 60px;
    overflow: hidden;
  }
  
  .message-bubble {
    max-width: 85%;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--message-border);
  border-radius: 50%;
  border-top-color: var(--message-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
