<%
// Conversation item partial for message system
// Parameters: conversation, currentUser
const conversation = locals.conversation || {};
const currentUser = locals.currentUser || {};
const isUnread = conversation.unread_count > 0;
const lastMessage = conversation.last_message || '';
const participantName = conversation.participant_name || conversation.sender_matricule || 'Inconnu';
const participantRole = conversation.participant_role || 'utilisateur';
const lastMessageTime = conversation.last_message_time ? new Date(conversation.last_message_time) : new Date();
const conversationId = conversation.id || conversation.conversation_id || '';

// Generate avatar initials
const getInitials = (name) => {
  if (!name) return '?';
  return name.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('');
};

// Format time display
const formatTime = (date) => {
  const now = new Date();
  const diff = now - date;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);
  
  if (minutes < 1) return 'À l\'instant';
  if (minutes < 60) return `${minutes}min`;
  if (hours < 24) return `${hours}h`;
  if (days < 7) return `${days}j`;
  return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' });
};

// Get role color
const getRoleColor = (role) => {
  switch(role) {
    case 'admin': return '#dc3545';
    case 'professeur': return '#198754';
    case 'eleve': return '#0d6efd';
    default: return '#6c757d';
  }
};

// Truncate message preview
const truncateMessage = (message, maxLength = 50) => {
  if (!message) return 'Aucun message';
  return message.length > maxLength ? message.substring(0, maxLength) + '...' : message;
};
%>

<div class="conversation-item <%= isUnread ? 'unread' : '' %>" 
     data-conversation-id="<%= conversationId %>"
     data-participant="<%= participantName %>"
     onclick="selectConversation('<%= conversationId %>', '<%= participantName %>', '<%= participantRole %>')">
  
  <div class="d-flex align-items-center">
    <!-- Avatar -->
    <div class="conversation-avatar" style="background-color: <%= getRoleColor(participantRole) %>">
      <%= getInitials(participantName) %>
    </div>
    
    <!-- Conversation Content -->
    <div class="conversation-content">
      <div class="conversation-title">
        <span class="participant-name"><%= participantName %></span>
        <span class="conversation-time"><%= formatTime(lastMessageTime) %></span>
      </div>
      
      <div class="d-flex align-items-center justify-content-between">
        <div class="conversation-preview">
          <% if (conversation.type === 'annonce') { %>
            <i class="bi bi-megaphone me-1"></i>
          <% } %>
          <%= truncateMessage(lastMessage) %>
        </div>
        
        <!-- Role Badge -->
        <span class="badge rounded-pill" style="background-color: <%= getRoleColor(participantRole) %>; font-size: 0.7rem;">
          <%= participantRole %>
        </span>
      </div>
    </div>
  </div>
  
  <!-- Unread Badge -->
  <% if (isUnread) { %>
    <div class="unread-badge">
      <%= conversation.unread_count > 99 ? '99+' : conversation.unread_count %>
    </div>
  <% } %>
  
  <!-- Message Type Indicator -->
  <% if (conversation.type === 'annonce') { %>
    <div class="position-absolute" style="top: 8px; left: 8px;">
      <i class="bi bi-megaphone text-warning" title="Annonce"></i>
    </div>
  <% } %>
</div>

<script>
// Add conversation item functionality
function selectConversation(conversationId, participantName, participantRole) {
  // Remove active class from all items
  document.querySelectorAll('.conversation-item').forEach(item => {
    item.classList.remove('active');
  });
  
  // Add active class to selected item
  event.currentTarget.classList.add('active');
  
  // Mark as read
  if (event.currentTarget.classList.contains('unread')) {
    markConversationAsRead(conversationId);
    event.currentTarget.classList.remove('unread');
    const badge = event.currentTarget.querySelector('.unread-badge');
    if (badge) badge.remove();
  }
  
  // Load conversation messages
  loadConversationMessages(conversationId, participantName, participantRole);
  
  // Update thread header
  updateThreadHeader(participantName, participantRole);
  
  // Show message thread on mobile
  if (window.innerWidth <= 768) {
    document.querySelector('.conversation-sidebar').classList.add('collapsed');
    document.querySelector('.message-thread').classList.add('active');
  }
}

function markConversationAsRead(conversationId) {
  fetch(`/api/message/conversation/${conversationId}/read`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  }).catch(error => {
    console.error('Error marking conversation as read:', error);
  });
}

function loadConversationMessages(conversationId, participantName, participantRole) {
  const messageList = document.querySelector('.message-list');
  if (!messageList) return;
  
  // Show loading state
  messageList.innerHTML = '<div class="text-center p-4"><div class="loading-spinner"></div></div>';
  
  fetch(`/api/message/conversation/${conversationId}/messages`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        displayMessages(data.messages, participantName);
      } else {
        messageList.innerHTML = '<div class="text-center p-4 text-muted">Erreur lors du chargement des messages</div>';
      }
    })
    .catch(error => {
      console.error('Error loading messages:', error);
      messageList.innerHTML = '<div class="text-center p-4 text-muted">Erreur lors du chargement des messages</div>';
    });
}

function updateThreadHeader(participantName, participantRole) {
  const threadTitle = document.querySelector('.thread-title');
  if (!threadTitle) return;
  
  const avatar = threadTitle.querySelector('.thread-avatar');
  const info = threadTitle.querySelector('.thread-info');
  
  if (avatar) {
    avatar.textContent = participantName.split(' ').map(word => word.charAt(0).toUpperCase()).slice(0, 2).join('');
    avatar.style.backgroundColor = getRoleColor(participantRole);
  }
  
  if (info) {
    info.querySelector('h6').textContent = participantName;
    info.querySelector('small').textContent = participantRole;
  }
}

function getRoleColor(role) {
  switch(role) {
    case 'admin': return '#dc3545';
    case 'professeur': return '#198754';
    case 'eleve': return '#0d6efd';
    default: return '#6c757d';
  }
}
</script>
