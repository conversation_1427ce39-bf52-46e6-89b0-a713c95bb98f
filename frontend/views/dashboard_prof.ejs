<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>NS School Manager - Tableau de bord Professeur</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

  <%- include('partials/dashboard-styles') %>

  <style>
    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }

    /* Professor-specific styles */
    .professor-badge {
      background: linear-gradient(135deg, #3b82f6, #1d4ed8);
      color: white;
      font-weight: 600;
      border-radius: 50%;
      width: 45px;
      height: 45px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .professor-welcome {
      background: linear-gradient(135deg, #1e40af, #3b82f6);
      color: white;
      border-radius: var(--border-radius);
      padding: 2rem;
      margin-bottom: 2rem;
      position: relative;
      overflow: hidden;
    }

    .professor-welcome::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -50%;
      width: 200%;
      height: 200%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="profgrain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="80" r="1" fill="white" opacity="0.1"/><circle cx="60" cy="30" r="1" fill="white" opacity="0.1"/><circle cx="30" cy="70" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23profgrain)"/></svg>');
      animation: professorFloat 25s ease-in-out infinite;
    }

    @keyframes professorFloat {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-15px) rotate(90deg); }
    }

    .professor-stats {
      position: relative;
      z-index: 1;
    }

    .teaching-hours {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      margin-left: auto;
    }

    .teaching-hours-number {
      font-size: 1.8rem;
      font-weight: 700;
      line-height: 1;
    }

    .teaching-hours-label {
      font-size: 0.7rem;
      opacity: 0.9;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Additional dashboard layout styles */
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 2rem;
      min-height: calc(100vh - 60px);
      background-color: #f9fafb;
    }
  </style>
</head>
<body>
  <!-- Professor Topbar -->
  <%- include('partials/prof-topbar') %>

  <!-- Professor Sidebar -->
  <% var currentPage = 'dashboard'; %>
  <%- include('partials/prof-sidebar') %>

  <!-- Professional Main Content -->
  <div class="dashboard-main">
    <!-- Professor Welcome Section -->
    <div class="professor-welcome">
      <div class="professor-stats">
        <div class="d-flex align-items-center justify-content-between">
          <div>
            <h1 class="mb-2">Bonjour, Professeur! 👨‍🏫</h1>
            <p class="mb-0 opacity-90">Gérez vos cours et suivez vos élèves efficacement</p>
          </div>
          <div class="teaching-hours">
            <div class="teaching-hours-number"><%= stats.cours || 0 %></div>
            <div class="teaching-hours-label">Cours</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Professional Statistics -->
    <div class="stats-grid">
      <%- include('partials/stat-card', {
        icon: 'bi-journal-bookmark',
        value: stats.cours || '0',
        label: 'Cours assignés',
        type: 'primary',
        change: null,
        changeType: null
      }) %>

      <%- include('partials/stat-card', {
        icon: 'bi-people',
        value: stats.eleves || '0',
        label: 'Élèves suivis',
        type: 'success',
        change: null,
        changeType: null
      }) %>

      <%- include('partials/stat-card', {
        icon: 'bi-folder-check',
        value: stats.devoirs || '0',
        label: 'Devoirs rendus',
        type: 'warning',
        change: null,
        changeType: null
      }) %>

      <%- include('partials/stat-card', {
        icon: 'bi-x-circle',
        value: (stats.absences || '0') + ' jours',
        label: 'Absences signalées',
        type: 'danger',
        change: null,
        changeType: null
      }) %>
    </div>

    <!-- Quick Actions for Professors -->
    <div class="quick-actions">
      <a href="/prof/note" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-journal-plus"></i>
        </div>
        <div class="quick-action-label">Gérer les notes</div>
      </a>

      <a href="/prof/devoir" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-clipboard-check"></i>
        </div>
        <div class="quick-action-label">Devoirs & corrections</div>
      </a>

      <a href="/prof/message" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-chat-dots"></i>
        </div>
        <div class="quick-action-label">Messagerie</div>
      </a>

      <a href="/professeur/cours" class="quick-action">
        <div class="quick-action-icon">
          <i class="bi bi-book"></i>
        </div>
        <div class="quick-action-label">Mes cours</div>
      </a>
    </div>

    <!-- Professional Activity Feed -->
    <%- include('partials/activity-feed', {
      activities: typeof activites !== 'undefined' ? activites : [],
      title: 'Activités récentes de mes élèves',
      emptyMessage: 'Aucune activité récente de vos élèves'
    }) %>

    <!-- Additional Professor Dashboard Sections -->
    <div class="row">
      <div class="col-lg-6">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="bi bi-clipboard-check"></i>
              Devoirs à corriger
            </h3>
          </div>
          <div class="card-body">
            <div class="text-center py-4">
              <i class="bi bi-clipboard-check display-4 text-muted mb-3"></i>
              <p class="text-muted">Aucun devoir en attente de correction</p>
              <a href="/prof/devoir" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-eye me-1"></i>
                Gérer les devoirs
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="bi bi-journal-plus"></i>
              Notes récentes
            </h3>
          </div>
          <div class="card-body">
            <div class="text-center py-4">
              <i class="bi bi-journal-plus display-4 text-muted mb-3"></i>
              <p class="text-muted">Aucune note ajoutée récemment</p>
              <a href="/prof/note" class="btn btn-outline-primary btn-sm">
                <i class="bi bi-plus-circle me-1"></i>
                Ajouter des notes
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Teaching Schedule Overview -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="bi bi-calendar-week"></i>
          Aperçu de la semaine
        </h3>
      </div>
      <div class="card-body">
        <div class="text-center py-4">
          <i class="bi bi-calendar-week display-4 text-muted mb-3"></i>
          <p class="text-muted">Consultez votre emploi du temps pour voir vos cours de la semaine</p>
          <a href="/prof/emploi-temps" class="btn btn-outline-primary btn-sm">
            <i class="bi bi-calendar me-1"></i>
            Voir l'emploi du temps
          </a>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Add smooth animations on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Animate stat cards with staggered delay
      const statCards = document.querySelectorAll('.stat-card');
      statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
      });

      // Animate quick actions
      const quickActions = document.querySelectorAll('.quick-action');
      quickActions.forEach((action, index) => {
        action.style.animationDelay = `${0.4 + index * 0.1}s`;
        action.classList.add('fade-in');
      });

      // Add hover effects to dashboard cards
      const dashboardCards = document.querySelectorAll('.dashboard-card');
      dashboardCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-2px)';
          this.style.boxShadow = 'var(--shadow-lg)';
        });

        card.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
          this.style.boxShadow = 'var(--shadow)';
        });
      });
    });
  </script>
</body>
</html>
