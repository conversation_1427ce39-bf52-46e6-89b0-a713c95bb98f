<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Gestion des Salles</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
    .room-card {
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .room-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    .room-type-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
    .capacity-indicator {
      background: linear-gradient(45deg, #007bff, #0056b3);
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 0.8rem;
    }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'salles' }) %>

  <div class="main-content">
    <h2 class="mb-4">Gestion des Salles</h2>
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#salleModal" onclick="ouvrirModalAjout()">
      <i class="bi bi-plus-circle me-2"></i>Ajouter une salle
    </button>

    <!-- Filters -->
    <div class="row mb-4">
      <div class="col-md-4">
        <select class="form-select" id="filtreType" onchange="filtrerSalles()">
          <option value="">Tous les types</option>
          <option value="normale">Salles Normales</option>
          <option value="laboratoire">Laboratoires</option>
          <option value="informatique">Salles Informatique</option>
          <option value="sport">Installations Sportives</option>
          <option value="bibliotheque">Bibliothèques</option>
          <option value="autre">Autres Salles</option>
        </select>
      </div>
      <div class="col-md-4">
        <select class="form-select" id="filtreBatiment" onchange="filtrerSalles()">
          <option value="">Tous les bâtiments</option>
        </select>
      </div>
      <div class="col-md-4">
        <div class="input-group">
          <span class="input-group-text"><i class="bi bi-search"></i></span>
          <input type="text" class="form-control" id="rechercheNom" placeholder="Rechercher par nom..." onkeyup="filtrerSalles()">
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4" id="statistiques">
      <!-- Statistics will be loaded here -->
    </div>

    <!-- Rooms Grid -->
    <div class="row" id="sallesContainer">
      <!-- Rooms will be loaded here -->
    </div>
  </div>

  <!-- Modal for Add/Edit Room -->
  <div class="modal fade" id="salleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalTitle">
            <i class="bi bi-building me-2"></i>Nouvelle Salle
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <form id="salleForm">
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <label for="nomSalle" class="form-label">Nom de la salle *</label>
                <input type="text" class="form-control" id="nomSalle" name="nom_salle" required>
              </div>
              <div class="col-md-6">
                <label for="typeSalle" class="form-label">Type de salle *</label>
                <select class="form-select" id="typeSalle" name="type_salle" required>
                  <option value="">Sélectionner un type</option>
                  <option value="normale">Salle Normale</option>
                  <option value="laboratoire">Laboratoire</option>
                  <option value="informatique">Salle Informatique</option>
                  <option value="sport">Installation Sportive</option>
                  <option value="bibliotheque">Bibliothèque</option>
                  <option value="autre">Autre</option>
                </select>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-md-4">
                <label for="capacite" class="form-label">Capacité *</label>
                <input type="number" class="form-control" id="capacite" name="capacite" min="1" max="200" required>
              </div>
              <div class="col-md-4">
                <label for="etage" class="form-label">Étage *</label>
                <select class="form-select" id="etage" name="etage" required>
                  <option value="">Sélectionner</option>
                  <option value="RDC">Rez-de-chaussée</option>
                  <option value="1er">1er étage</option>
                  <option value="2ème">2ème étage</option>
                  <option value="3ème">3ème étage</option>
                </select>
              </div>
              <div class="col-md-4">
                <label for="batiment" class="form-label">Bâtiment *</label>
                <input type="text" class="form-control" id="batiment" name="batiment" required>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-md-12">
                <label for="equipements" class="form-label">Équipements</label>
                <textarea class="form-control" id="equipements" name="equipements" rows="3" placeholder="Décrivez les équipements disponibles..."></textarea>
              </div>
            </div>
            <div class="row mt-3">
              <div class="col-md-6">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="disponible" name="disponible" checked>
                  <label class="form-check-label" for="disponible">
                    Salle disponible
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-check-circle me-2"></i>Enregistrer
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Loading Spinner -->
  <div class="position-fixed top-50 start-50 translate-middle d-none" id="loadingSpinner">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Confirmation Modal -->
  <div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-exclamation-triangle text-warning me-2"></i>Confirmation
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <p id="confirmationMessage">Êtes-vous sûr de vouloir supprimer cette salle ?</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="button" class="btn btn-danger" id="confirmButton">
            <i class="bi bi-trash me-2"></i>Supprimer
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast Container -->
  <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toastContainer"></div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/salles.js"></script>
</body>
</html>
