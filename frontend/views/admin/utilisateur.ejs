<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Gestion des utilisateurs</title>
   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

    .topbar {
      width: 100%;
      height: 60px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding:40px;
      margin-bottom: 50px;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color:white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
    
      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body>

  <!-- Admin Topbar -->
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'utilisateur' }) %>

  <div class="main-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2>Gestion des utilisateurs</h2>
      <div>
        <a href="/admin/registration" class="btn btn-primary me-2">
          <i class="bi bi-person-plus"></i> Enregistrement complet
        </a>
        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modalAjout">
          <i class="bi bi-plus-circle"></i> Ajout rapide
        </button>
      </div>
    </div>

    <!-- Tableau -->
    <table class="table table-bordered table-hover">
      <thead class="table-secondary">
        <tr>
          <th>Matricule</th>
          <th>Rôle</th>
          <th>Nom complet</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <% utilisateurs.forEach(util => { %>
          <tr>
            <td><%= util.matricule %></td>
            <td>
              <span class="badge bg-<%= util.role === 'admin' ? 'primary' : util.role === 'professeur' ? 'success' : 'warning' %>">
                <%= util.role === 'professeur' ? 'Professeur' : util.role === 'eleve' ? 'Élève' : 'Admin' %>
              </span>
            </td>
            <td><%= util.nom || 'Non défini' %> <%= util.prenom || '' %></td>
            <td>
              <span class="badge bg-success">
                <i class="bi bi-check-circle me-1"></i>Actif
              </span>
            </td>
            <td>
              <!-- Modifier -->
              <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#modalModifier_<%= util.matricule %>">
                <i class="bi bi-pencil"></i>
              </button>

              <!-- Supprimer -->
              <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#modalSupprimer_<%= util.matricule %>">
                <i class="bi bi-trash"></i>
              </button>
            </td>
          </tr>

          <!-- Modal Modifier -->
          <div class="modal fade" id="modalModifier_<%= util.matricule %>" tabindex="-1">
            <div class="modal-dialog">
              <form method="POST" action="/admin/utilisateur/modifier/<%= util.matricule %>">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">Modifier utilisateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                  </div>
                  <div class="modal-body">
                    <div class="mb-3">
                      <label class="form-label">Matricule</label>
                      <input type="text" class="form-control" value="<%= util.matricule %>" readonly>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">Rôle</label>
                      <select name="role" class="form-select" required>
                        <option value="admin" <%= util.role === 'admin' ? 'selected' : '' %>>Admin</option>
                        <option value="professeur" <%= util.role === 'professeur' ? 'selected' : '' %>>Professeur</option>
                        <option value="eleve" <%= util.role === 'eleve' ? 'selected' : '' %>>Élève</option>
                        <option value="parent" <%= util.role === 'parent' ? 'selected' : '' %>>Parent</option>
                      </select>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">Nouveau mot de passe</label>
                      <input type="password" name="password" class="form-control" placeholder="Laisser vide pour ne pas modifier">
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Modifier</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- Modal Supprimer -->
          <div class="modal fade" id="modalSupprimer_<%= util.matricule %>" tabindex="-1">
            <div class="modal-dialog">
              <form method="POST" action="/admin/utilisateur/supprimer/<%= util.matricule %>">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">Supprimer utilisateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                  </div>
                  <div class="modal-body">
                    Êtes-vous sûr de vouloir supprimer <strong><%= util.matricule %></strong> ?
                  </div>
                  <div class="modal-footer">
                    <button type="submit" class="btn btn-danger">Oui, supprimer</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        <% }) %>
      </tbody>
    </table>
  </div>

  <!-- Modal Ajout -->
  <div class="modal fade" id="modalAjout" tabindex="-1">
    <div class="modal-dialog">
      <form method="POST" action="/admin/utilisateur/ajouter">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Ajouter un utilisateur</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">Matricule</label>
              <input type="text" name="matricule" class="form-control" required>
            </div>
            <div class="mb-3">
              <label class="form-label">Rôle</label>
              <select name="role" class="form-select" required>
                <option value="admin">Admin</option>
                <option value="professeur">Professeur</option>
                <option value="eleve">Élève</option>
                <option value="parent">Parent</option>
              </select>
            </div>
            <div class="mb-3">
              <label class="form-label">Mot de passe</label>
              <input type="password" name="password" class="form-control" required>
            </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-primary">Ajouter</button>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
