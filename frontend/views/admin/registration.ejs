<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Enregistrement d'utilisateurs</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

    .topbar {
      width: 100%;
      height: 60px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 40px;
      margin-bottom: 50px;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color: white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .registration-card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      padding: 30px;
      margin-bottom: 30px;
    }

    .role-section {
      display: none;
    }

    .role-section.active {
      display: block;
    }

    .form-floating {
      margin-bottom: 1rem;
    }

    .password-strength {
      height: 5px;
      border-radius: 3px;
      margin-top: 5px;
      transition: all 0.3s ease;
    }

    .strength-weak { background-color: #dc3545; }
    .strength-medium { background-color: #ffc107; }
    .strength-strong { background-color: #198754; }

    .alert-custom {
      border-radius: 10px;
      border: none;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body>

  <!-- Admin Topbar -->
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'registration' }) %>

  <div class="main-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="bi bi-person-plus me-2"></i>Enregistrement d'utilisateurs</h2>
      <a href="/admin/utilisateur" class="btn btn-outline-primary">
        <i class="bi bi-people me-2"></i>Voir tous les utilisateurs
      </a>
    </div>

    <!-- Alert pour les messages -->
    <div id="alertContainer"></div>

    <!-- Formulaire d'enregistrement -->
    <div class="registration-card">
      <form id="registrationForm">
        <div class="row">
          <div class="col-md-6">
            <div class="form-floating">
              <input type="text" class="form-control" id="matricule" name="matricule" placeholder="Matricule" required>
              <label for="matricule"><i class="bi bi-card-text me-2"></i>Matricule</label>
              <div class="invalid-feedback" id="matricule-feedback"></div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating">
              <select class="form-select" id="role" name="role" required>
                <option value="">Sélectionner un rôle</option>
                <option value="eleve">Élève</option>
                <option value="professeur">Professeur</option>
                <option value="admin">Administrateur</option>
              </select>
              <label for="role"><i class="bi bi-person-badge me-2"></i>Rôle</label>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-floating">
              <input type="password" class="form-control" id="password" name="password" placeholder="Mot de passe" required>
              <label for="password"><i class="bi bi-lock me-2"></i>Mot de passe</label>
              <div class="password-strength" id="passwordStrength"></div>
              <div class="invalid-feedback" id="password-feedback"></div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating">
              <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="Confirmer le mot de passe" required>
              <label for="confirmPassword"><i class="bi bi-lock-fill me-2"></i>Confirmer le mot de passe</label>
              <div class="invalid-feedback" id="confirmPassword-feedback"></div>
            </div>
          </div>
        </div>

        <!-- Section commune -->
        <div class="row">
          <div class="col-md-6">
            <div class="form-floating">
              <input type="text" class="form-control" id="nom" name="nom" placeholder="Nom" required>
              <label for="nom"><i class="bi bi-person me-2"></i>Nom</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating">
              <input type="text" class="form-control" id="prenom" name="prenom" placeholder="Prénom" required>
              <label for="prenom"><i class="bi bi-person me-2"></i>Prénom</label>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-floating">
              <select class="form-select" id="sexe" name="sexe" required>
                <option value="">Sélectionner le sexe</option>
                <option value="homme">Homme</option>
                <option value="femme">Femme</option>
              </select>
              <label for="sexe"><i class="bi bi-gender-ambiguous me-2"></i>Sexe</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-floating">
              <input type="tel" class="form-control" id="telephone" name="telephone" placeholder="Téléphone">
              <label for="telephone"><i class="bi bi-telephone me-2"></i>Téléphone</label>
            </div>
          </div>
        </div>

        <!-- Section spécifique aux élèves -->
        <div id="eleveSection" class="role-section">
          <h5 class="mt-4 mb-3 text-primary"><i class="bi bi-mortarboard me-2"></i>Informations élève</h5>
          <div class="row">
            <div class="col-md-6">
              <div class="form-floating">
                <input type="date" class="form-control" id="date_naissance" name="date_naissance">
                <label for="date_naissance"><i class="bi bi-calendar me-2"></i>Date de naissance</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating">
                <select class="form-select" id="nom_classe" name="nom_classe">
                  <option value="">Sélectionner une classe</option>
                  <% classes.forEach(classe => { %>
                    <option value="<%= classe.nom_classe %>"><%= classe.nom_classe %> - <%= classe.niveau %></option>
                  <% }) %>
                </select>
                <label for="nom_classe"><i class="bi bi-building me-2"></i>Classe</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Section spécifique aux professeurs et admins -->
        <div id="profAdminSection" class="role-section">
          <h5 class="mt-4 mb-3 text-success"><i class="bi bi-person-badge me-2"></i>Informations professionnelles</h5>
          <div class="row">
            <div class="col-md-6">
              <div class="form-floating">
                <input type="email" class="form-control" id="email" name="email">
                <label for="email"><i class="bi bi-envelope me-2"></i>Email</label>
                <div class="invalid-feedback" id="email-feedback"></div>
              </div>
            </div>
            <div class="col-md-6" id="specialisteField">
              <div class="form-floating">
                <input type="text" class="form-control" id="specialiste" name="specialiste" placeholder="Spécialité">
                <label for="specialiste"><i class="bi bi-award me-2"></i>Spécialité</label>
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
          <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
            <i class="bi bi-arrow-clockwise me-2"></i>Réinitialiser
          </button>
          <button type="submit" class="btn btn-primary" id="submitBtn">
            <i class="bi bi-person-plus me-2"></i>Créer le compte
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Gestion des sections selon le rôle
    document.getElementById('role').addEventListener('change', function() {
      const role = this.value;
      const eleveSection = document.getElementById('eleveSection');
      const profAdminSection = document.getElementById('profAdminSection');
      const specialisteField = document.getElementById('specialisteField');
      const emailField = document.getElementById('email');
      const classeField = document.getElementById('nom_classe');

      // Réinitialiser les sections
      eleveSection.classList.remove('active');
      profAdminSection.classList.remove('active');

      // Réinitialiser les champs requis
      emailField.required = false;
      classeField.required = false;

      if (role === 'eleve') {
        eleveSection.classList.add('active');
        classeField.required = true;
        specialisteField.style.display = 'none';
      } else if (role === 'professeur') {
        profAdminSection.classList.add('active');
        emailField.required = true;
        specialisteField.style.display = 'block';
      } else if (role === 'admin') {
        profAdminSection.classList.add('active');
        emailField.required = true;
        specialisteField.style.display = 'none';
      }
    });

    // Validation du mot de passe en temps réel
    document.getElementById('password').addEventListener('input', function() {
      const password = this.value;
      const strengthBar = document.getElementById('passwordStrength');
      const feedback = document.getElementById('password-feedback');

      let strength = 0;
      let message = '';

      if (password.length >= 6) strength++;
      if (password.match(/[a-z]/)) strength++;
      if (password.match(/[A-Z]/)) strength++;
      if (password.match(/[0-9]/)) strength++;
      if (password.match(/[^a-zA-Z0-9]/)) strength++;

      strengthBar.style.width = (strength * 20) + '%';

      if (strength < 2) {
        strengthBar.className = 'password-strength strength-weak';
        message = 'Mot de passe faible';
      } else if (strength < 4) {
        strengthBar.className = 'password-strength strength-medium';
        message = 'Mot de passe moyen';
      } else {
        strengthBar.className = 'password-strength strength-strong';
        message = 'Mot de passe fort';
      }

      feedback.textContent = message;
    });

    // Validation de la confirmation du mot de passe
    document.getElementById('confirmPassword').addEventListener('input', function() {
      const password = document.getElementById('password').value;
      const confirmPassword = this.value;
      const feedback = document.getElementById('confirmPassword-feedback');

      if (confirmPassword && password !== confirmPassword) {
        this.classList.add('is-invalid');
        feedback.textContent = 'Les mots de passe ne correspondent pas';
      } else {
        this.classList.remove('is-invalid');
        feedback.textContent = '';
      }
    });

    // Vérification de la disponibilité du matricule
    let matriculeTimeout;
    document.getElementById('matricule').addEventListener('input', function() {
      const matricule = this.value;
      const feedback = document.getElementById('matricule-feedback');

      clearTimeout(matriculeTimeout);

      if (matricule.length >= 3) {
        matriculeTimeout = setTimeout(() => {
          fetch(`/admin/utilisateur/check/${matricule}`)
            .then(response => response.json())
            .then(data => {
              if (data.success && !data.available) {
                this.classList.add('is-invalid');
                feedback.textContent = 'Ce matricule est déjà utilisé';
              } else {
                this.classList.remove('is-invalid');
                feedback.textContent = '';
              }
            })
            .catch(error => {
              console.error('Erreur vérification matricule:', error);
            });
        }, 500);
      }
    });

    // Soumission du formulaire
    document.getElementById('registrationForm').addEventListener('submit', function(e) {
      e.preventDefault();

      const submitBtn = document.getElementById('submitBtn');
      const originalText = submitBtn.innerHTML;

      // Désactiver le bouton et afficher le loading
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Création en cours...';

      const formData = new FormData(this);
      const data = Object.fromEntries(formData);

      fetch('/admin/registration/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showAlert('success', data.message);
          resetForm();
        } else {
          showAlert('danger', data.message);
        }
      })
      .catch(error => {
        console.error('Erreur:', error);
        showAlert('danger', 'Erreur lors de la création du compte');
      })
      .finally(() => {
        // Réactiver le bouton
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      });
    });

    // Fonction pour afficher les alertes
    function showAlert(type, message) {
      const alertContainer = document.getElementById('alertContainer');
      const alert = document.createElement('div');
      alert.className = `alert alert-${type} alert-custom alert-dismissible fade show`;
      alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

      alertContainer.innerHTML = '';
      alertContainer.appendChild(alert);

      // Auto-hide après 5 secondes
      setTimeout(() => {
        if (alert.parentNode) {
          alert.remove();
        }
      }, 5000);
    }

    // Fonction pour réinitialiser le formulaire
    function resetForm() {
      document.getElementById('registrationForm').reset();
      document.getElementById('role').dispatchEvent(new Event('change'));

      // Réinitialiser les classes de validation
      document.querySelectorAll('.is-invalid').forEach(el => {
        el.classList.remove('is-invalid');
      });

      // Réinitialiser la barre de force du mot de passe
      document.getElementById('passwordStrength').style.width = '0%';
      document.getElementById('passwordStrength').className = 'password-strength';
    }
  </script>
</body>
</html>
