<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mon Profil - Admin</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

    .topbar {
      width: 100%;
      height: 60px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 40px;
      margin-bottom: 50px;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover, .sidebar a.active {
      background-color: #1e77cf;
      color: white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .profile-card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      padding: 30px;
      margin-bottom: 30px;
    }

    .profile-avatar {
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, #0d6efd, #1e77cf);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 2.5rem;
      font-weight: bold;
      margin: 0 auto 20px;
    }

    .password-strength {
      height: 5px;
      border-radius: 3px;
      margin-top: 5px;
      transition: all 0.3s ease;
    }

    .strength-weak { background-color: #dc3545; }
    .strength-medium { background-color: #ffc107; }
    .strength-strong { background-color: #198754; }

    .alert-custom {
      border-radius: 10px;
      border: none;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body>
  <!-- Admin Topbar -->
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'profil' }) %>

  <div class="main-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="bi bi-person-circle me-2"></i>Mon Profil</h2>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Profile Information Card -->
    <div class="profile-card">
      <div class="row">
        <div class="col-md-4 text-center">
          <div class="profile-avatar">
            <%= profile.nom ? profile.nom.charAt(0).toUpperCase() : 'A' %><%= profile.prenom ? profile.prenom.charAt(0).toUpperCase() : 'D' %>
          </div>
          <h4><%= profile.nom || 'Admin' %> <%= profile.prenom || 'User' %></h4>
          <p class="text-muted">Administrateur</p>
          <span class="badge bg-primary">Matricule: <%= profile.matricule %></span>
        </div>
        <div class="col-md-8">
          <h5 class="mb-3"><i class="bi bi-person-lines-fill me-2"></i>Informations personnelles</h5>
          <form id="profileForm">
            <div class="row">
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="nom" name="nom" value="<%= profile.nom || '' %>" required>
                  <label for="nom"><i class="bi bi-person me-2"></i>Nom</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="prenom" name="prenom" value="<%= profile.prenom || '' %>" required>
                  <label for="prenom"><i class="bi bi-person me-2"></i>Prénom</label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="email" class="form-control" id="email" name="email" value="<%= profile.email || '' %>">
                  <label for="email"><i class="bi bi-envelope me-2"></i>Email</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <select class="form-select" id="sexe" name="sexe">
                    <option value="">Sélectionner</option>
                    <option value="homme" <%= profile.sexe === 'homme' ? 'selected' : '' %>>Homme</option>
                    <option value="femme" <%= profile.sexe === 'femme' ? 'selected' : '' %>>Femme</option>
                  </select>
                  <label for="sexe"><i class="bi bi-gender-ambiguous me-2"></i>Sexe</label>
                </div>
              </div>
            </div>
            <div class="form-floating mb-3">
              <input type="tel" class="form-control" id="telephone" name="telephone" value="<%= profile.telephone || '' %>">
              <label for="telephone"><i class="bi bi-telephone me-2"></i>Téléphone</label>
            </div>
            <button type="submit" class="btn btn-primary">
              <i class="bi bi-check-circle me-2"></i>Mettre à jour le profil
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Password Change Card -->
    <div class="profile-card">
      <h5 class="mb-3"><i class="bi bi-shield-lock me-2"></i>Changer le mot de passe</h5>
      <form id="passwordForm">
        <div class="row">
          <div class="col-md-4">
            <div class="form-floating mb-3">
              <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
              <label for="currentPassword"><i class="bi bi-lock me-2"></i>Mot de passe actuel</label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating mb-3">
              <input type="password" class="form-control" id="newPassword" name="newPassword" required>
              <label for="newPassword"><i class="bi bi-lock-fill me-2"></i>Nouveau mot de passe</label>
              <div class="password-strength" id="passwordStrength"></div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating mb-3">
              <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
              <label for="confirmPassword"><i class="bi bi-lock-fill me-2"></i>Confirmer le mot de passe</label>
            </div>
          </div>
        </div>
        <button type="submit" class="btn btn-warning">
          <i class="bi bi-shield-check me-2"></i>Changer le mot de passe
        </button>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
  <script>
    // Profile form submission
    $('#profileForm').on('submit', function(e) {
      e.preventDefault();
      
      const formData = {
        nom: $('#nom').val(),
        prenom: $('#prenom').val(),
        email: $('#email').val(),
        sexe: $('#sexe').val(),
        telephone: $('#telephone').val()
      };

      $.ajax({
        url: '/profile',
        method: 'PUT',
        data: formData,
        success: function(response) {
          showAlert('success', response.message);
          // Update avatar initials
          const initials = (formData.nom.charAt(0) + formData.prenom.charAt(0)).toUpperCase();
          $('.profile-avatar').text(initials);
        },
        error: function(xhr) {
          const response = xhr.responseJSON;
          showAlert('danger', response.message || 'Erreur lors de la mise à jour');
        }
      });
    });

    // Password form submission
    $('#passwordForm').on('submit', function(e) {
      e.preventDefault();
      
      const currentPassword = $('#currentPassword').val();
      const newPassword = $('#newPassword').val();
      const confirmPassword = $('#confirmPassword').val();

      if (newPassword !== confirmPassword) {
        showAlert('danger', 'Les nouveaux mots de passe ne correspondent pas');
        return;
      }

      $.ajax({
        url: '/change-password',
        method: 'POST',
        data: {
          currentPassword: currentPassword,
          newPassword: newPassword,
          confirmPassword: confirmPassword
        },
        success: function(response) {
          showAlert('success', response.message);
          $('#passwordForm')[0].reset();
          $('#passwordStrength').removeClass('strength-weak strength-medium strength-strong');
        },
        error: function(xhr) {
          const response = xhr.responseJSON;
          showAlert('danger', response.message || 'Erreur lors du changement de mot de passe');
        }
      });
    });

    // Password strength indicator
    $('#newPassword').on('input', function() {
      const password = $(this).val();
      const strength = calculatePasswordStrength(password);
      const strengthBar = $('#passwordStrength');
      
      strengthBar.removeClass('strength-weak strength-medium strength-strong');
      
      if (password.length > 0) {
        if (strength < 3) {
          strengthBar.addClass('strength-weak');
        } else if (strength < 5) {
          strengthBar.addClass('strength-medium');
        } else {
          strengthBar.addClass('strength-strong');
        }
      }
    });

    function calculatePasswordStrength(password) {
      let strength = 0;
      if (password.length >= 8) strength++;
      if (/[a-z]/.test(password)) strength++;
      if (/[A-Z]/.test(password)) strength++;
      if (/[0-9]/.test(password)) strength++;
      if (/[^A-Za-z0-9]/.test(password)) strength++;
      return strength;
    }

    function showAlert(type, message) {
      const alertHtml = `
        <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
          <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      `;
      $('#alertContainer').html(alertHtml);
      
      // Auto-dismiss after 5 seconds
      setTimeout(() => {
        $('.alert').alert('close');
      }, 5000);
    }
  </script>
</body>
</html>
