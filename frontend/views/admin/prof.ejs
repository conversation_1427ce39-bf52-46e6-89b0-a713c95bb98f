<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Gestion des Professeurs</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color:white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
    
      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'prof' }) %>

  <div class="main-content mt-5">
    <h2 class="mb-4">Gestion des Professeurs</h2>
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#addModal">Ajouter un professeur</button>

    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Matricule</th>
          <th>Nom</th>
          <th>Prénom</th>
          <th>Sexe</th>
          <th>Spécialité</th>
          <th>Email</th>
          <th>Téléphone</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody id="profBody"></tbody>
    </table>
  </div>

  <!-- Modal ajout -->
  <div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
      <form id="addProfForm" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Ajouter Professeur</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <input name="matricule" class="form-control mb-2" placeholder="Matricule" required>
          <input name="nom" class="form-control mb-2" placeholder="Nom" required>
          <input name="prenom" class="form-control mb-2" placeholder="Prénom" required>
          <select name="sexe" class="form-control mb-2" required>
            <option value="">Sexe</option>
            <option value="homme">Homme</option>
            <option value="femme">Femme</option>
          </select>
          <input name="specialiste" class="form-control mb-2" placeholder="Spécialité" required>
          <input name="email" class="form-control mb-2" placeholder="Email" required>
          <input name="telephone" class="form-control mb-2" placeholder="Téléphone" required>
          <div class="mb-2">
            <label class="form-label small text-muted">Mot de passe (optionnel)</label>
            <input name="password" type="password" class="form-control" placeholder="Laisser vide pour mot de passe par défaut (prof123)" />
            <div class="form-text">Si vide, le mot de passe par défaut "prof123" sera utilisé</div>
          </div>
        </div>
        <div class="modal-footer">
          
          <button class="btn btn-success">Ajouter</button>
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Annuler</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal édition -->
  <div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
      <form id="editProfForm" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Modifier Professeur</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <input name="matricule" id="editMatricule" class="form-control mb-2" readonly>
          <input name="nom" id="editNom" class="form-control mb-2" required>
          <input name="prenom" id="editPrenom" class="form-control mb-2" required>
          <select name="sexe" id="editSexe" class="form-control mb-2" required>
            <option value="homme">Homme</option>
            <option value="femme">Femme</option>
          </select>
          <input name="specialiste" id="editSpecialiste" class="form-control mb-2" required>
          <input name="email" id="editEmail" class="form-control mb-2" required>
          <input name="telephone" id="editTelephone" class="form-control mb-2" required>
          <div class="mb-2">
            <label class="form-label small text-muted">Nouveau mot de passe (optionnel)</label>
            <input name="password" type="password" class="form-control" placeholder="Laisser vide pour ne pas modifier" />
            <div class="form-text">Laisser vide pour conserver le mot de passe actuel</div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-success">Modifier</button>
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Annuler</button>
        </div>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

  <script>
    function loadProfs() {
      $.get('/api/prof', response => {
        $('#profBody').html('');

        // Handle new API response format
        const data = response.success ? response.data : response;

        data.forEach(p => {
          $('#profBody').append(`
            <tr>
              <td>${p.matricule}</td>
              <td>${p.nom}</td>
              <td>${p.prenom}</td>
              <td>${p.sexe}</td>
              <td>${p.specialiste}</td>
              <td>${p.email}</td>
              <td>${p.telephone}</td>
              <td>
                <button class="btn btn-primary btn-sm" onclick='openEdit(${JSON.stringify(p)})'>Modifier</button>
                <button class="btn btn-danger btn-sm" onclick="deleteProf('${p.matricule}')">Supprimer</button>
              </td>
            </tr>
          `);
        });
      });
    }

    $('#addProfForm').submit(function(e) {
      e.preventDefault();
      $.post('/api/prof', $(this).serialize(), () => {
        $('#addModal').modal('hide');
        loadProfs();
        this.reset();
      });
    });

    function openEdit(prof) {
      $('#editMatricule').val(prof.matricule);
      $('#editNom').val(prof.nom);
      $('#editPrenom').val(prof.prenom);
      $('#editSexe').val(prof.sexe);
      $('#editSpecialiste').val(prof.specialiste);
      $('#editEmail').val(prof.email);
      $('#editTelephone').val(prof.telephone);
      new bootstrap.Modal(document.getElementById('editModal')).show();
    }

    $('#editProfForm').submit(function(e) {
      e.preventDefault();
      const matricule = $('#editMatricule').val();
      $.ajax({
        url: `/api/prof/${matricule}`,
        method: 'PUT',
        data: $(this).serialize(),
        success: () => {
          $('#editModal').modal('hide');
          loadProfs();
        }
      });
    });

    function deleteProf(matricule) {
      if (confirm("Supprimer ce professeur ?")) {
        $.ajax({
          url: `/api/prof/${matricule}`,
          method: 'DELETE',
          success: loadProfs
        });
      }
    }

    $(document).ready(loadProfs);
  </script>
</body>
</html>
