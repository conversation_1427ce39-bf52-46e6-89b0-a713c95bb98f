<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Gestion des Élèves</title>

  <!-- Vendor CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />

  <!-- Shared Admin Styles -->
  <%- include('../partials/admin-styles') %>

  <!-- Page-specific minimal styles only for print and subtle enhancements.
       Duplicate sidebar/main-content definitions removed per guidance. -->
  <style>
    /* Improve table readability and sticky header using existing Bootstrap tokens */
    .table thead th {
      position: sticky;
      top: 0;
      z-index: 2;
      background-color: #f1f3f5; /* light header for contrast */
    }

    .table tbody tr:hover {
      background-color: #f8f9fa; /* subtle hover */
    }

    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
      .main-content {
        margin: 0 !important;
        padding: 0 !important;
      }
    }
  </style>
</head>
<body class="bg-light">
  <!-- Top navigation and sidebar -->
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'eleve' }) %>

  <header class="main-content">
    <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center justify-content-between gap-2 mt-4">
      <div>
        <h1 class="h3 mb-1">Gestion des Élèves</h1>
        <p class="text-muted mb-0">Créer, consulter et mettre à jour les informations des élèves</p>
      </div>
      <div class="mt-2 mt-md-0">
        <button class="btn btn-success d-inline-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addModal" aria-label="Ajouter un élève">
          <i class="bi bi-plus-lg me-2" aria-hidden="true"></i>
          Ajouter un élève
        </button>
      </div>
    </div>
  </header>

  <main class="main-content" role="main" aria-labelledby="pageTitle">
    <section class="mt-3">
      <div class="table-responsive rounded-3 shadow-sm bg-white">
        <table class="table table-hover table-bordered align-middle mb-0">
          <thead class="table-light">
            <tr>
              <th scope="col">Matricule</th>
              <th scope="col">Nom</th>
              <th scope="col">Prénom</th>
              <th scope="col">Sexe</th>
              <th scope="col">Date Naissance</th>
              <th scope="col">Classe</th>
              <th scope="col" class="text-center" style="width: 140px;">Actions</th>
            </tr>
          </thead>
          <tbody id="eleveBody">
            <!-- Rempli dynamiquement via loadEleves() -->
          </tbody>
        </table>

        <!-- Empty state container; shown when #eleveBody is empty (no extra logic added) -->
        <div id="eleveEmptyState" class="text-center py-5 text-muted d-none">
          <i class="bi bi-people fs-1 d-block mb-2" aria-hidden="true"></i>
          <p class="mb-0">Aucun élève disponible pour le moment.</p>
        </div>
      </div>
    </section>
  </main>

  <!-- Modal ajout -->
  <div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addEleveLabel" aria-hidden="true">
    <div class="modal-dialog">
      <form id="addEleveForm" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addEleveLabel">Ajouter Élève</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
        </div>
        <div class="modal-body">
          <input name="matricule" class="form-control mb-2" placeholder="Matricule" required />
          <input name="nom" class="form-control mb-2" placeholder="Nom" required />
          <input name="prenom" class="form-control mb-2" placeholder="Prénom" required />
          <select name="sexe" class="form-control mb-2" required aria-label="Sélectionner le sexe">
            <option value="">Sexe</option>
            <option value="homme">Homme</option>
            <option value="femme">Femme</option>
          </select>
          <input type="date" name="date_naissance" class="form-control mb-2" required aria-label="Date de naissance" />
          <select class="form-select mb-2" name="classe" required aria-label="Sélectionner la classe">
            <option value="">-- Sélectionner une classe --</option>
            <% classes.forEach(classe => { %>
              <option value="<%= classe.nom_classe %>"><%= classe.nom_classe %></option>
            <% }); %>
          </select>
          <div class="mb-2">
            <label class="form-label small text-muted">Mot de passe (optionnel)</label>
            <input name="password" type="password" class="form-control" placeholder="Laisser vide pour mot de passe par défaut (eleve123)" />
            <div class="form-text">Si vide, le mot de passe par défaut "eleve123" sera utilisé</div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-success" aria-label="Confirmer l’ajout">Ajouter</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" aria-label="Annuler l’ajout">Annuler</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal Modifier Élève -->
  <div class="modal fade" id="modifierEleveModal" tabindex="-1" aria-labelledby="modifierEleveLabel" aria-hidden="true">
    <div class="modal-dialog">
      <form id="formModifierEleve" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modifierEleveLabel">Modifier Élève</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
        </div>
        <div class="modal-body">
          <input type="hidden" id="modifierMatricule" name="matricule" />
          <div class="mb-3">
            <label for="modifierNom" class="form-label">Nom</label>
            <input type="text" class="form-control" id="modifierNom" name="nom" required />
          </div>
          <div class="mb-3">
            <label for="modifierPrenom" class="form-label">Prénom</label>
            <input type="text" class="form-control" id="modifierPrenom" name="prenom" required />
          </div>
          <div class="mb-3">
            <label for="modifierClasse" class="form-label">Classe</label>
            <select class="form-control" id="modifierClasse" name="classe" required aria-label="Modifier la classe">
              <option value="">-- Sélectionner une classe --</option>
              <% classes.forEach((classe) => { %>
                <option value="<%= classe.nom_classe %>"><%= classe.nom_classe %></option>
              <% }); %>
            </select>
          </div>
          <div class="mb-3">
            <label for="modifierPassword" class="form-label">Nouveau mot de passe (optionnel)</label>
            <input type="password" class="form-control" id="modifierPassword" name="password" placeholder="Laisser vide pour ne pas modifier" />
            <div class="form-text">Laisser vide pour conserver le mot de passe actuel</div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary" aria-label="Enregistrer les modifications">Enregistrer</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" aria-label="Annuler la modification">Annuler</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Vendor JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

  <!-- Page JS: preserve all endpoints/logic; enhance rendering for empty state and aria labels -->
  <script>
    // Fonction pour charger la liste des élèves
    function loadEleves() {
      $.get('/api/eleve', response => {
        const $body = $('#eleveBody');
        const $empty = $('#eleveEmptyState');
        $body.html('');

        // Handle new API response format
        const data = response.success ? response.data : response;

        if (Array.isArray(data) && data.length > 0) {
          $empty.addClass('d-none');
          data.forEach(e => {
            $body.append(`
              <tr>
                <td>${e.matricule}</td>
                <td>${e.nom}</td>
                <td>${e.prenom}</td>
                <td>${e.sexe}</td>
                <td>${e.date_naissance}</td>
                <td>${e.nom_classe}</td>
                <td class="text-center">
                  <div class="btn-group btn-group-sm" role="group" aria-label="Actions élève ${e.matricule}">
                    <button class="btn btn-primary" onclick='openEdit(${JSON.stringify(e)})' aria-label="Modifier l’élève ${e.matricule}">
                      <i class="bi bi-pencil-square" aria-hidden="true"></i>
                    </button>
                    <button class="btn btn-danger" onclick="deleteEleve('${e.matricule}')" aria-label="Supprimer l’élève ${e.matricule}">
                      <i class="bi bi-trash" aria-hidden="true"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `);
          });
        } else {
          // No new logic; just toggle visibility based on the same data
          $empty.removeClass('d-none');
        }
      });
    }

    // Ajouter un élève
    $('#addEleveForm').submit(function(e) {
      e.preventDefault();
      $.post('/api/eleve', $(this).serialize(), () => {
        $('#addModal').modal('hide');
        loadEleves();
        this.reset();
      });
    });

    // Fonction pour ouvrir le modal de modification avec les données de l'élève
    function openEdit(eleve) {
      $('#modifierMatricule').val(eleve.matricule);
      $('#modifierNom').val(eleve.nom);
      $('#modifierPrenom').val(eleve.prenom);
      $('#modifierClasse').val(eleve.nom_classe);
      const modal = new bootstrap.Modal(document.getElementById('modifierEleveModal'));
      modal.show();
    }

    // Soumission du formulaire de modification
    $('#formModifierEleve').submit(function(e) {
      e.preventDefault();
      const matricule = $('#modifierMatricule').val();
      $.ajax({
        url: `/api/eleve/${matricule}`,
        method: 'PUT',
        data: $(this).serialize(),
        success: () => {
          $('#modifierEleveModal').modal('hide');
          loadEleves();
        },
        error: () => {
          alert('Erreur lors de la modification');
        }
      });
    });

    // Supprimer un élève
    function deleteEleve(matricule) {
      if (confirm("Supprimer cet élève ?")) {
        $.ajax({
          url: `/api/eleve/${matricule}`,
          method: 'DELETE',
          success: loadEleves,
        });
      }
    }

    // Chargement initial
    $(document).ready(function() {
      loadEleves();
    });
  </script>
</body>
</html>