<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Gestion des Cours</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
    /* Page-specific styles for cour */

    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color:white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }
      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'cour' }) %>

  <div class="main-content mt-5">
    <h2 class="mb-4">Gestion des matieres</h2>
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#addModal">Ajouter une matiere</button>

    <table class="table table-bordered table-striped">
      <thead>
        <tr>
          <th>ID</th>
          <th>Nom_matiere</th>
          <th>Professeur</th>
          <th>Classe</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody id="courBody">
        <!-- Données AJAX -->
      </tbody>
    </table>
  </div>

  <!-- Modal ajout -->
  <div class="modal fade" id="addModal" tabindex="-1">
    <div class="modal-dialog">
      <form id="addCourForm" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Ajouter une matiere</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <input name="nom" class="form-control mb-2" placeholder="Nom du cours" required />
          <select name="professeur_matricule" id="profSelect" class="form-control mb-2" required>
            <option value="">Sélectionnez un professeur</option>
          </select>
          <select name="nom_classe" id="classeSelect" class="form-control mb-2" required>
            <option value="">Sélectionnez une classe</option>
          </select>
        </div>
        <div class="modal-footer">
          <button class="btn btn-success">Ajouter</button>
          <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Annuler</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal édition -->
  <div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
      <form id="editCourForm" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Modifier une matiere</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <input type="hidden" name="matiere_id" id="editId" />
          <input name="nom" id="editNom" class="form-control mb-2" required />
          <select name="professeur_matricule" id="editProf" class="form-control mb-2" required></select>
          <select name="nom_classe" id="editClasse" class="form-control mb-2" required></select>
        </div>
        <div class="modal-footer">
          <button class="btn btn-warning">Modifier</button>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        </div>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>

  <script>
    function loadCours() {
      $.get('/api/cour', data => {
        $('#courBody').html('');
        data.forEach(c => {
          $('#courBody').append(`
            <tr>
              <td>${c.matiere_id}</td>
              <td>${c.nom}</td>
              <td>${c.professeur_nom} ${c.professeur_prenom}</td>
              <td>${c.nom_classe}</td>
              <td>
                <button class="btn btn-sm btn-primary" onclick='openEdit(${JSON.stringify(c)})'>Modifier</button>
                <button class="btn btn-sm btn-danger" onclick='deleteCour(${c.matiere_id})'>Supprimer</button>
              </td>
            </tr>
          `);
        });
      });
    }

    function loadProfEtClasse() {
      $.get('/api/cour/professeurs', data => {
        $('#profSelect, #editProf').html('<option value="">Sélectionnez un professeur</option>');
        data.forEach(p => {
          $('#profSelect, #editProf').append(`<option value="${p.matricule}">${p.nom} ${p.prenom}</option>`);
        });
      });

      $.get('/api/cour/classes', data => {
        $('#classeSelect, #editClasse').html('<option value="">Sélectionnez une classe</option>');
        data.forEach(c => {
          $('#classeSelect, #editClasse').append(`<option value="${c.nom_classe}">${c.nom_classe}</option>`);
        });
      });
    }

    $('#addCourForm').submit(function (e) {
      e.preventDefault();
      $.post('/api/cour', $(this).serialize(), () => {
        $('#addModal').modal('hide');
        this.reset();
        loadCours();
      });
    });

    function openEdit(cour) {
      $('#editId').val(cour.matiere_id);
      $('#editNom').val(cour.nom);
      $('#editProf').val(cour.professeur_matricule);
      $('#editClasse').val(cour.nom_classe);
      new bootstrap.Modal(document.getElementById('editModal')).show();
    }

    $('#editCourForm').submit(function (e) {
      e.preventDefault();
      const id = $('#editId').val();
      $.ajax({
        url: `/api/cour/${id}`,
        method: 'PUT',
        data: $(this).serialize(),
        success: () => {
          $('#editModal').modal('hide');
          loadCours();
        }
      });
    });

    function deleteCour(id) {
      if (confirm("Confirmer la suppression ?")) {
        $.ajax({
          url: `/api/cour/${id}`,
          method: 'DELETE',
          success: loadCours
        });
      }
    }

    $(document).ready(() => {
      loadCours();
      loadProfEtClasse();
    });
  </script>
</body>
</html>
