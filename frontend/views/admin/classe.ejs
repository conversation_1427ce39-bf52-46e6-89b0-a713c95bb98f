<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Gestion des classes</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color:white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'classe' }) %>


  <div class="main-content">
    <h2 class="mb-4">Gestion des classes</h2>
    <button class="btn btn-success mb-3" data-bs-toggle="modal" data-bs-target="#modalAjout">Ajouter une classe</button>

    <table class="table table-bordered">
      <thead class="table-dark">
        <tr>
          <th>Nom</th>
          <th>Niveau</th>
          <th>Année scolaire</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody id="tableClasseBody"></tbody>
    </table>
  </div>

  <!-- Modal Ajout -->
  <div class="modal fade" id="modalAjout" tabindex="-1">
    <div class="modal-dialog">
      <form id="formAjoutClasse" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Ajouter une classe</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <input name="nom_classe" class="form-control mb-2" placeholder="Nom de la classe" required>
          <input name="niveau" class="form-control mb-2" placeholder="Niveau" required>
          <input name="annee_scolaire" class="form-control" placeholder="Année scolaire" required>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">Ajouter</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal Modifier -->
  <div class="modal fade" id="modalEdit" tabindex="-1">
    <div class="modal-dialog">
      <form id="formEditClasse" class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Modifier la classe</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <input type="hidden" name="old_nom_classe">
          <input name="nom_classe" class="form-control mb-2" placeholder="Nom de la classe" required>
          <input name="niveau" class="form-control mb-2" placeholder="Niveau" required>
          <input name="annee_scolaire" class="form-control" placeholder="Année scolaire" required>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-success">Modifier</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Modal Supprimer -->
  <div class="modal fade" id="modalDelete" tabindex="-1">
    <div class="modal-dialog">
      <form id="formDeleteClasse" class="modal-content">
        <div class="modal-header bg-danger text-white">
          <h5 class="modal-title">Supprimer la classe</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <p>Voulez-vous vraiment supprimer cette classe ?</p>
          <input type="hidden" name="nom_classe">
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-danger">Supprimer</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    const baseUrl = window.location.origin;

    function chargerClasses() {
      $.get(`${baseUrl}/api/classe`, data => {
        $('#tableClasseBody').empty();
        data.forEach(c => {
          $('#tableClasseBody').append(`
            <tr>
              <td>${c.nom_classe}</td>
              <td>${c.niveau}</td>
              <td>${c.annee_scolaire}</td>
              <td>
                <button class="btn btn-sm btn-primary me-2" onclick='ouvrirEdit(${JSON.stringify(c)})'>Modifier</button>
                <button class="btn btn-sm btn-danger" onclick='ouvrirDelete("${c.nom_classe}")'>Supprimer</button>
              </td>
            </tr>
          `);
        });
      });
    }

    $('#formAjoutClasse').submit(function(e) {
      e.preventDefault();
      $.post(`${baseUrl}/api/classe`, $(this).serialize(), () => {
        $('#modalAjout').modal('hide');
        this.reset();
        chargerClasses();
      });
    });

    function ouvrirEdit(classe) {
      const form = $('#formEditClasse')[0];
      form.old_nom_classe.value = classe.nom_classe;
      form.nom_classe.value = classe.nom_classe;
      form.niveau.value = classe.niveau;
      form.annee_scolaire.value = classe.annee_scolaire;
      new bootstrap.Modal(document.getElementById('modalEdit')).show();
    }

    $('#formEditClasse').submit(function(e) {
      e.preventDefault();
      const old_nom_classe = this.old_nom_classe.value;
      const data = $(this).serialize();
      $.ajax({
        url: `${baseUrl}/api/classe/${encodeURIComponent(old_nom_classe)}`,
        method: 'PUT',
        data,
        success: () => {
          $('#modalEdit').modal('hide');
          chargerClasses();
        },
        error: (err) => {
          alert("Erreur lors de la modification");
          console.error(err);
        }
      });
    });

    function ouvrirDelete(nom_classe) {
      $('#formDeleteClasse')[0].nom_classe.value = nom_classe;
      new bootstrap.Modal(document.getElementById('modalDelete')).show();
    }

    $('#formDeleteClasse').submit(function(e) {
      e.preventDefault();
      const id = this.nom_classe.value;
      $.ajax({
        url: `${baseUrl}/api/classe/${encodeURIComponent(id)}`,
        method: 'DELETE',
        success: () => {
          $('#modalDelete').modal('hide');
          chargerClasses();
        },
        error: (err) => {
          alert("Erreur lors de la suppression");
          console.error(err);
        }
      });
    });

    // Initialisation
    chargerClasses();
  </script>
</body>
</html>
