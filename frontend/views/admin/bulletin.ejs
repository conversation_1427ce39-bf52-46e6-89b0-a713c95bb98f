<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title><%= title %></title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons (facultatif pour icône imprimante) -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
    .admin-circle {
      background-color: #0d6efd;
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover {
      background-color: #1e77cf;
      color:white;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'bulletin' }) %>


  <div class="main-content py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2 class="text-primary">📚 Gestion des bulletins</h2>
      <div>
        <button class="btn btn-success me-2 no-print" data-bs-toggle="modal" data-bs-target="#ajouterModal">
          <i class="bi bi-plus-circle"></i> Ajouter
        </button>
        <button class="btn btn-primary no-print" onclick="window.print()">
          <i class="bi bi-printer"></i> Imprimer
        </button>
      </div>
    </div>

    <div class="table-responsive shadow-sm rounded">
      <table class="table table-bordered table-striped align-middle bg-white">
        <thead class="table-dark text-center">
          <tr>
            <th>Élève</th>
            <th>Classe</th>
            <th>Période</th>
            <th>Moyenne</th>
            <th>Appréciation</th>
            <th>Date</th>
            <th class="no-print">Actions</th>
          </tr>
        </thead>
        <tbody>
          <% bulletins.forEach(b => { %>
            <tr>
              <td><%= b.nom %> <%= b.prenom %></td>
              <td><%= b.classe || b.nom_classe %></td>
              <td><%= b.periode %></td>
              <td><%= b.moyenne_generale %></td>
              <td><%= b.appreciation %></td>
              <td><%= b.date_creation.toISOString().split('T')[0] %></td>
              <td class="no-print">
                <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#modifierModal<%= b.id %>">✏️</button>
                <button class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#supprimerModal<%= b.id %>">🗑️</button>
              </td>
            </tr>

            <!-- Modal Modifier -->
            <div class="modal fade" id="modifierModal<%= b.id %>" tabindex="-1" aria-labelledby="modifierModalLabel<%= b.id %>" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <form action="/admin/bulletin/modifier/<%= b.id %>" method="POST">
                    <div class="modal-header bg-primary text-white">
                      <h5 class="modal-title" id="modifierModalLabel<%= b.id %>">Modifier le bulletin</h5>
                      <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                      <div class="mb-3">
                        <label for="periode<%= b.id %>" class="form-label">Période</label>
                        <input type="text" name="periode" id="periode<%= b.id %>" class="form-control" value="<%= b.periode %>" required>
                      </div>
                      <div class="mb-3">
                        <label for="moyenne<%= b.id %>" class="form-label">Moyenne générale</label>
                        <input type="number" step="0.01" name="moyenne_generale" id="moyenne<%= b.id %>" class="form-control" value="<%= b.moyenne_generale %>" required>
                      </div>
                      <div class="mb-3">
                        <label for="appreciation<%= b.id %>" class="form-label">Appréciation</label>
                        <textarea name="appreciation" id="appreciation<%= b.id %>" class="form-control" rows="3" required><%= b.appreciation %></textarea>
                      </div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                      <button type="submit" class="btn btn-primary">Modifier</button>
                    </div>
                  </form>
                </div>
              </div>
            </div>

            <!-- Modal Supprimer -->
            <div class="modal fade" id="supprimerModal<%= b.id %>" tabindex="-1" aria-labelledby="supprimerModalLabel<%= b.id %>" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <form action="/admin/bulletin/supprimer/<%= b.id %>" method="POST">
                    <div class="modal-header bg-danger text-white">
                      <h5 class="modal-title" id="supprimerModalLabel<%= b.id %>">
                        <i class="bi bi-exclamation-triangle me-2"></i>Confirmation de suppression
                      </h5>
                      <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                      <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h6 class="mt-3">Êtes-vous sûr de vouloir supprimer ce bulletin ?</h6>
                        <p class="text-muted">Cette action est irréversible.</p>
                      </div>
                    </div>
                    <div class="modal-footer">
                      <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                      <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>Supprimer
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          <% }); %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Modal Ajouter -->
  <div class="modal fade" id="ajouterModal" tabindex="-1" aria-labelledby="ajouterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <form action="/admin/bulletin/ajouter" method="POST">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title" id="ajouterModalLabel">
              <i class="bi bi-plus-circle me-2"></i>Ajouter un bulletin
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="eleve_matricule" class="form-label">Élève</label>
              <select name="eleve_matricule" id="eleve_matricule" class="form-select" required>
                <option value="">Sélectionner un élève</option>
                <% eleves.forEach(e => { %>
                  <option value="<%= e.matricule %>"><%= e.nom %> <%= e.prenom %></option>
                <% }) %>
              </select>
            </div>
            <div class="mb-3">
              <label for="periode" class="form-label">Période</label>
              <input type="text" name="periode" id="periode" class="form-control" placeholder="Ex: 1er Trimestre" required>
            </div>
            <div class="mb-3">
              <label for="moyenne_generale" class="form-label">Moyenne générale</label>
              <input type="number" step="0.01" name="moyenne_generale" id="moyenne_generale" class="form-control" min="0" max="20" placeholder="0.00" required>
            </div>
            <div class="mb-3">
              <label for="appreciation" class="form-label">Appréciation</label>
              <textarea name="appreciation" id="appreciation" class="form-control" rows="3" placeholder="Saisir l'appréciation générale..." required></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="submit" class="btn btn-success">
              <i class="bi bi-check-circle me-2"></i>Ajouter
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
