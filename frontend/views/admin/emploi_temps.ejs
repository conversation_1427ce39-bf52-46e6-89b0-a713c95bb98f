<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Gestion des Emplois du Temps - NS School Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />

  <%- include('../partials/admin-styles') %>

  <style>
    /* Styles spécifiques à l'emploi du temps */
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    /* Main content */
    .main-content {
      min-height: calc(100vh - 60px);
    }

    /* Filtres et contrôles */
    .filters-section {
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }

    /* Table styles */
    .table-box {
      background-color: white; 
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      position: relative;
    }
    .planning-table {
      margin-bottom: 0;
    }
    .planning-table th {
      background-color: #0d6efd;
      color: white;
      font-weight: 600;
      text-align: center;
      vertical-align: middle;
      border: none;
      padding: 15px 10px;
    }
    .planning-table td {
      text-align: center;
      vertical-align: middle;
      padding: 12px 10px;
      border-color: #e9ecef;
    }
    .creneau-row:hover {
      background-color: #f8f9fa;
    }

    /* Badges et indicateurs */
    .badge-matiere {
      font-size: 0.85rem;
      padding: 6px 12px;
    }
    .time-badge {
      background-color: #e9ecef;
      color: #495057;
      font-family: 'Courier New', monospace;
      font-weight: 600;
    }

    /* Boutons d'action */
    .btn-action {
      border-radius: 50%;
      width: 32px;
      height: 32px;
      padding: 0;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    /* Modal styles */
    .modal-header {
      background-color: #0d6efd;
      color: white;
      border-bottom: none;
    }
    .modal-header .btn-close {
      filter: invert(1);
    }

    /* Loading states */
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    /* Responsive */
    @media print {
      .no-print { display: none; }
      .sidebar { display: none; }
      .main-content { margin-left: 0; }
    }

    @media (max-width: 768px) {
      .sidebar { transform: translateX(-100%); }
      .main-content { margin-left: 0; }
    }
  </style>
</head>
<body>
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'emploi_temps' }) %>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h2 class="mb-1">
          <i class="bi bi-calendar-week text-primary me-2"></i>
          Gestion des Emplois du Temps
        </h2>
        <p class="text-muted mb-0">Planifiez et gérez les emplois du temps des classes</p>
      </div>
      <div class="d-flex gap-2">
        <button class="btn btn-outline-primary no-print" onclick="window.print()">
          <i class="bi bi-printer"></i> Imprimer
        </button>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addModal">
          <i class="bi bi-plus-circle"></i> Nouveau créneau
        </button>
      </div>
    </div>

    <!-- Filtres -->
    <div class="filters-section">
      <div class="row g-3 align-items-end">
        <div class="col-md-4">
          <label for="classeFiltre" class="form-label fw-medium">
            <i class="bi bi-funnel me-1"></i>Filtrer par classe
          </label>
          <select id="classeFiltre" class="form-select">
            <option value="">-- Toutes les classes --</option>
          </select>
        </div>
        <div class="col-md-4">
          <label for="jourFiltre" class="form-label fw-medium">
            <i class="bi bi-calendar-day me-1"></i>Filtrer par jour
          </label>
          <select id="jourFiltre" class="form-select">
            <option value="">-- Tous les jours --</option>
            <option value="Lundi">Lundi</option>
            <option value="Mardi">Mardi</option>
            <option value="Mercredi">Mercredi</option>
            <option value="Jeudi">Jeudi</option>
            <option value="Vendredi">Vendredi</option>
            <option value="Samedi">Samedi</option>
          </select>
        </div>
        <div class="col-md-4">
          <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
            <i class="bi bi-x-circle"></i> Effacer les filtres
          </button>
        </div>
      </div>
    </div>

    <!-- Table des emplois du temps -->
    <div class="table-box">
      <table class="table table-hover planning-table">
        <thead>
          <tr>
            <th style="width: 15%">Jour</th>
            <th style="width: 20%">Matière</th>
            <th style="width: 12%">Début</th>
            <th style="width: 12%">Fin</th>
            <th style="width: 15%">Salle</th>
            <th style="width: 15%">Classe</th>
            <th style="width: 11%" class="no-print">Actions</th>
          </tr>
        </thead>
        <tbody id="planningBody">
          <tr>
            <td colspan="7" class="text-center text-muted p-4">
              <i class="bi bi-calendar-x fs-1"></i>
              <p class="mt-2">Sélectionnez une classe pour voir son emploi du temps</p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Modal d'ajout/modification de créneau -->
  <div class="modal fade" id="addModal" tabindex="-1" data-mode="add">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-plus-circle me-2"></i>
            Ajouter un créneau
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div id="addError" class="d-none"></div>

          <form id="addForm">
            <div class="row g-3">
              <div class="col-md-6">
                <label for="classeSelect" class="form-label">
                  <i class="bi bi-collection me-1"></i>Classe *
                </label>
                <select id="classeSelect" name="classeSelect" class="form-select" required>
                  <option value="">-- Sélectionner une classe --</option>
                </select>
              </div>

              <div class="col-md-6">
                <label for="matiereSelect" class="form-label">
                  <i class="bi bi-book me-1"></i>Matière *
                </label>
                <select id="matiereSelect" name="matiereSelect" class="form-select" required>
                  <option value="">-- Sélectionner une matière --</option>
                </select>
              </div>

              <div class="col-md-4">
                <label for="jourSelect" class="form-label">
                  <i class="bi bi-calendar-day me-1"></i>Jour *
                </label>
                <select id="jourSelect" name="jourSelect" class="form-select" required>
                  <option value="">-- Sélectionner un jour --</option>
                  <option value="Lundi">Lundi</option>
                  <option value="Mardi">Mardi</option>
                  <option value="Mercredi">Mercredi</option>
                  <option value="Jeudi">Jeudi</option>
                  <option value="Vendredi">Vendredi</option>
                  <option value="Samedi">Samedi</option>
                </select>
              </div>

              <div class="col-md-4">
                <label for="heureDebut" class="form-label">
                  <i class="bi bi-clock me-1"></i>Heure début *
                </label>
                <input type="time" id="heureDebut" name="heureDebut" class="form-control" required>
              </div>

              <div class="col-md-4">
                <label for="heureFin" class="form-label">
                  <i class="bi bi-clock-fill me-1"></i>Heure fin *
                </label>
                <input type="time" id="heureFin" name="heureFin" class="form-control" required>
              </div>

              <div class="col-12">
                <label for="salle_id" class="form-label">
                  <i class="bi bi-geo-alt me-1"></i>Salle *
                </label>
                <select id="salle_id" name="salle_id" class="form-select" required>
                  <option value="">Sélectionner une salle...</option>
                  <!-- Options will be loaded dynamically -->
                </select>
                <div class="form-text">
                  <small class="text-muted">
                    <i class="bi bi-info-circle me-1"></i>
                    Les salles sont organisées par type et affichent la capacité
                  </small>
                </div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle"></i> Annuler
          </button>
          <button type="submit" form="addForm" class="btn btn-primary">
            <i class="bi bi-check-circle"></i> Ajouter
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/emploi-temps.js"></script>

  <script>
    // Fonction pour effacer les filtres
    function clearFilters() {
      document.getElementById('classeFiltre').value = '';
      document.getElementById('jourFiltre').value = '';
      if (window.emploiTempsManager) {
        window.emploiTempsManager.clearPlanning();
      }
    }

    // Gestion du filtre par jour
    document.getElementById('jourFiltre').addEventListener('change', function() {
      if (window.emploiTempsManager && window.emploiTempsManager.timetableData) {
        window.emploiTempsManager.renderPlanning();
      }
    });

    // Reset du modal à la fermeture
    document.getElementById('addModal').addEventListener('hidden.bs.modal', function() {
      if (window.emploiTempsManager) {
        window.emploiTempsManager.resetForm();
      }
    });
  </script>
</body>
</html>
