<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Messagerie Administrateur - NS School Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
    /* Admin-specific message styles */
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
      height: calc(100vh - 60px);
      overflow: hidden;
    }

    .message-page-header {
      background: linear-gradient(135deg, #0d6efd, #0b5ed7);
      color: white;
      padding: 1.5rem;
      border-radius: 12px;
      margin-bottom: 1.5rem;
      box-shadow: 0 4px 20px rgba(13, 110, 253, 0.3);
    }

    .message-page-header h2 {
      margin: 0;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .message-stats {
      display: flex;
      gap: 1rem;
      margin-top: 1rem;
    }

    .stat-item {
      background: rgba(255, 255, 255, 0.2);
      padding: 0.75rem 1rem;
      border-radius: 8px;
      text-align: center;
      flex: 1;
    }

    .stat-number {
      font-size: 1.5rem;
      font-weight: bold;
      display: block;
    }

    .stat-label {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'message' }) %>

<div class="main-content">
  <!-- Page Header with Stats -->
  <div class="message-page-header">
    <h2>
      <i class="bi bi-chat-dots"></i>
      Messagerie Administrateur
    </h2>
    <div class="message-stats">
      <div class="stat-item">
        <span class="stat-number"><%= messages.filter(m => !m.lu).length %></span>
        <span class="stat-label">Non lus</span>
      </div>
      <div class="stat-item">
        <span class="stat-number"><%= messages.filter(m => m.type === 'privé').length %></span>
        <span class="stat-label">Messages privés</span>
      </div>
      <div class="stat-item">
        <span class="stat-number"><%= messages.filter(m => m.type === 'annonce').length %></span>
        <span class="stat-label">Annonces envoyées</span>
      </div>
      <div class="stat-item">
        <span class="stat-number"><%= profs.length %></span>
        <span class="stat-label">Professeurs</span>
      </div>
    </div>
  </div>

  <!-- Professional Message Interface -->
  <%- include('../partials/message-layout', {
    userRole: 'admin',
    currentUser: { matricule: matricule, role: role },
    conversations: locals.conversations || [],
    messages: messages,
    availableRecipients: profs.map(p => ({ matricule: p.matricule, role: 'professeur' })),
    classes: classes || []
  }) %>
</div>
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Admin Message Page Initialization
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 Admin Message Page Loaded');

      // Initialize tooltips
      const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
      });

      // Load conversations data for admin
      loadAdminConversations();

      // Set up periodic refresh for real-time updates
      setInterval(loadAdminConversations, 30000); // Refresh every 30 seconds
    });

    function loadAdminConversations() {
      fetch('/api/message/admin/conversations')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            updateConversationData(data.conversations);
            updateMessageStats(data.stats);
          }
        })
        .catch(error => {
          console.error('Error loading admin conversations:', error);
        });
    }

    function updateConversationData(conversations) {
      // Update the global conversations data
      if (typeof allConversations !== 'undefined') {
        allConversations = conversations;
        filteredConversations = [...conversations];
        updateConversationList();
      }
    }

    function updateMessageStats(stats) {
      // Update the stats in the header
      const statItems = document.querySelectorAll('.stat-number');
      if (statItems.length >= 4 && stats) {
        statItems[0].textContent = stats.unread || 0;
        statItems[1].textContent = stats.private || 0;
        statItems[2].textContent = stats.announcements || 0;
        statItems[3].textContent = stats.professors || 0;
      }
    }

    // Admin-specific message functions
    function broadcastToAllClasses() {
      // Open compose modal with announcement type pre-selected
      const modal = new bootstrap.Modal(document.getElementById('composeModal'));
      const typeSelect = document.getElementById('messageType');
      if (typeSelect) {
        typeSelect.value = 'annonce';
        typeSelect.dispatchEvent(new Event('change'));
      }
      modal.show();
    }

    function sendUrgentMessage() {
      // Open compose modal with urgent priority
      const modal = new bootstrap.Modal(document.getElementById('composeModal'));
      const prioritySelect = document.getElementById('messagePriority');
      if (prioritySelect) {
        prioritySelect.value = 'urgent';
      }
      modal.show();
    }

    // Export admin functions to global scope
    window.broadcastToAllClasses = broadcastToAllClasses;
    window.sendUrgentMessage = sendUrgentMessage;
  </script>
</body>
</html>
