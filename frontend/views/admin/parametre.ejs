<!-- frontend/views/admin/parametre.ejs -->
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Paramètres - NS School</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <%- include('../partials/admin-styles') %>

  <style>
    /* Page-specific styles for parametre */

    .card-stat {
      display: flex;
      align-items: center;
      padding: 20px;
      background-color: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }

    .card-stat .icon {
      font-size: 2.2rem;
      margin-right: 15px;
    }

    .icon-eleve {
      color: #0d6efd;
    }
    .icon-prof {
      color: #20c997;
    }
    .icon-classe {
      color: #ffc107;
    }
    .icon-absence {
      color: #dc3545;
    }

    .table-box {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    .initial-circle {
      color: white;
      font-weight: bold;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.9rem;
    }

    .role-text {
      font-size: 0.75rem;
      color: #6c757d;
      text-transform: lowercase;
    }

    .color-red { background-color: #dc3545; }
    .color-blue { background-color: #0d6efd; }
    .color-green { background-color: #20c997; }
    .color-orange { background-color: #fd7e14; }
    
      @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
  </style>
</head>
<body class="bg-light">
  <%- include('../partials/admin-topbar') %>
  <%- include('../partials/sidebar', { currentPage: 'parametre' }) %>
  
  <div class="main-content mt-4">
    <h2 class="mb-4">⚙ Paramètres du système</h2>

    <form id="formParametres">
      <div class="row mb-3">
        <div class="col-md-4">
          <label for="annee_scolaire" class="form-label">Année scolaire</label>
          <input type="text" class="form-control" id="annee_scolaire" required>
        </div>
        <div class="col-md-4">
          <label for="nb_trimestres" class="form-label">Nombre de trimestres</label>
          <input type="number" class="form-control" id="nb_trimestres" required>
        </div>
        <div class="col-md-4">
          <label for="theme" class="form-label">Thème</label>
          <select id="theme" class="form-select">
            <option value="clair">Clair</option>
            <option value="sombre">Sombre</option>
          </select>
        </div>
      </div>

      <div class="form-check form-switch mb-3">
        <input class="form-check-input" type="checkbox" id="messagerie_active">
        <label class="form-check-label" for="messagerie_active">Activer la messagerie</label>
      </div>

      <button type="submit" class="btn btn-primary">Enregistrer</button>
    </form>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    async function chargerParametres() {
      const res = await fetch('/api/parametre');
      const data = await res.json();
      document.getElementById('annee_scolaire').value = data.annee_scolaire || '';
      document.getElementById('nb_trimestres').value = data.nb_trimestres || 3;
      document.getElementById('theme').value = data.theme || 'clair';
      document.getElementById('messagerie_active').checked = data.messagerie_active === 1;
    }

    document.getElementById('formParametres').addEventListener('submit', async e => {
      e.preventDefault();
      const data = {
        annee_scolaire: document.getElementById('annee_scolaire').value,
        nb_trimestres: parseInt(document.getElementById('nb_trimestres').value),
        theme: document.getElementById('theme').value,
        messagerie_active: document.getElementById('messagerie_active').checked ? 1 : 0
      };

      const res = await fetch('/api/parametre', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (res.ok) alert('Paramètres enregistrés');
    });

    chargerParametres();
  </script>
</body>
</html>
