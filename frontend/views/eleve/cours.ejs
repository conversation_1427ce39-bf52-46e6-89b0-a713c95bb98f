<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title><%= titre %></title>
 <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>

  <style>
           @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    /* Page-specific styles - navigation styles now handled by dashboard-styles partial */

    
    
  </style>
</head>
<body>

  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'cours'; %>
  <%- include('../partials/eleve-sidebar') %>
  <div class="dashboard-main">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="bi bi-book me-2"></i>Cours disponibles</h2>
      <button class="btn btn-outline-primary" onclick="refreshCours()">
        <i class="bi bi-arrow-clockwise me-2"></i>Actualiser
      </button>
    </div>

    <!-- Alert pour les messages -->
    <div id="alertContainer"></div>

    <!-- Informations de classe -->
    <div class="card mb-4" id="classeInfo" style="display: none;">
      <div class="card-body">
        <h6 class="card-title"><i class="bi bi-building me-2"></i>Ma classe</h6>
        <p class="card-text mb-0" id="classeNom">-</p>
      </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="input-group">
          <span class="input-group-text"><i class="bi bi-search"></i></span>
          <input type="text" id="searchInput" class="form-control" placeholder="Rechercher un cours...">
        </div>
      </div>
      <div class="col-md-3">
        <select id="filterMatiere" class="form-select">
          <option value="">Toutes les matières</option>
        </select>
      </div>
      <div class="col-md-3">
        <select id="sortBy" class="form-select">
          <option value="date_desc">Plus récent</option>
          <option value="date_asc">Plus ancien</option>
          <option value="titre_asc">Titre A-Z</option>
          <option value="titre_desc">Titre Z-A</option>
          <option value="matiere_asc">Matière A-Z</option>
        </select>
      </div>
    </div>

    <!-- Liste des cours -->
    <div id="coursContainer">
      <div class="row" id="coursGrid">
        <!-- Les cours seront chargés dynamiquement -->
      </div>

      <div id="loadingSpinner" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-2 text-muted">Chargement des cours...</p>
      </div>

      <div id="noResults" class="text-center py-5" style="display: none;">
        <i class="bi bi-book fs-1 text-muted"></i>
        <h5 class="mt-3 text-muted">Aucun cours trouvé</h5>
        <p class="text-muted">Aucun cours ne correspond à vos critères de recherche.</p>
      </div>

      <div id="noCours" class="text-center py-5" style="display: none;">
        <i class="bi bi-book fs-1 text-muted"></i>
        <h5 class="mt-3 text-muted">Aucun cours disponible</h5>
        <p class="text-muted">Aucun cours n'est encore disponible pour votre classe.</p>
      </div>
    </div>

    <!-- Modal Info Cours -->
    <div class="modal fade" id="infoModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title"><i class="bi bi-info-circle me-2"></i>Informations du cours</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body" id="infoModalBody">
            <!-- Contenu dynamique -->
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            <button type="button" class="btn btn-primary" id="downloadFromModal" onclick="downloadFromModal()">
              <i class="bi bi-download me-2"></i>Télécharger
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    // Variables globales
    let allCours = [];
    let filteredCours = [];
    let currentCoursForModal = null;

    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
      loadCours();
      setupEventListeners();
    });

    // Charger les cours
    function loadCours() {
      document.getElementById('loadingSpinner').style.display = 'block';
      document.getElementById('coursGrid').innerHTML = '';

      fetch('/eleve/cours/api')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            allCours = data.data.cours;
            filteredCours = [...allCours];

            // Afficher les informations de classe
            document.getElementById('classeNom').textContent = data.data.classe;
            document.getElementById('classeInfo').style.display = 'block';

            // Remplir le filtre des matières
            populateMatiereFilter();

            // Afficher les cours
            displayCours();
          } else {
            showAlert('danger', data.message);
          }
        })
        .catch(error => {
          console.error('Erreur chargement cours:', error);
          showAlert('danger', 'Erreur lors du chargement des cours');
        })
        .finally(() => {
          document.getElementById('loadingSpinner').style.display = 'none';
        });
    }

    // Remplir le filtre des matières
    function populateMatiereFilter() {
      const filterSelect = document.getElementById('filterMatiere');
      const matieres = [...new Set(allCours.map(c => c.matiere_nom))].sort();

      filterSelect.innerHTML = '<option value="">Toutes les matières</option>';
      matieres.forEach(matiere => {
        const option = document.createElement('option');
        option.value = matiere;
        option.textContent = matiere;
        filterSelect.appendChild(option);
      });
    }

    // Afficher les cours
    function displayCours() {
      const container = document.getElementById('coursGrid');
      const noResults = document.getElementById('noResults');
      const noCours = document.getElementById('noCours');

      container.innerHTML = '';

      if (allCours.length === 0) {
        noCours.style.display = 'block';
        noResults.style.display = 'none';
        return;
      }

      if (filteredCours.length === 0) {
        noResults.style.display = 'block';
        noCours.style.display = 'none';
        return;
      }

      noResults.style.display = 'none';
      noCours.style.display = 'none';

      filteredCours.forEach(cours => {
        const coursCard = createCoursCard(cours);
        container.appendChild(coursCard);
      });
    }

    // Créer une carte de cours
    function createCoursCard(cours) {
      const col = document.createElement('div');
      col.className = 'col-md-6 col-lg-4 mb-4';

      const fileIcon = getFileIcon(cours.file_extension);
      const fileSize = cours.fichier ? 'Taille inconnue' : 'Fichier manquant';

      col.innerHTML = `
        <div class="card h-100 shadow-sm">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-start mb-2">
              <h6 class="card-title mb-0">${cours.titre}</h6>
              <span class="badge bg-primary">${cours.matiere_nom}</span>
            </div>

            <p class="card-text text-muted small mb-2">
              <i class="bi bi-person me-1"></i>${cours.professeur_complet}
            </p>

            <p class="card-text text-muted small mb-3">
              <i class="bi bi-calendar me-1"></i>${new Date(cours.date_ajout).toLocaleDateString('fr-FR')}
            </p>

            <div class="d-flex align-items-center mb-3">
              <i class="${fileIcon} me-2 fs-5"></i>
              <div class="flex-grow-1">
                <div class="small text-truncate">${cours.original_filename}</div>
                <div class="text-muted" style="font-size: 0.75rem;">${fileSize}</div>
              </div>
            </div>
          </div>

          <div class="card-footer bg-transparent">
            <div class="d-flex gap-2">
              <button class="btn btn-primary btn-sm flex-grow-1" onclick="downloadCours(${cours.cours_id})">
                <i class="bi bi-download me-1"></i>Télécharger
              </button>
              <button class="btn btn-outline-info btn-sm" onclick="viewCoursInfo(${cours.cours_id})" title="Informations">
                <i class="bi bi-info-circle"></i>
              </button>
            </div>
          </div>
        </div>
      `;

      return col;
    }

    // Obtenir l'icône selon l'extension du fichier
    function getFileIcon(extension) {
      const icons = {
        '.pdf': 'bi bi-file-earmark-pdf text-danger',
        '.doc': 'bi bi-file-earmark-word text-primary',
        '.docx': 'bi bi-file-earmark-word text-primary',
        '.ppt': 'bi bi-file-earmark-ppt text-warning',
        '.pptx': 'bi bi-file-earmark-ppt text-warning',
        '.xls': 'bi bi-file-earmark-excel text-success',
        '.xlsx': 'bi bi-file-earmark-excel text-success',
        '.txt': 'bi bi-file-earmark-text text-secondary',
        '.jpg': 'bi bi-file-earmark-image text-info',
        '.jpeg': 'bi bi-file-earmark-image text-info',
        '.png': 'bi bi-file-earmark-image text-info',
        '.zip': 'bi bi-file-earmark-zip text-dark'
      };

      return icons[extension] || 'bi bi-file-earmark text-muted';
    }

    // Filtrer et trier les cours
    function filterAndSortCours() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const matiereFilter = document.getElementById('filterMatiere').value;
      const sortBy = document.getElementById('sortBy').value;

      // Filtrer
      filteredCours = allCours.filter(cours => {
        const matchesSearch = cours.titre.toLowerCase().includes(searchTerm) ||
                            cours.matiere_nom.toLowerCase().includes(searchTerm) ||
                            cours.professeur_complet.toLowerCase().includes(searchTerm);

        const matchesMatiere = !matiereFilter || cours.matiere_nom === matiereFilter;

        return matchesSearch && matchesMatiere;
      });

      // Trier
      filteredCours.sort((a, b) => {
        switch (sortBy) {
          case 'date_asc':
            return new Date(a.date_ajout) - new Date(b.date_ajout);
          case 'date_desc':
            return new Date(b.date_ajout) - new Date(a.date_ajout);
          case 'titre_asc':
            return a.titre.localeCompare(b.titre);
          case 'titre_desc':
            return b.titre.localeCompare(a.titre);
          case 'matiere_asc':
            return a.matiere_nom.localeCompare(b.matiere_nom);
          default:
            return new Date(b.date_ajout) - new Date(a.date_ajout);
        }
      });

      displayCours();
    }

    // Télécharger un cours
    function downloadCours(coursId) {
      // Utiliser le bon endpoint backend
      window.open(`/eleve/cours/download/${coursId}`, '_blank');
    }

    // Voir les informations d'un cours
    function viewCoursInfo(coursId) {
      fetch(`/api/download/cours/${coursId}/info`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            const cours = data.data;
            currentCoursForModal = coursId;

            document.getElementById('infoModalBody').innerHTML = `
              <div class="row">
                <div class="col-sm-4"><strong>Titre:</strong></div>
                <div class="col-sm-8">${cours.titre}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Matière:</strong></div>
                <div class="col-sm-8">${cours.matiere}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Professeur:</strong></div>
                <div class="col-sm-8">${cours.professeur}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Classe:</strong></div>
                <div class="col-sm-8">${cours.classe}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Fichier:</strong></div>
                <div class="col-sm-8">${cours.fichier.nom_original}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Taille:</strong></div>
                <div class="col-sm-8">${cours.fichier.taille}</div>
              </div>
              <div class="row mt-2">
                <div class="col-sm-4"><strong>Date d'ajout:</strong></div>
                <div class="col-sm-8">${new Date(cours.date_ajout).toLocaleDateString('fr-FR')}</div>
              </div>
            `;

            new bootstrap.Modal(document.getElementById('infoModal')).show();
          } else {
            showAlert('danger', data.message);
          }
        })
        .catch(error => {
          console.error('Erreur:', error);
          showAlert('danger', 'Erreur lors de la récupération des informations');
        });
    }

    // Télécharger depuis le modal
    function downloadFromModal() {
      if (currentCoursForModal) {
        downloadCours(currentCoursForModal);
        bootstrap.Modal.getInstance(document.getElementById('infoModal')).hide();
      }
    }

    // Actualiser les cours
    function refreshCours() {
      loadCours();
    }

    // Configuration des écouteurs d'événements
    function setupEventListeners() {
      document.getElementById('searchInput').addEventListener('input', filterAndSortCours);
      document.getElementById('filterMatiere').addEventListener('change', filterAndSortCours);
      document.getElementById('sortBy').addEventListener('change', filterAndSortCours);
    }

    // Fonction utilitaire pour afficher les alertes
    function showAlert(type, message) {
      const alertContainer = document.getElementById('alertContainer');
      const alert = document.createElement('div');
      alert.className = `alert alert-${type} alert-dismissible fade show`;
      alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

      alertContainer.innerHTML = '';
      alertContainer.appendChild(alert);

      setTimeout(() => {
        if (alert.parentNode) {
          alert.remove();
        }
      }, 5000);
    }
  </script>
</body>
</html>
