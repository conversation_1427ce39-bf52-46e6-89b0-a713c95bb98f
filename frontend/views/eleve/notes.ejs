<!DOCTYPE html>
<html lang="fr">
<head>

  <meta charset="UTF-8">
  <title>Mes Notes</title>

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>

  <style>
    .planning-table {
      table-layout: fixed;
      width: 100%;
    }
    .planning-table th, .planning-table td {
      text-align: center;
      vertical-align: middle;
    }
    @media print {
      .no-print {
        display: none;
      }
    }

    /* Page-specific styles - navigation styles now handled by dashboard-styles partial */

  </style>
</head>
  <body>

  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'notes'; %>
  <%- include('../partials/eleve-sidebar') %>

  <div class="dashboard-main">
    <h2 class="mb-4">Mes Notes</h2>
    <table class="table table-bordered table-striped">
      <thead class="table-dark">
        <tr>
          <th>Matière</th>
          <th>Note</th>
          <th>Type</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
        <% if (notes.length === 0) { %>
          <tr><td colspan="4" class="text-center">Aucune note trouvée</td></tr>
        <% } else { %>
          <% notes.forEach(note => { %>
            <tr>
              <td><%= note.matiere %></td>
              <td><%= note.note %></td>
              <td><%= note.type_evaluation || 'N/A' %></td>
              <td><%= note.date_evaluation ? new Date(note.date_evaluation).toISOString().split('T')[0] : 'N/A' %></td>
            </tr>
          <% }) %>
        <% } %>
      </tbody>
    </table>

    <% if (moyenne) { %>
      <div class="alert alert-info mt-3">
        Moyenne générale : <strong><%= moyenne %>/20</strong>
      </div>
    <% } %>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
