<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Emploi du temps</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>

  <style>
    /* Timetable grid */
    .tt-container {
      display: grid;
      grid-template-columns: 120px repeat(5, 1fr);
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      overflow: auto;
      background: #fff;
    }
    .tt-head, .tt-row { display: contents; }
    .tt-cell {
      border-bottom: 1px solid #f1f5f9;
      border-right: 1px solid #f1f5f9;
      padding: 10px 12px;
      background: #fff;
    }
    .tt-cell.head {
      background: #f8fafc;
      font-weight: 600;
      color: #111827;
    }
    .tt-time.sticky {
      position: sticky;
      left: 0;
      z-index: 2;
      background: #f8fafc;
    }
    .tt-day.head.sticky {
      position: sticky;
      top: 0;
      z-index: 3;
    }

    /* Session cards */
    .session {
      background: #f9fafb;
      border: 1px solid #eef2f7;
      border-left: 4px solid var(--color, #4f46e5);
      border-radius: 10px;
      padding: 8px 10px;
      margin-bottom: 8px;
      box-shadow: 0 1px 2px rgba(16, 24, 40, 0.06);
    }
    .session-title {
      font-weight: 600;
      font-size: 0.95rem;
      color: #111827;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .session-meta {
      margin-top: 2px;
      font-size: 0.82rem;
      color: #374151;
      display: flex;
      gap: 6px;
      align-items: center;
      flex-wrap: wrap;
    }
    .chip {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      border-radius: 999px;
      padding: 2px 8px;
      font-size: 0.75rem;
      line-height: 1.2;
      border: 1px solid #e5e7eb;
      color: #374151;
      background: #f3f4f6;
    }
    .chip.room { background: #eef2ff; color: #3730a3; border-color: #e0e7ff; }
    .chip.teacher { background: #ecfeff; color: #164e63; border-color: #cffafe; }
    .chip.time { background: #fef3c7; color: #92400e; border-color: #fde68a; }
    .session-note {
      margin-top: 4px;
      font-size: 0.78rem;
      color: #6b7280;
    }
    /* Empty placeholder styling (no curved colored strip) */
    .session.empty {
      opacity: .28;
      min-height: 28px;
      background: transparent;
      border: 1px dashed #e5e7eb;
      border-left: 1px dashed #e5e7eb;
      box-shadow: none;
      border-radius: 8px;
    }
    .tt-cell:hover .session.empty {
      border-color: #cbd5e1;
      opacity: .4;
    }

    /* Header and toolbar */
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      gap: 12px;
      flex-wrap: wrap;
    }
    .week-switcher { display: flex; gap: 8px; align-items: center; }
    .week-label { font-weight: 600; }
    .btn-plain { padding: 8px 12px; border-radius: 8px; border: 1px solid #e5e7eb; background: #fff; cursor: pointer; }
    .btn-dark { background: #111827; color: #fff; border: 1px solid #111827; }
    .subtle { color: #6b7280; }

    /* Empty state */
    .empty-state {
      border: 2px dashed #e5e7eb;
      border-radius: 12px;
      padding: 32px;
      text-align: center;
      color: #6b7280;
      background: #fafafa;
    }
    .empty-title { font-weight: 600; color: #111827; margin-top: 8px; }
    .empty-subtitle { font-size: .95rem; margin-top: 2px; }

    /* Responsive */
    @media (max-width: 900px) {
      .tt-container { grid-template-columns: 90px repeat(5, minmax(140px, 1fr)); }
    }
    @media (max-width: 640px) {
      .tt-container { display: block; }
      .tt-head { display: none; }
      .tt-row {
        display: grid;
        grid-template-columns: 90px 1fr;
      }
      .tt-cell { border-right: none; }
      .tt-time.sticky { position: static; background: #f8fafc; }
    }

    /* Print */
    @media print {
      .no-print, .sidebar, .topbar, .page-header { display: none !important; }
      .tt-container { border: none; }
      .session { break-inside: avoid; }
      body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
    }
  </style>
</head>
<body>
  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'emploi-temps'; %>
  <%- include('../partials/eleve-sidebar') %>

  <div class="dashboard-main">
    <div class="page-header">
      <div>
        <h2 class="mb-0">Mon emploi du temps <span class="subtle">— <%= nomClasse %></span></h2>
        <% if (typeof weekLabel !== 'undefined') { %>
          <div class="subtle"><i class="bi bi-calendar-week"></i> <span id="weekLabel"><%= weekLabel %></span></div>
        <% } %>
      </div>
      <div class="d-flex align-items-center gap-2 no-print">
        <button class="btn-plain btn-dark" id="btnPrint"><i class="bi bi-printer"></i> Imprimer</button>
      </div>
    </div>

    <%
      // Build a simple map day->list of sessions and extract distinct days and sorted time slots
      const daysOrder = ['Lundi','Mardi','Mercredi','Jeudi','Vendredi','Samedi','Dimanche'];
      const emploiData = Array.isArray(emploi) ? emploi : [];
      const byDay = {};
      const timeSet = new Set();
      emploiData.forEach(it => {
        const d = it.jour_semaine || '';
        if (!byDay[d]) byDay[d] = [];
        byDay[d].push(it);
        timeSet.add((it.heure_debut||'') + '–' + (it.heure_fin||''));
      });
      // Always show Mon -> Sat columns, even if empty; change to slice(0,5) for Mon->Fri
      const weekdays = daysOrder.slice(0, 6);
      // Robust time sorting using HH:mm values
      function parseHM(v){ const [h,m]=String(v||'00:00').split(':').map(Number); return (h*60)+(m||0); }
      const timeSlots = Array.from(timeSet).filter(Boolean).sort((a,b) => {
        const [aS] = a.split('–'); const [bS] = b.split('–');
        return parseHM(aS) - parseHM(bS);
      });
      // utility to pick a consistent color per subject
      function colorFor(subject) {
        const palette = ['#4f46e5','#059669','#db2777','#2563eb','#f59e0b','#0ea5e9','#10b981'];
        if (!subject) return palette[0];
        let sum = 0; for (let i=0;i<subject.length;i++) sum += subject.charCodeAt(i);
        return palette[sum % palette.length];
      }
    %>

    <% if (emploiData.length === 0) { %>
      <div class="empty-state">
        <div style="font-size: 42px;">📅</div>
        <div class="empty-title">Aucun cours prévu</div>
        <div class="empty-subtitle">L’emploi du temps pour cette période n’est pas disponible.</div>
      </div>
    <% } else { %>
      <div class="tt-container" role="table" aria-label="Emploi du temps">
        <!-- Head -->
        <div class="tt-head" role="rowgroup">
          <div class="tt-cell tt-time head sticky" role="columnheader" aria-label="Heures">Heures</div>
          <% weekdays.forEach(function(day){ %>
            <div class="tt-cell tt-day head sticky" role="columnheader"><%= day %></div>
          <% }); %>
        </div>

        <!-- Body rows per time slot -->
        <% timeSlots.forEach(function(slot){ %>
          <div class="tt-row" role="row">
            <div class="tt-cell tt-time sticky" role="rowheader"><%= slot %></div>
            <% weekdays.forEach(function(day){
                 const sessions = (byDay[day]||[]).filter(s => ((s.heure_debut||'') + '–' + (s.heure_fin||'')) === slot);
            %>
              <div class="tt-cell" role="cell">
                <% if (!sessions || sessions.length === 0) { %>
                  <div class="session empty"></div>
                <% } else { sessions.forEach(function(s){
                      const subject = s.nom_matiere || s.matiere || 'Cours';
                      const teacher = s.nom_prof || s.prof || s.enseignant || '';
                      const room = s.salle || s.salle_nom || '';
                      const note = s.note || '';
                      const color = colorFor(subject);
                %>
                  <div class="session" style="--color:<%= color %>;">
                    <div class="session-title" title="<%= subject %>"><%= subject %></div>
                    <div class="session-meta">
                      <% if (room) { %><span class="chip room" title="Salle"><i class="bi bi-door-closed"></i> <%= room %></span><% } %>
                      <% if (teacher) { %><span class="chip teacher" title="Professeur"><i class="bi bi-person-badge"></i> <%= teacher %></span><% } %>
                      <span class="chip time" title="Horaire"><i class="bi bi-clock"></i> <%= s.heure_debut %>–<%= s.heure_fin %></span>
                    </div>
                    <% if (note) { %><div class="session-note"><%= note %></div><% } %>
                  </div>
                <% }); } %>
              </div>
            <% }); %>
          </div>
        <% }); %>
      </div>
    <% } %>

    <div class="mt-4 d-flex gap-2 no-print">
      <a href="/dashboard-eleve" class="btn btn-secondary">← Retour au tableau de bord</a>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Print only
    (function(){
      const printBtn = document.getElementById('btnPrint');
      if (printBtn) printBtn.addEventListener('click', () => window.print());
    })();
  </script>
</body>
</html>
