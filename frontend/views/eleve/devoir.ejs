<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Devoirs à faire</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>

  <style>
           @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    /* Page-specific styles - navigation styles now handled by dashboard-styles partial */


    
    
  </style>
</head>
<body>

  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'devoirs'; %>
  <%- include('../partials/eleve-sidebar') %>
  <div class="dashboard-main">
    <h2 class="mb-4">📚 Devoirs à faire</h2>

  <% if (typeof errorMsg !== 'undefined' && errorMsg) { %>
    <div class="alert alert-danger"><%= errorMsg %></div>
  <% } %>

  <% if (devoirs.length === 0) { %>
    <div class="alert alert-info">Aucun devoir disponible pour l’instant.</div>
  <% } else { %>
    <% devoirs.forEach(devoir => { %>
      <div class="card mb-3">
        <div class="card-body">
          <h5 class="card-title"><%= devoir.titre %></h5>
          <p class="card-text"><%= devoir.description %></p>
          <p>Matière : <strong><%= devoir.matiere %></strong></p>
          <p>Professeur : <%= devoir.prof_nom %> <%= devoir.prof_prenom ? ('(' + devoir.prof_prenom + ')') : '' %></p>
<p>Date limite : <%= devoir.date_limite && devoir.date_limite.toISOString ? devoir.date_limite.toISOString().slice(0,10) : (devoir.date_limite ? devoir.date_limite : 'Non spécifiée') %></p>

<% if (devoir.fichier) { %>
  <div class="alert alert-success">
    ✅ Devoir déjà soumis le <%= devoir.date_soumission ? new Date(devoir.date_soumission).toLocaleString('fr-FR') : '' %>.
    <br/>
    <a href="/uploads/soumissions/<%= devoir.fichier %>" target="_blank">Voir le fichier soumis</a>
    <% if (devoir.note !== null) { %><br>Note : <%= devoir.note %> <% } %>
    <% if (devoir.remarque) { %><br>Remarque : <%= devoir.remarque %> <% } %>
  </div>
<% } else { %>
  <form action="/eleve/devoirs/soumettre/<%= devoir.devoir_id %>" method="POST" enctype="multipart/form-data">
    <div class="mb-2">
      <input type="file" name="fichier" required class="form-control">
    </div>
    <button type="submit" class="btn btn-success">📤 Soumettre</button>
  </form>
<% } %>
        </div>
      </div>
    <% }) %>
  <% } %>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
