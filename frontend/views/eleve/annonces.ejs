<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Annonces pour la classe <%= nomClasse %></title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>

  <style>
           @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    /* Page-specific styles - navigation styles now handled by dashboard-styles partial */


    
    
  </style>
</head>
<body>

  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'annonces'; %>
  <%- include('../partials/eleve-sidebar') %>


<div class="dashboard-main">
  <h2 class="mb-4 text-center">📢 Annonces pour la classe <%= nomClasse %></h2>

  <% if (annonces.length === 0) { %>
    <div class="alert alert-info">Aucune annonce pour le moment.</div>
  <% } else { %>
    <% annonces.forEach(annonce => { %>
      <div class="card mb-3 shadow-sm">
        <div class="card-header">
          <strong><%= annonce.expediteur_role %></strong> — <%= new Date(annonce.date_envoi).toLocaleString('fr-FR') %>
        </div>
        <div class="card-body">
          <p class="card-text"><%= annonce.contenu %></p>
        </div>
      </div>
    <% }); %>
  <% } %>
</div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
