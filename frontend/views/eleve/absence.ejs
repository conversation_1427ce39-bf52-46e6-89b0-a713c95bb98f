<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Mes absences</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
           @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }
    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }
    .table-box {
      background-color: white; border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    .initial-circle {
      color: white; font-weight: bold; border-radius: 50%;
      width: 35px; height: 35px; display: inline-flex;
      align-items: center; justify-content: center; font-size: 0.9rem;
    }
    .role-text { font-size: 0.75rem; color: #6c757d; text-transform: lowercase; }

    
    
  </style>
</head>
<body>

  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'absences'; %>
  <%- include('../partials/eleve-sidebar') %>

<div class="main-content">
  <h2 class="mb-4">Mes absences</h2>

  <% if (absences.length === 0) { %>
    <div class="alert alert-info">Aucune absence enregistrée.</div>
  <% } else { %>
    <table class="table table-striped">
      <thead class="table-dark">
        <tr>
          <th>Date</th>
          <th>Motif</th>
          <th>Justifiée</th>
        </tr>
      </thead>
      <tbody>
        <% absences.forEach(abs => { %>
          <tr>
            <td>
              <% if (abs.date_absence) { %>
                <%= (abs.date_absence instanceof Date ? abs.date_absence.toISOString().split('T')[0] : String(abs.date_absence).slice(0, 10)) %>
              <% } else { %>
                --
              <% } %>
            </td>
            <td><%= abs.motif %></td>
            <td>
              <% if (abs.justifiee) { %>
                <span class="badge bg-success">Oui</span>
              <% } else { %>
                <span class="badge bg-danger">Non</span>
              <% } %>
            </td>
          </tr>
        <% }) %>
      </tbody>
    </table>
  <% } %>

  <a href="/dashboard-eleve" class="btn btn-secondary mt-3">Retour au tableau de bord</a>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
