<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Mon Bulletin</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Dashboard Styles -->
  <%- include('../partials/dashboard-styles') %>

  <style>
           @media print {
      .btn, .navbar, .sidebar, .no-print, .modal {
        display: none !important;
      }
      body {
        background: white;
      }
    }
    /* Page-specific styles - navigation styles now handled by dashboard-styles partial */


    
    
  </style>
</head>
<body>

  <!-- Student Topbar -->
  <%- include('../partials/eleve-topbar') %>

  <!-- Student Sidebar -->
  <% var currentPage = 'bulletin'; %>
  <%- include('../partials/eleve-sidebar') %>
<div class="dashboard-main">

  <h2 class="mb-4">Mon Bulletin</h2>

  <% if (bulletins.length === 0) { %>
    <p>Aucun bulletin disponible pour le moment.</p>
  <% } else { %>
    <% bulletins.forEach(bulletin => { %>
      <div class="card mb-4" id="bulletin-<%= bulletin.id %>">
        <div class="card-header d-flex justify-content-between align-items-center">
          <strong>Periode : <%= bulletin.periode %></strong>
          <button class="btn btn-sm btn-outline-primary" onclick="imprimerBulletin('bulletin-<%= bulletin.id %>')">🖨️ Imprimer</button>
        </div>
        <div class="card-body">
          <p><strong>Moyenne générale :</strong> <%= bulletin.moyenne_generale %></p>
          <p><strong>Appréciation :</strong> <%= bulletin.appreciation %></p>
          <p><strong>Date de création :</strong> <%= bulletin.date_creation.toLocaleDateString() %></p>
        </div>
      </div>
    <% }); %>
  <% } %>
 </div>
  <script>
    function imprimerBulletin(id) {
      const section = document.getElementById(id).innerHTML;
      const fenetre = window.open('', '_blank');
      fenetre.document.write('<html><head><title>Impression Bulletin</title>');
      fenetre.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">');
      fenetre.document.write('</head><body>');
      fenetre.document.write(section);
      fenetre.document.write('</body></html>');
      fenetre.document.close();
      fenetre.print();
    }
  </script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
