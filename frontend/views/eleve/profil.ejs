<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mon Profil - Élève</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
    }

    .topbar {
      width: 100%;
      height: 60px;
      background-color: white;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 40px;
      margin-bottom: 50px;
      position: sticky;
      top: 0;
      z-index: 1000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .eleve-circle {
      background-color: #ffc107;
      color: #212529;
      font-weight: bold;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sidebar {
      width: 250px;
      background-color: #fff;
      border-right: 1px solid #dee2e6;
      padding-top: 20px;
      position: fixed;
      top: 60px;
      left: 0;
      bottom: 0;
      margin-top: 30px;
      box-shadow: 4px 0 12px rgba(0, 0, 0, 0.3);
    }

    .sidebar a {
      color: #333;
      text-decoration: none;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      transition: background 0.2s ease-in-out;
    }

    .sidebar a i {
      margin-right: 10px;
    }

    .sidebar a:hover, .sidebar a.active {
      background-color: #ffc107;
      color: #212529;
    }

    .main-content {
      margin-left: 250px;
      margin-top: 60px;
      padding: 20px;
    }

    .profile-card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      padding: 30px;
      margin-bottom: 30px;
    }

    .profile-avatar {
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, #ffc107, #fd7e14);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #212529;
      font-size: 2.5rem;
      font-weight: bold;
      margin: 0 auto 20px;
    }

    .password-strength {
      height: 5px;
      border-radius: 3px;
      margin-top: 5px;
      transition: all 0.3s ease;
    }

    .strength-weak { background-color: #dc3545; }
    .strength-medium { background-color: #ffc107; }
    .strength-strong { background-color: #198754; }

    .alert-custom {
      border-radius: 10px;
      border: none;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body>
  <!-- Topbar -->
  <div class="topbar">
    <div class="d-flex align-items-center">
      <i class="bi bi-mortarboard-fill fs-3 me-2 text-primary"></i>
      <span class="fs-5 fw-bold">NS School Manager</span>
    </div>
    <div class="d-flex align-items-center gap-4">
      <div class="position-relative">
        <i class="bi bi-bell fs-4"></i>
        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">1</span>
      </div>
      <div class="eleve-circle">EL</div>
      <div class="dropdown">
        <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle" id="profileDropdown" data-bs-toggle="dropdown">
          <i class="bi bi-person-circle fs-4"></i>
        </a>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdown">
          <li><a class="dropdown-item" href="/eleve/profil"><i class="bi bi-person me-2"></i>Mon Profil</a></li>
          <li><hr class="dropdown-divider"></li>
          <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</a></li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Sidebar -->
  <div class="sidebar">
    <ul class="nav flex-column">
      <li><a href="/dashboard-eleve"><i class="bi bi-speedometer2"></i> Tableau de bord</a></li>
      <li><a href="/eleve/cours"><i class="bi bi-book"></i> Mes Cours</a></li>
      <li><a href="/eleve/notes"><i class="bi bi-clipboard-data"></i> Mes Notes</a></li>
      <li><a href="/eleve/absences"><i class="bi bi-calendar-x"></i> Mes Absences</a></li>
      <li><a href="/eleve/devoirs"><i class="bi bi-journal-text"></i> Devoirs</a></li>
      <li><a href="/eleve/emploi"><i class="bi bi-calendar3"></i> Emploi du temps</a></li>
      <li><a href="/eleve/bulletin"><i class="bi bi-file-earmark-text"></i> Bulletin</a></li>
      <li><a href="/eleve/annonces"><i class="bi bi-megaphone"></i> Annonces</a></li>
      <li><a href="/eleve/profil" class="active"><i class="bi bi-person-circle"></i> Mon Profil</a></li>
      <li><a href="/logout"><i class="bi bi-box-arrow-right"></i> Déconnexion</a></li>
    </ul>
  </div>

  <div class="main-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h2><i class="bi bi-person-circle me-2"></i>Mon Profil</h2>
    </div>

    <!-- Alert Messages -->
    <div id="alertContainer"></div>

    <!-- Profile Information Card -->
    <div class="profile-card">
      <div class="row">
        <div class="col-md-4 text-center">
          <div class="profile-avatar">
            <%= profile.nom ? profile.nom.charAt(0).toUpperCase() : 'E' %><%= profile.prenom ? profile.prenom.charAt(0).toUpperCase() : 'L' %>
          </div>
          <h4><%= profile.nom || 'Élève' %> <%= profile.prenom || 'User' %></h4>
          <p class="text-muted">Élève</p>
          <span class="badge bg-warning text-dark">Matricule: <%= profile.matricule %></span>
          <% if (profile.nom_classe) { %>
            <br><span class="badge bg-primary mt-2">Classe: <%= profile.nom_classe %></span>
          <% } %>
        </div>
        <div class="col-md-8">
          <h5 class="mb-3"><i class="bi bi-person-lines-fill me-2"></i>Informations personnelles</h5>
          <form id="profileForm">
            <div class="row">
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="nom" name="nom" value="<%= profile.nom || '' %>" required>
                  <label for="nom"><i class="bi bi-person me-2"></i>Nom</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="text" class="form-control" id="prenom" name="prenom" value="<%= profile.prenom || '' %>" required>
                  <label for="prenom"><i class="bi bi-person me-2"></i>Prénom</label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <select class="form-select" id="sexe" name="sexe">
                    <option value="">Sélectionner</option>
                    <option value="homme" <%= profile.sexe === 'homme' ? 'selected' : '' %>>Homme</option>
                    <option value="femme" <%= profile.sexe === 'femme' ? 'selected' : '' %>>Femme</option>
                  </select>
                  <label for="sexe"><i class="bi bi-gender-ambiguous me-2"></i>Sexe</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-floating mb-3">
                  <input type="date" class="form-control" id="date_naissance" name="date_naissance" 
                         value="<%= profile.date_naissance ? new Date(profile.date_naissance).toISOString().split('T')[0] : '' %>">
                  <label for="date_naissance"><i class="bi bi-calendar me-2"></i>Date de naissance</label>
                </div>
              </div>
            </div>
            <div class="form-floating mb-3">
              <input type="text" class="form-control" id="nom_classe" name="nom_classe" value="<%= profile.nom_classe || '' %>" readonly>
              <label for="nom_classe"><i class="bi bi-building me-2"></i>Classe (non modifiable)</label>
            </div>
            <button type="submit" class="btn btn-warning">
              <i class="bi bi-check-circle me-2"></i>Mettre à jour le profil
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Password Change Card -->
    <div class="profile-card">
      <h5 class="mb-3"><i class="bi bi-shield-lock me-2"></i>Changer le mot de passe</h5>
      <form id="passwordForm">
        <div class="row">
          <div class="col-md-4">
            <div class="form-floating mb-3">
              <input type="password" class="form-control" id="currentPassword" name="currentPassword" required>
              <label for="currentPassword"><i class="bi bi-lock me-2"></i>Mot de passe actuel</label>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating mb-3">
              <input type="password" class="form-control" id="newPassword" name="newPassword" required>
              <label for="newPassword"><i class="bi bi-lock-fill me-2"></i>Nouveau mot de passe</label>
              <div class="password-strength" id="passwordStrength"></div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-floating mb-3">
              <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
              <label for="confirmPassword"><i class="bi bi-lock-fill me-2"></i>Confirmer le mot de passe</label>
            </div>
          </div>
        </div>
        <button type="submit" class="btn btn-danger">
          <i class="bi bi-shield-check me-2"></i>Changer le mot de passe
        </button>
      </form>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
  <script>
    // Profile form submission
    $('#profileForm').on('submit', function(e) {
      e.preventDefault();
      
      const formData = {
        nom: $('#nom').val(),
        prenom: $('#prenom').val(),
        sexe: $('#sexe').val(),
        date_naissance: $('#date_naissance').val()
      };

      $.ajax({
        url: '/profile',
        method: 'PUT',
        data: formData,
        success: function(response) {
          showAlert('success', response.message);
          // Update avatar initials
          const initials = (formData.nom.charAt(0) + formData.prenom.charAt(0)).toUpperCase();
          $('.profile-avatar').text(initials);
        },
        error: function(xhr) {
          const response = xhr.responseJSON;
          showAlert('danger', response.message || 'Erreur lors de la mise à jour');
        }
      });
    });

    // Password form submission
    $('#passwordForm').on('submit', function(e) {
      e.preventDefault();
      
      const currentPassword = $('#currentPassword').val();
      const newPassword = $('#newPassword').val();
      const confirmPassword = $('#confirmPassword').val();

      if (newPassword !== confirmPassword) {
        showAlert('danger', 'Les nouveaux mots de passe ne correspondent pas');
        return;
      }

      $.ajax({
        url: '/change-password',
        method: 'POST',
        data: {
          currentPassword: currentPassword,
          newPassword: newPassword,
          confirmPassword: confirmPassword
        },
        success: function(response) {
          showAlert('success', response.message);
          $('#passwordForm')[0].reset();
          $('#passwordStrength').removeClass('strength-weak strength-medium strength-strong');
        },
        error: function(xhr) {
          const response = xhr.responseJSON;
          showAlert('danger', response.message || 'Erreur lors du changement de mot de passe');
        }
      });
    });

    // Password strength indicator
    $('#newPassword').on('input', function() {
      const password = $(this).val();
      const strength = calculatePasswordStrength(password);
      const strengthBar = $('#passwordStrength');
      
      strengthBar.removeClass('strength-weak strength-medium strength-strong');
      
      if (password.length > 0) {
        if (strength < 3) {
          strengthBar.addClass('strength-weak');
        } else if (strength < 5) {
          strengthBar.addClass('strength-medium');
        } else {
          strengthBar.addClass('strength-strong');
        }
      }
    });

    function calculatePasswordStrength(password) {
      let strength = 0;
      if (password.length >= 8) strength++;
      if (/[a-z]/.test(password)) strength++;
      if (/[A-Z]/.test(password)) strength++;
      if (/[0-9]/.test(password)) strength++;
      if (/[^A-Za-z0-9]/.test(password)) strength++;
      return strength;
    }

    function showAlert(type, message) {
      const alertHtml = `
        <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
          <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
          ${message}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      `;
      $('#alertContainer').html(alertHtml);
      
      // Auto-dismiss after 5 seconds
      setTimeout(() => {
        $('.alert').alert('close');
      }, 5000);
    }
  </script>
</body>
</html>
