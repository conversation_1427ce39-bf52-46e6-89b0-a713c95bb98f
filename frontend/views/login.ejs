<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>NS School Manager</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <style>
    body {
      background: linear-gradient(135deg, #0313ee, #eb7526);
      font-family: 'Arial', sans-serif;
      margin: 0;
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
    }

    .bubbles {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 0;
      overflow: hidden;
    }

    .bubble {
      position: absolute;
      bottom: -100px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.8);
      animation: rise 20s linear infinite;
    }

    @keyframes rise {
      0% { transform: translateY(0) scale(1); opacity: 0.8; }
      100% { transform: translateY(-100vh) scale(0.5); opacity: 0; }
    }

    .container-connexion {
      position: relative;
      background: rgba(255, 255, 255, 0.95);
      border: 3px solid #fff;
      border-radius: 15px;
      padding: 20px;
      max-width: 420px;
      width: 90%;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      z-index: 1;
    }

    .header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 15px;
    }

    .header h1 {
      font-size: 1.6rem;
      margin-top: 8px;
      color: #000;
    }

    .logo {
      font-size: 2.4rem;
      color: #007bff;
    }

    #message {
      margin-bottom: 10px;
    }

    .btn-retour {
      margin-top: 10px;
    }

    .form-control {
      font-size: 0.9rem;
    }

    .options-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: -10px;
      margin-bottom: 15px;
    }

    .forgot-password {
      color: #007bff;
      text-decoration: none;
      font-size: 0.85rem;
    }

    .forgot-password:hover {
      text-decoration: underline;
    }

    .profils {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      margin: 15px 0;
    }

    .profil {
      background: white;
      border-radius: 10px;
      padding: 8px;
      flex: 1;
      text-align: center;
      cursor: pointer;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 2px solid #ddd;
    }

    .profil:hover {
      transform: scale(1.08);
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    }

    .profil i {
      font-size: 1.4rem;
      color: #007bff;
    }

    .profil span {
      display: block;
      margin-top: 4px;
      font-weight: bold;
      font-size: 0.85rem;
    }

    .profil.selected {
      border: 2px solid #007bff;
      background: #e6f0ff;
    }

    .section-title {
      text-align: center;
      margin: 15px 0 8px;
      font-size: 11px;
      position: relative;
    }

    .section-title::before,
    .section-title::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 40%;
      height: 1px;
      background-color: #ccc;
    }

    .section-title::before {
      left: 0;
    }

    .section-title::after {
      right: 0;
    }
  </style>
</head>
<body>
  <div class="bubbles" id="bubbles"></div>

  <div class="container-connexion">
    <div class="header">
      <i class="fas fa-user-graduate logo"></i>
      <h1>NS School Manager</h1>
    </div>

    <div id="message"></div>

    <form id="loginForm" method="POST" action="/api/auth/login">
      <div class="mb-2">
        <label for="identifiant" class="form-label">Matricule</label>
        <input type="text" class="form-control" id="identifiant" name="matricule" placeholder="Entrez votre matricule" required />
      </div>

      <div class="mb-2">
        <label for="motdepasse" class="form-label">Mot de passe</label> 
        <input type="password" class="form-control" id="motdepasse" name="password" placeholder="Mot de passe" required />
      </div>

      <div class="d-flex justify-content-between align-items-center mb-2">
        <div class="form-check">
          <input class="form-check-input" type="checkbox" id="remember" name="remember">
          <label class="form-check-label" for="remember">Se souvenir de moi</label>
        </div>
        <a href="#" class="forgot-password" id="motDePasseOublie">Mot de passe oublié ?</a>
      </div>

      <div id="alertMotDePasse" class="alert alert-info d-none" role="alert">
        Pour réinitialiser votre mot de passe, veuillez contacter l’administrateur de NS School.
      </div>

      <div class="section-title">Choisir un profil</div>

      <div class="profils">
        <div class="profil" data-role="eleve">
          <i class="fas fa-users"></i>
          <span>Élève</span>
        </div>
         <div class="profil" data-role="professeur">
          <i class="fas fa-chalkboard-teacher"></i>
          <span>Professeur</span>
        </div>
      </div>

      <input type="hidden" name="role" id="role" value="" />

      <div class="d-grid">
        <button type="submit" class="btn btn-primary btn-block">Se connecter</button>
      </div>
    </form>
  </div>

  <script>
    // Création des bulles
    const bubblesContainer = document.getElementById('bubbles');
    function createBubble() {
      const size = Math.random() * 40 + 50;
      const posX = Math.random() * 100;
      const delay = Math.random() * 20;
      const duration = Math.random() * 20 + 10;

      const bubble = document.createElement('div');
      bubble.className = 'bubble';
      bubble.style.width = size + 'px';
      bubble.style.height = size + 'px';
      bubble.style.left = posX + '%';
      bubble.style.animationDelay = delay + 's';
      bubble.style.animationDuration = duration + 's';

      bubblesContainer.appendChild(bubble);
      setTimeout(() => { if (bubble.parentNode) bubble.remove(); }, (duration + 1) * 1000);
    }
    setInterval(createBubble, 300);

    // Sélection du profil
    const profils = document.querySelectorAll('.profil');
    const roleInput = document.getElementById('role');

    profils.forEach(profil => {
      profil.addEventListener('click', () => {
        profils.forEach(p => p.classList.remove('selected'));
        profil.classList.add('selected');
        roleInput.value = profil.dataset.role;
      });
    });

    // Formulaire de connexion
    const form = document.getElementById('loginForm');
    const messageDiv = document.getElementById('message');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const matricule = document.getElementById('identifiant').value.trim();
      const password = document.getElementById('motdepasse').value.trim();
      const role = document.getElementById('role').value;
      const remember = document.getElementById('remember')?.checked;

      if (!matricule || !password || !role) {
        afficherMessage("Veuillez remplir tous les champs et choisir un profil.", "danger");
        return;
      }

      try {
        const res = await fetch('/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ matricule, password, role, remember: remember ? 'on' : 'off' })
        });

        const data = await res.json();

        if (res.ok) {
          afficherMessage(data.message, "success");
          setTimeout(() => {
            window.location.href = data.redirect;
          }, 2000);
        } else {
          afficherMessage(data.message, "danger");
        }
      } catch (err) {
        afficherMessage("Erreur serveur. Veuillez réessayer.", "danger");
      }
    });

    function afficherMessage(message, type) {
      messageDiv.innerHTML = `<div class="alert alert-${type}" role="alert">${message}</div>`;
      setTimeout(() => {
        messageDiv.innerHTML = '';
      }, 5000);
    }

    // Mot de passe oublié
    document.getElementById('motDePasseOublie').addEventListener('click', function(e) {
      e.preventDefault();
      const alertBox = document.getElementById('alertMotDePasse');

      alertBox.classList.remove('d-none');
      setTimeout(() => {
        alertBox.classList.add('d-none');
      }, 6000);
    });
  </script>
</body>
</html>
